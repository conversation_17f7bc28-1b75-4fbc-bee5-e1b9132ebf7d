using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using Newtonsoft.Json;
using System.Linq;
using System.Threading;
using System.Collections.Generic;
using System.Net;
using Coplay.Controllers.Systems;
using System.IO;
using Coplay.Common.CoplayApi.Models;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using Coplay.Models.Assistants;
using System.IO.Abstractions;
using System.Reactive.Disposables;
using Coplay.Common.Exceptions;
using Coplay.Models.Generation;
using Coplay.Models.ThreadReview;
using Coplay.Services.LogCollection;
using UnityEditor;

namespace Coplay.Common.CoplayApi
{
    public class CoplayApiClient : ICoplayApiClient
    {
        private const int SubmitLogsMaxRetries = 3;
        private static readonly TimeSpan RequestTimeout = TimeSpan.FromSeconds(10);

        private readonly HttpClient _client;
        private readonly SemaphoreSlim _refreshTokenSemaphore = new SemaphoreSlim(1, 1);
        private readonly CancellationTokenSource _disposalCts = new CancellationTokenSource();
        private readonly SettingsController _settingsController;
        private readonly IFileSystem _fileSystem;
        private readonly CompositeDisposable _disposables = new();
        private bool _disposed = false;

        private readonly JsonSerializerSettings _jsonSerializerSettings = new()
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new SnakeCaseNamingStrategy(),
            },
            Converters = new List<JsonConverter>
            {
                new StringEnumConverter(new SnakeCaseNamingStrategy()),
            }
        };

        public CoplayApiClient(SettingsController settingsController, IFileSystem fileSystem)
        {
            _settingsController = settingsController;
            _fileSystem = fileSystem;

            HttpClientHandler handler = new()
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
            };
            _client = new HttpClient(handler);
            Utils.LoadEnvironmentVariables(_fileSystem);
            _client.BaseAddress = new Uri(Constants.CoplayApiUrl);

            UpdateUserAgentHeader("0.0.0");

            UpdateDeviceIdHeader(_settingsController.DeviceId);
            UpdateAuthHeader(_settingsController.AccessToken);

            _settingsController.AccessTokenObservable.Subscribe(UpdateAuthHeader).AddTo(_disposables);
            _settingsController.DeviceIdObservable.Subscribe(UpdateDeviceIdHeader).AddTo(_disposables);

            InitAsync();
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                _disposables.Dispose();

                // Cancel all pending operations
                if (!_disposalCts.IsCancellationRequested)
                {
                    _disposalCts.Cancel();
                }

                _client.DefaultRequestHeaders.Clear();
                _client.Dispose();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error during CoplayApiClient disposal: {ex.Message}");
            }
        }

        private void InitAsync()
        {
            EditorApplication.delayCall += async () => {
                var version = await GetLocalPackageVersion(_disposalCts.Token);
                UpdateUserAgentHeader(version);
            };
        }

        // Helper method to create a linked token that combines the user token and the disposal token
        private CancellationTokenSource CreateLinkedToken(CancellationToken userToken)
        {
            return CancellationTokenSource.CreateLinkedTokenSource(userToken, _disposalCts.Token);
        }

        private void UpdateAuthHeader(string accessToken)
        {
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                "Bearer",
                accessToken
            );
        }

        private void UpdateDeviceIdHeader(string deviceId)
        {
            _client.DefaultRequestHeaders.Remove("x-device-id");
            _client.DefaultRequestHeaders.Add("x-device-id", deviceId);
        }

        private void UpdateUserAgentHeader(string version)
        {
            _client.DefaultRequestHeaders.Remove("User-Agent");
            _client.DefaultRequestHeaders.Add("User-Agent", $"Coplay/{version}");
        }

        private async Task<HttpResponseMessage> GetAsync(string endpoint, CancellationToken cancellationToken = default)
        {
            var requestTime = DateTime.Now;
            var response = await _client.GetAsync(endpoint, cancellationToken);
            var responseTime = DateTime.Now;
            var responseTimeSpan = responseTime - requestTime;
            // Debug.Log($"GetAsync {endpoint} took {responseTimeSpan.TotalMilliseconds}ms");
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                if (await RefreshAccessToken(requestTime, cancellationToken))
                {
                    response = await _client.GetAsync(endpoint, cancellationToken);
                }
            }
            return response;
        }

        private async Task<HttpResponseMessage> PostAsync(string endpoint, HttpContent content, CancellationToken cancellationToken = default)
        {
            var requestTime = DateTime.Now;
            var response = await _client.PostAsync(endpoint, content, cancellationToken);
            var responseTime = DateTime.Now;
            var responseTimeSpan = responseTime - requestTime;
            // Debug.Log($"PostAsync {endpoint} took {responseTimeSpan.TotalMilliseconds}ms"); // This should not be committed to the repo uncommented because it causes a lot of spam.
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                if (await RefreshAccessToken(requestTime, cancellationToken))
                {
                    response = await _client.PostAsync(endpoint, content, cancellationToken);
                }
            }
            else if (response.StatusCode == HttpStatusCode.UnprocessableEntity)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                CoplayLogger.LogError("Unprocessable entity", new Exception(errorMessage));
                throw new HttpRequestException($"Unprocessable entity: {errorMessage}");
            }
            return response;
        }

        private async Task<HttpResponseMessage> DeleteAsync(string endpoint, CancellationToken cancellationToken = default)
        {
            var requestTime = DateTime.Now;
            var response = await _client.DeleteAsync(endpoint, cancellationToken);
            var responseTime = DateTime.Now;
            var responseTimeSpan = responseTime - requestTime;
            // Debug.Log($"DeleteAsync {endpoint} took {responseTimeSpan.TotalMilliseconds}ms"); // This should not be committed to the repo uncommented because it causes a lot of spam.
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                if (await RefreshAccessToken(requestTime, cancellationToken))
                {
                    response = await _client.DeleteAsync(endpoint, cancellationToken);
                }
            }
            else if (response.StatusCode == HttpStatusCode.UnprocessableEntity)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                CoplayLogger.LogError("Unprocessable entity", new Exception(errorMessage));
            }
            return response;
        }

        private async Task<HttpResponseMessage> PostStreamAsync(string endpoint, HttpContent content, CancellationToken cancellationToken = default)
        {
            // TODO: we can refactor this image-gen process to be the same as our text streaming instead
            var requestTime = DateTime.Now;
            // Use SendAsync with HttpCompletionOption.ResponseHeadersRead to enable streaming
            var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
            {
                Content = content
            };

            var response = await _client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            var responseTime = DateTime.Now;
            var responseTimeSpan = responseTime - requestTime;
            // Debug.Log($"PostStreamAsync {endpoint} took {responseTimeSpan.TotalMilliseconds}ms");
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                if (await RefreshAccessToken(requestTime, cancellationToken))
                {
                    // Create a new request for retry since the original one was consumed
                    var retryRequest = new HttpRequestMessage(HttpMethod.Post, endpoint)
                    {
                        Content = content
                    };
                    response = await _client.SendAsync(retryRequest, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
                }
            }
            else if (response.StatusCode == HttpStatusCode.UnprocessableEntity)
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                CoplayLogger.LogError("Unprocessable entity", new Exception(errorMessage));
            }
            return response;
        }

        private async Task<string> GetLocalPackageVersion(CancellationToken cancellationToken = default)
        {
            try
            {
                var request = UnityEditor.PackageManager.Client.List(true);
                while (!request.IsCompleted)
                {
                    await Task.Delay(100, cancellationToken);
                }

                var package = request.Result.FirstOrDefault(p => p.name == "com.coplaydev.coplay");
                if (package != null)
                {
                    return package.version;
                }

                CoplayLogger.LogWarning("Coplay package not found, assume dev version");
                return "0.0.0";
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get local package version", ex);
                return "0.0.0";
            }
        }

        public async Task<VersionResponse> CheckForUpdates(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                string localVersion = await GetLocalPackageVersion(linkedToken.Token);
                if (localVersion == "0.0.0")
                {
                    return null;
                }

                var content = new StringContent(JsonConvert.SerializeObject(new { version = localVersion }, _jsonSerializerSettings), Encoding.UTF8, "application/json");
                var response = await PostAsync("/version", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<VersionResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to check for updates", ex);
                return null;
            }
        }

        public async Task<string> GetDeviceId(CancellationToken cancellationToken = default)
        {
            using var linkedToken = CreateLinkedToken(cancellationToken);
            var response = await PostAsync("/auth/device", null, linkedToken.Token);
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var deviceId = JsonConvert.DeserializeObject<DeviceIdResponse>(responseContent, _jsonSerializerSettings);
            _settingsController.DeviceId = deviceId.DeviceId;
            CoplayLogger.Log($"Successfully got device ID: {deviceId.DeviceId}");
            return deviceId.DeviceId;
        }

        public async Task<bool> CheckForTokens(CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            var timeout = TimeSpan.FromMinutes(2);

            while (DateTime.UtcNow - startTime < timeout)
            {
                try
                {
                    using var linkedToken = CreateLinkedToken(cancellationToken);
                    if (string.IsNullOrEmpty(_settingsController.DeviceId))
                    {
                        break;
                    }
                    var response = await GetAsync($"/auth/device/{_settingsController.DeviceId}", linkedToken.Token);
                    if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        await Task.Delay(2000, linkedToken.Token); // Sleep for 2 seconds
                        continue;
                    }
                    response.EnsureSuccessStatusCode();
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokens = JsonConvert.DeserializeObject<TokensResponse>(responseContent, _jsonSerializerSettings);
                    if (tokens.AccessToken != null && tokens.RefreshToken != null)
                    {
                        _settingsController.SetTokens(tokens.AccessToken, tokens.RefreshToken);
                        CoplayLogger.Log("Successfully logged in");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    CoplayLogger.LogError("Login failed", ex);
                    if (ex is OperationCanceledException)
                    {
                        throw;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            return false;
        }

        public async Task<bool> RefreshAccessToken(DateTime requestTime, CancellationToken cancellationToken = default)
        {
            // Take a lock to prevent concurrent refreshes:
            // * If another request is already refreshing the token, wait for it to finish (for 30s) and the change will be detected in the initial if statement
            // * If not request is refreshing the token, the lock will be obtained immediately and the refresh will proceed
            if (!await _refreshTokenSemaphore.WaitAsync(30000, cancellationToken))
            {
                CoplayLogger.LogError("Timeout waiting to refresh access token");
                return false;
            }
            try
            {
                // If the token was refreshed since the last request, return true assuming refresh was successful
                if (_settingsController.LastTokenRefresh > requestTime)
                {
                    return true;
                }

                // If the refresh token is not found, set the tokens to null and return false to force a login
                if (string.IsNullOrEmpty(_settingsController.RefreshToken))
                {
                    CoplayLogger.LogWarning("No refresh token found");
                    _settingsController.SetTokens(null, null);
                    return false;
                }

                // Refresh the access token
                var refreshRequest = new { refreshToken = _settingsController.RefreshToken };
                var json = JsonConvert.SerializeObject(refreshRequest, _jsonSerializerSettings);
                using var content = new StringContent(json, Encoding.UTF8, "application/json");
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await _client.PostAsync($"/auth/device/{_settingsController.DeviceId}/refresh", content, linkedToken.Token);
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    CoplayLogger.LogError("Failed to refresh access token");
                    _settingsController.SetTokens(null, null);
                    return false;
                }
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var tokens = JsonConvert.DeserializeObject<TokensResponse>(responseContent, _jsonSerializerSettings);
                _settingsController.SetTokens(tokens.AccessToken, tokens.RefreshToken);
                CoplayLogger.Log("Successfully refreshed access token");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to refresh access token", ex);
                return false;
            }
            finally
            {
                _refreshTokenSemaphore.Release();
            }
        }

        /// <summary>
        /// Submits a log to the Coplay API
        /// We retry on network errors 3 times
        /// At the moment this is most likely to occur when sending large prompts with a slower internet speed
        /// </summary>
        /// <param name="severity"></param>
        /// <param name="message"></param>
        /// <param name="threadId">Optional thread ID for the log entry</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns></returns>
        public async Task SubmitLog(string severity, string message, string threadId = null, CancellationToken cancellationToken = default)
        {
            for (int attempt = 0; attempt <= SubmitLogsMaxRetries && !_disposed; attempt++)
            {
                try
                {
                    var logRequest = new LogRequest
                    {
                        Timestamp = DateTime.UtcNow.ToString("o"),
                        Severity = severity,
                        Message = message,
                        UnityVersion = Application.unityVersion,
                        ThreadId = threadId
                    };

                    var json = JsonConvert.SerializeObject(logRequest, _jsonSerializerSettings);
                    using var content = new StringContent(json, Encoding.UTF8, "application/json");
                    using var timeoutCts = new CancellationTokenSource(RequestTimeout);
                    using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken, _disposalCts.Token);
                    try
                    {
                        var response = await PostAsync("/log", content, linkedCts.Token);
                        response.EnsureSuccessStatusCode();
                        return; // Success, exit retry loop
                    }
                    catch (OperationCanceledException)
                    {
                        if (_disposalCts.IsCancellationRequested)
                            return;

                        // If cancelled due to timeout, throw HttpRequestException to trigger retry
                        throw new HttpRequestException("Request timed out");
                    }
                }
                catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
                {
                    if (_disposalCts.IsCancellationRequested)
                        return;

                    if (attempt == SubmitLogsMaxRetries)
                    {
#if DEBUG
                        Debug.LogException(ex);
                        Debug.LogError($"Failed to submit log to Coplay API after {SubmitLogsMaxRetries + 1} attempts");
#endif
                        return;
                    }

                    // Exponential backoff with jitter ref: https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/
                    var delayMs = Math.Min(100 * Math.Pow(2, attempt), 1000) + new System.Random().Next(100);
                    await Task.Delay((int)delayMs, cancellationToken);
                }
                catch (Exception ex)
                {
#if DEBUG
                    Debug.LogError($"Unexpected error while submitting log to Coplay API: {ex}");
                    Debug.LogException(ex);
#endif
                    return;
                }
            }
        }

        public async Task<string> GetContextHash(string contextId, CancellationToken cancellationToken = default)
        {
            try
            {
                var encodedContextId = Uri.EscapeDataString(contextId);
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/context/{encodedContextId}", linkedToken.Token);
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    CoplayLogger.Log($"Context {contextId} not found");
                    return null;
                }
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get context", ex);
                throw;
            }
        }

        public async Task SyncContext(string contextId, string hash, string content, CancellationToken cancellationToken = default)
        {
            try
            {
                var encodedContent = new StringContent(
                    JsonConvert.SerializeObject(new { hash, content }, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );
                var encodedContextId = Uri.EscapeDataString(contextId);
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync($"/context/{encodedContextId}", encodedContent, linkedToken.Token);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to sync context: {ex.Message}");
            }
        }

        public async Task<PromptResponse> StreamSendMessageToLLM(PromptRequest promptRequest, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(JsonConvert.SerializeObject(promptRequest, _jsonSerializerSettings), Encoding.UTF8, "application/json");
            var response = await PostAsync("/assistant/stream", content, cancellationToken);
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<PromptResponse>(responseContent, _jsonSerializerSettings);
        }

        public async Task<PromptResponse> GetStreamedMessage(string threadId, int lastUpdateId = 0, CancellationToken cancellationToken = default)
        {
            var response = await GetAsync($"/assistant/stream/{threadId}?last_update_id={lastUpdateId}", cancellationToken);
            if (response.StatusCode == HttpStatusCode.NotFound)
                return null;

            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<PromptResponse>(responseContent, _jsonSerializerSettings);
        }

        public async Task CancelStream(string threadId, CancellationToken cancellationToken = default)
        {
            using var linkedToken = CreateLinkedToken(cancellationToken);
            await PostAsync($"/assistant/stream/{threadId}/cancel", null, linkedToken.Token);
        }

        public async Task<List<CoplayThread>> GetThreads(CancellationToken cancellationToken = default)
        {
            var response = await GetAsync("/assistant/threads", cancellationToken);
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<CoplayThread>>(responseContent, _jsonSerializerSettings);
        }

        public async Task<List<PromptResponse>> GetThreadMessages(string threadId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/assistant/threads/{threadId}", linkedToken.Token);
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    throw new ThreadNotFoundException(threadId);
                }
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<List<PromptResponse>>(responseContent, _jsonSerializerSettings);
                return result;
            }
            catch (ThreadNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get thread messages for thread ID: {threadId}", ex);
                return null;
            }
        }

        public async Task<CoplayThread> CreateAssistant(AssistantMode assistantMode, CancellationToken cancellationToken = default)
        {
            // TODO: refactor this. we probably don't need the createassistant function at all.
            var content = new StringContent(
                JsonConvert.SerializeObject(new { assistantMode, name = "New chat " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") }, _jsonSerializerSettings),
                Encoding.UTF8,
                "application/json"
            );
            using var linkedToken = CreateLinkedToken(cancellationToken);
            var response = await PostAsync($"/assistant", content, linkedToken.Token);
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var threadResponse = JsonConvert.DeserializeObject<CoplayThread>(responseContent, _jsonSerializerSettings);
            return threadResponse;
        }

        public async Task<CoplayThread> UpdateThread(string threadId, string name, AssistantMode assistantMode, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(JsonConvert.SerializeObject(new { name, assistantMode }, _jsonSerializerSettings), Encoding.UTF8, "application/json");
            var response = await PostAsync($"/assistant/threads/{threadId}", content, cancellationToken);
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<CoplayThread>(responseContent, _jsonSerializerSettings);
        }

        public async Task<bool> DeleteThread(string threadId, CancellationToken cancellationToken = default)
        {
            var response = await DeleteAsync($"/assistant/threads/{threadId}", cancellationToken);
            // We return true for 404s because we want the thread to be removed from local state
            // This is only likely to happen to devs who are testing locally
            if (response.StatusCode == HttpStatusCode.NotFound)
                return true;

            response.EnsureSuccessStatusCode();
            return true;
        }

        public async Task<bool> UploadFbxFile(string localFilePath, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrEmpty(localFilePath))
                {
                    CoplayLogger.LogError("File path is null or empty");
                    return false;
                }

                if (!_fileSystem.File.Exists(localFilePath))
                {
                    CoplayLogger.LogError($"File does not exist at path: {localFilePath}");
                    return false;
                }

                string fullPath = Path.GetFullPath(localFilePath);
                int index = fullPath.IndexOf("Assets", StringComparison.InvariantCulture);
                string relativePath = index >= 0 ? fullPath.Substring(index + "Assets".Length + 1) : Path.GetFileName(localFilePath);

                using (var content = new MultipartFormDataContent())
                {
                    byte[] fileBytes;
                    try
                    {
                        fileBytes = await _fileSystem.File.ReadAllBytesAsync(fullPath, cancellationToken).ConfigureAwait(false);
                    }
                    catch (IOException ex)
                    {
                        CoplayLogger.LogError($"Failed to read file: {fullPath}", ex);
                        return false;
                    }

                    var fileContent = new ByteArrayContent(fileBytes);
                    fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                    content.Add(fileContent, "file", Path.GetFileName(localFilePath));
                    content.Add(new StringContent(relativePath), "relative_path");

                    try
                    {
                        using var timeoutCts = new CancellationTokenSource(RequestTimeout);
                        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken, _disposalCts.Token);
                        var response = await PostAsync("/upload-fbx", content, linkedCts.Token);
                        response.EnsureSuccessStatusCode();
                        return response.IsSuccessStatusCode;
                    }
                    catch (OperationCanceledException)
                    {
                        CoplayLogger.LogError($"Upload request timed out for file: {localFilePath}");
                        return false;
                    }
                    catch (HttpRequestException ex)
                    {
                        CoplayLogger.LogError($"HTTP request failed while uploading file: {localFilePath}", ex);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Unexpected error while uploading FBX file: {localFilePath}", ex);
                return false;
            }
        }

        public async Task<SceneGenerationResponse> GenerateSceneAsync(SceneGenerationRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/scene/generate", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<SceneGenerationResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to generate scene", ex);
                throw;
            }
        }

        public async Task<SceneGenerationResponse> GetSceneGenerationStatusAsync(string jobId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/scene/status/{jobId}", linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<SceneGenerationResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get scene generation status for job ID: {jobId}", ex);
                throw;
            }
        }

        public async Task<ImageGenerationResponse> GenerateImage(string objectPrompt, string stylePrompt, string quality = "standard", string size = "1024x1024", string style = "natural", string provider = "dall-e", CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new
                {
                    object_prompt = objectPrompt,
                    style_prompt = stylePrompt,
                    quality,
                    size,
                    style
                };

                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync($"/image/generate?provider={provider}", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ImageGenerationResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to generate image", ex);
                throw;
            }
        }

        public async Task<DependencyResponse> DownloadDependencyAsync(string name, string version, string os, string architecture, CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new DependencyRequest
                {
                    Deps = new List<Dependency>
                    {
                        new Dependency(name, version, os, architecture)
                    }
                };

                var json = JsonConvert.SerializeObject(request, _jsonSerializerSettings);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/dependencies/download", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<DependencyResponse>(responseContent, _jsonSerializerSettings);
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to download dependency {name}: {ex.Message}", ex);
                return null;
            }
        }

        public async Task<AutocompleteResponse> GetAutocompleteAsync(AutocompleteRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/autocomplete", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<AutocompleteResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get autocomplete suggestions", ex);
                throw;
            }
        }

        public async Task<bool> SubmitFeedbackAsync(string runId, string key, float score, string comment = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var feedbackRequest = new FeedbackRequest
                {
                    RunId = runId,
                    Key = key,
                    Score = score,
                    Comment = comment
                };

                var content = new StringContent(
                    JsonConvert.SerializeObject(feedbackRequest, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/feedback", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                CoplayLogger.Log($"Successfully submitted feedback for run {runId}");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to submit feedback", ex);
                return false;
            }
        }

        /// <summary>
        /// Validates and saves the user's Anthropic API key
        /// </summary>
        /// <param name="apiKey">The Anthropic API key to validate and save</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if the key was validated and saved successfully, false otherwise</returns>
        public async Task<bool> ValidateAndSaveAnthropicKeyAsync(string apiKey, CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new AnthropicKeyRequest
                {
                    ApiKey = apiKey
                };

                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/auth/anthropic-key", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                CoplayLogger.Log("Successfully validated and saved Anthropic API key");
                return true;
            }
            catch (HttpRequestException ex)
            {
                CoplayLogger.LogError("HTTP request failed while validating Anthropic API key", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the remaining USD credit balance for the current user
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The detailed credit balance breakdown for the user</returns>
        public async Task<CreditBalanceResponse> GetCreditBalanceAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync("/auth/user/credits", linkedToken.Token);

                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    CoplayLogger.LogWarning("Credit balance endpoint not found");
                    return new CreditBalanceResponse { SubscriptionCredits = 0.0f, TopupCredits = 0.0f, Total = 0.0f };
                }

                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<CreditBalanceResponse>(responseContent, _jsonSerializerSettings);

                return result ?? new CreditBalanceResponse { SubscriptionCredits = 0.0f, TopupCredits = 0.0f, Total = 0.0f };
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get credit balance", ex);
                return new CreditBalanceResponse { SubscriptionCredits = 0.0f, TopupCredits = 0.0f, Total = 0.0f };
            }
        }

        public async Task<SubscriptionResponse> GetSubscriptionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync("/auth/user/subscription", linkedToken.Token);

                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    CoplayLogger.LogWarning("Subscription endpoint not found");
                    return null;
                }
                else if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    CoplayLogger.LogWarning("Unauthorized access to subscription endpoint");
                    return null;
                }

                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<SubscriptionResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get subscription", ex);
                return null;
            }
        }

        public async Task<bool> DeleteAnthropicKeyAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await _client.DeleteAsync("/auth/anthropic-key", linkedToken.Token);
                response.EnsureSuccessStatusCode();

                // Clear the key from settings
                _settingsController.UserAnthropicKey = "";

                CoplayLogger.Log("Successfully deleted Anthropic API key");
                return true;
            }
            catch (HttpRequestException ex)
            {
                CoplayLogger.LogError("HTTP request failed while deleting Anthropic API key", ex);
                return false;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error deleting Anthropic API key: {ex.Message}", ex);
                return false;
            }
        }

        public async Task<ImageFunctionsResponse> StartImageGenerationAsync(object request, CancellationToken cancellationToken = default)
        {
            try
            {
                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/image/functions/stream", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ImageFunctionsResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to start image generation", ex);
                throw;
            }
        }

        public async Task<ImageFunctionsResponse> GetImageGenerationStatusAsync(string taskId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/image/functions/stream/{taskId}", linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ImageFunctionsResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get image generation status for task {taskId}", ex);
                throw;
            }
        }

        public async Task<bool> CancelImageGenerationAsync(string taskId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync($"/image/functions/stream/{taskId}/cancel", null, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                CoplayLogger.Log($"Successfully cancelled image generation task {taskId}");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to cancel image generation task {taskId}", ex);
                return false;
            }
        }

        public async Task<FileUploadResponse> UploadFileAsync(string filePath, string threadId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    CoplayLogger.LogError("File path is null or empty");
                    return new FileUploadResponse { Success = false, Error = "File path is null or empty" };
                }

                if (!File.Exists(filePath))
                {
                    CoplayLogger.LogError($"File does not exist at path: {filePath}");
                    return new FileUploadResponse { Success = false, Error = $"File does not exist at path: {filePath}" };
                }

                if (string.IsNullOrEmpty(threadId))
                {
                    CoplayLogger.LogError("Thread ID is null or empty");
                    return new FileUploadResponse { Success = false, Error = "Thread ID is null or empty" };
                }

                CoplayLogger.Log($"Starting file upload: {filePath} for thread {threadId}");

                using (var content = new MultipartFormDataContent())
                {
                    byte[] fileBytes;
                    try
                    {
                        fileBytes = await File.ReadAllBytesAsync(filePath, cancellationToken).ConfigureAwait(false);
                    }
                    catch (IOException ex)
                    {
                        CoplayLogger.LogError($"Failed to read file: {filePath}", ex);
                        return new FileUploadResponse { Success = false, Error = $"Failed to read file: {ex.Message}" };
                    }

                    var fileContent = new ByteArrayContent(fileBytes);
                    
                    // Set content type based on file extension
                    string contentType = Path.GetExtension(filePath).ToLower() switch
                    {
                        ".glb" => "model/gltf-binary",
                        ".gltf" => "model/gltf+json",
                        ".fbx" => "application/octet-stream",
                        ".obj" => "text/plain",
                        ".ply" => "application/octet-stream",
                        ".stl" => "application/octet-stream",
                        _ => "application/octet-stream"
                    };
                    
                    fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType);
                    content.Add(fileContent, "file", Path.GetFileName(filePath));
                    content.Add(new StringContent(threadId), "thread_id");

                    try
                    {
                        using var linkedToken = CreateLinkedToken(cancellationToken);
                        var response = await PostAsync("/assets/upload", content, linkedToken.Token);
                        response.EnsureSuccessStatusCode();
                        
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var result = JsonConvert.DeserializeObject<FileUploadResponse>(responseContent, _jsonSerializerSettings);
                        
                        if (result.Success)
                        {
                            CoplayLogger.Log($"Successfully uploaded file {filePath} to {result.FilePath}");
                        }
                        else
                        {
                            CoplayLogger.LogError($"File upload failed: {result.Error}");
                        }
                        
                        return result;
                    }
                    catch (HttpRequestException ex)
                    {
                        CoplayLogger.LogError($"HTTP request failed while uploading file: {filePath}", ex);
                        return new FileUploadResponse { Success = false, Error = $"HTTP request failed: {ex.Message}" };
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Unexpected error while uploading file: {filePath}", ex);
                return new FileUploadResponse { Success = false, Error = $"Unexpected error: {ex.Message}" };
            }
        }

        public async Task<AssetGenerationJobResponse> GenerateAssetAsync(AssetGenerationRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/assets/generate", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<AssetGenerationJobResponse>(responseContent, _jsonSerializerSettings);
                
                CoplayLogger.Log($"Successfully started asset generation job {result.JobId}");
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to start asset generation", ex);
                throw;
            }
        }

        public async Task<AssetGenerationStatus> GetAssetGenerationStatusAsync(string jobId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/assets/status/{jobId}", linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<AssetGenerationStatus>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get asset generation status for job ID: {jobId}", ex);
                throw;
            }
        }

        public async Task<AssetGenerationResult> GetAssetGenerationResultAsync(string jobId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/assets/result/{jobId}", linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<AssetGenerationResult>(responseContent, _jsonSerializerSettings);
                
                CoplayLogger.Log($"Successfully retrieved asset generation result for job {jobId}");
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get asset generation result for job ID: {jobId}", ex);
                throw;
            }
        }

        public async Task<bool> CancelAssetGenerationAsync(string jobId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync($"/assets/cancel/{jobId}", null, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                CoplayLogger.Log($"Successfully cancelled asset generation job {jobId}");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to cancel asset generation job {jobId}", ex);
                return false;
            }
        }

        public async Task<TopUpResponse> TopUpCreditsAsync(TopUpRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/stripe/top-up", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<TopUpResponse>(responseContent, _jsonSerializerSettings);
                
                if (result.Success)
                {
                    CoplayLogger.Log($"Successfully topped up ${request.AmountUsd} credits");
                    // Update local credit balance if available
                    if (result.NewBalance.HasValue)
                    {
                        _settingsController.TotalCredit = result.NewBalance.Value;
                    }
                }
                else
                {
                    CoplayLogger.LogError($"Top-up failed: {result.Error}");
                }
                
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to top up credits", ex);
                return new TopUpResponse
                {
                    Success = false,
                    Error = "An unexpected error occurred. Please try again."
                };
            }
        }

        public async Task<ThreadShouldReviewResponse> ShouldReviewThreadAsync(string threadId, string customRules = null, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var requestBody = new { thread_id = threadId, custom_rules = customRules };
                var content = new StringContent(
                    JsonConvert.SerializeObject(requestBody, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );
                var response = await PostAsync("/assistant/check-and-start-thread-review", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ThreadShouldReviewResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to check if thread should be reviewed", ex);
                return new ThreadShouldReviewResponse { ShouldReview = false, ReviewId = null };
            }
        }

        public async Task<ThreadReviewResponse> GetThreadReviewByIdAsync(string reviewId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await GetAsync($"/assistant/thread-reviews/{reviewId}", linkedToken.Token);

                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    // Review not found for this device
                    return null;
                }

                response.EnsureSuccessStatusCode();
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ThreadReviewResponse>(responseContent, _jsonSerializerSettings);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get thread review by ID: {reviewId}", ex);
                return null;
            }
        }

        public async Task<ThreadReviewStatusResponse> RespondToThreadReviewAsync(string reviewId, bool accepted, CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new ThreadReviewStatusRequest
                {
                    Accepted = accepted
                };

                var content = new StringContent(
                    JsonConvert.SerializeObject(request, _jsonSerializerSettings),
                    Encoding.UTF8,
                    "application/json"
                );

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync($"/assistant/thread-reviews/{reviewId}/respond", content, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ThreadReviewStatusResponse>(responseContent, _jsonSerializerSettings);

                CoplayLogger.Log($"Successfully responded to thread review {reviewId}: {(accepted ? "accepted" : "rejected")}");
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to respond to thread review {reviewId}", ex);
                return null;
            }
        }

        public async Task UploadLogArchiveAsync(LogArchiveUploadRequest request, byte[] archiveBytes, CancellationToken cancellationToken = default)
        {
            try
            {
                CoplayLogger.Log($"Uploading log archive: {request.ArchiveFileName} ({request.ArchiveSize} bytes)");

                // Create multipart form data content
                using var formData = new MultipartFormDataContent
                {
                    // Add metadata as form fields
                    // Note: device_id and user_email are already sent in headers
                    { new StringContent(request.ArchiveFileName), "archive_file_name" },
                    { new StringContent(request.ArchiveSize.ToString()), "archive_size" },
                    { new StringContent(request.Timestamp.ToString("O")), "timestamp" },
                    { new StringContent(request.UnityVersion), "unity_version" },
                    { new StringContent(request.Platform), "platform" }
                };

                // Add the archive file
                var fileContent = new ByteArrayContent(archiveBytes);
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/zip");
                formData.Add(fileContent, "archive", request.ArchiveFileName);

                using var linkedToken = CreateLinkedToken(cancellationToken);
                var response = await PostAsync("/logs/upload-archive", formData, linkedToken.Token);
                response.EnsureSuccessStatusCode();

                CoplayLogger.Log($"Successfully uploaded log archive: {request.ArchiveFileName}");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to upload log archive: {ex.Message}", ex);
                throw;
            }
        }

    }

}
