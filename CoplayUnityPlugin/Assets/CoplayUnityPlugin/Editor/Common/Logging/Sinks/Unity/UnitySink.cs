using Serilog.Core;
using Serilog.Events;
using UnityEngine;

namespace Coplay.Common.Logging.Sinks.Unity
{
    public class UnitySink : ILogEventSink
    {
        public void Emit(LogEvent logEvent)
        {
            if (logEvent == null)
                return;

            var message = LogUtils.SanitizeForLogging(logEvent.RenderMessage());

            // Output to Unity console based on log level, similar to CoplayLogger formatting
            switch (logEvent.Level)
            {
                case LogEventLevel.Verbose:
                case LogEventLevel.Debug:
                case LogEventLevel.Information:
                    Debug.Log(message);
                    break;
                case LogEventLevel.Warning:
                    Debug.LogWarning(message);
                    break;
                case LogEventLevel.Error:
                case LogEventLevel.Fatal:
                    Debug.LogError(message);
                    if (logEvent.Exception != null)
                        Debug.LogException(logEvent.Exception);
                    break;
            }
        }
    }
}
