import sys
from pathlib import Path
import asyncio

# Add the project root to the python path to allow for absolute imports.
# This allows the script to be run directly.
# The project root is 3 levels up from the script file.
# coplay/Backend/coplay/recipes/delete_recipe_namespace.py
sys.path.append(str(Path(__file__).resolve().parents[2]))

from coplay.turbopuffer import delete_namespace

async def main():
    await delete_namespace("recipes")

if __name__ == "__main__":
    asyncio.run(main())