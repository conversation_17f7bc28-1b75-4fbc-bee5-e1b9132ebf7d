using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using UnityEditor;
using Coplay.Common;
using Coplay.Models.Generation;
using Coplay.Common.Services.BackgroundTasks;
using Coplay.Common.CoplayApi;
using System.Threading;
using Coplay.Common.CoplayApi.Models;
using Coplay.Services.Chat;

namespace Coplay.Controllers.Generation
{
    public class AssetGenerationController
    {
        private readonly BackgroundTaskService _backgroundTaskService;
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly ContextHelpers _contextHelpers;
        private readonly ICoplayThreadContext _coplayThreadContext;

        public AssetGenerationController(BackgroundTaskService backgroundTaskService, ICoplayApiClient coplayApiClient, ContextHelpers contextHelpers, ICoplayThreadContext coplayThreadContext)
        {
            _backgroundTaskService = backgroundTaskService;
            _coplayApiClient = coplayApiClient;
            _contextHelpers = contextHelpers;
            _coplayThreadContext = coplayThreadContext;
        }

        /// <summary>
        /// Generates a 3D model from an image using the specified provider (FAL or Meshy)
        /// </summary>
        /// <param name="imageUrl">URL of the input image</param>
        /// <param name="outputPath">Path where the model file should be saved</param>
        /// <param name="provider">Provider to use: 'fal' or 'meshy'</param>
        /// <param name="providerOptions">JSON string containing provider-specific options
        /// 
        /// FAL options:
        /// {
        ///   "textured_mesh": "true",
        ///   "seed": "12345",
        ///   "num_inference_steps": "50",
        ///   "guidance_scale": "7.5",
        ///   "octree_resolution": "256",
        ///   "output_format": "glb"
        /// }
        /// 
        /// Meshy options:
        /// {
        ///   "ai_model": "meshy-5",
        ///   "topology": "triangle",
        ///   "target_polycount": "50000",
        ///   "symmetry_mode": "auto",
        ///   "should_remesh": "true",
        ///   "should_texture": "true",
        ///   "enable_pbr": "true",
        ///   "texture_prompt": "realistic wood texture",
        ///   "output_format": "glb"
        /// }
        /// </param>
        /// <returns>The final result with model URLs</returns>
        public async Task<AssetGenerationResult> GenerateAssetFromImage(
            string imageUrl,
            string outputPath,
            AssetGenerationProviders provider = AssetGenerationProviders.Meshy4,
            string providerOptions = null)
        {
            CoplayLogger.Log($"Starting 3D asset generation from image using {provider}...");

            using var bgTask = _backgroundTaskService.CreateTask("Generating 3D asset from image...");
            var cancellationToken = bgTask.CancellationTokenSource.Token;

            string jobId = null;

            try
            {
                string imageData = imageUrl;
                if (imageUrl.StartsWith("file://")) {
                    imageData = await _contextHelpers.LoadFileToDataUriBase64(imageUrl.Substring(7));
                }
                
                // Create the base request
                var request = new AssetGenerationRequest
                {
                    Provider = provider.ToString(),
                    InputImageUrl = imageData
                };

                // Parse provider-specific options from JSON
                if (!string.IsNullOrEmpty(providerOptions))
                {
                    try
                    {
                        var options = ParseJsonToDictionary(providerOptions);
                        ApplyProviderOptions(request, options);
                    }
                    catch (System.Exception ex)
                    {
                        CoplayLogger.LogWarning($"Failed to parse provider options JSON: {ex.Message}");
                    }
                }

                bgTask.UpdateProgress("Starting asset generation job...", 0.1f);

                // Start the generation job
                var jobResponse = await _coplayApiClient.GenerateAssetAsync(request, cancellationToken);
                jobId = jobResponse.JobId;

                CoplayLogger.Log($"Asset generation job started with ID: {jobId}");

                // Poll for completion
                AssetGenerationStatus status;
                do
                {
                    // Wait between polls
                    await Task.Delay(2000, cancellationToken);

                    // Get status
                    status = await _coplayApiClient.GetAssetGenerationStatusAsync(jobId, cancellationToken);

                    // Update progress with the message from backend
                    float progress = status.Progress.HasValue ? status.Progress.Value / 100f : 0f;
                    string progressMessage = !string.IsNullOrEmpty(status.Error) ? status.Error : 
                                           (!string.IsNullOrEmpty(status.Message) ? status.Message :
                                           $"Asset generation status: {status.Status}");
                    
                    bgTask.UpdateProgress(progressMessage, 0.1f + (progress * 0.8f));

                    CoplayLogger.Log($"Asset generation status: {status.Status}, Progress: {status.Progress}%, Message: {progressMessage}");

                    // Check for completion or failure
                    if (status.Status == AssetGenerationStatusValues.Failed)
                    {
                        throw new Exception($"Asset generation failed: {status.Error}");
                    }

                } while (status.Status != AssetGenerationStatusValues.Completed);

                bgTask.UpdateProgress("Getting final result...", 0.9f);

                // Get the final result
                var result = status.Result;

                // Download the model file if an output path was specified
                if (!string.IsNullOrEmpty(outputPath))
                {
                    await DownloadModel(result, outputPath, bgTask, cancellationToken);
                }

                CoplayLogger.Log($"3D asset generation from image using {provider} completed successfully!");
                return result;
            }
            catch (OperationCanceledException)
            {
                CoplayLogger.Log("3D asset generation cancelled");
                await CancelAssetGeneration(jobId);
                throw new Exception("3D asset generation cancelled");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error generating 3D asset: {ex.Message}");
                throw;
            }
            finally
            {
                bgTask.Complete();
            }
        }

        /// <summary>
        /// Generates a 3D model from a text prompt using Meshy
        /// </summary>
        /// <param name="prompt">Text prompt for 3D model generation</param>
        /// <param name="outputPath">Path where the model file should be saved</param>
        /// <param name="providerOptions">JSON string containing provider-specific options
        /// 
        /// Meshy text-to-3D options:
        /// {
        ///   "art_style": "realistic",
        ///   "negative_prompt": "low quality, low resolution",
        ///   "enable_refinement": "true",
        ///   "output_format": "glb"
        /// }
        /// </param>
        /// <returns>The final result with model URLs</returns>
        public async Task<AssetGenerationResult> GenerateAssetFromText(
            string prompt,
            string outputPath,
            AssetGenerationProviders provider = AssetGenerationProviders.Meshy4,
            string providerOptions = null)
        {
            CoplayLogger.Log("Starting 3D asset generation from text...");

            using var bgTask = _backgroundTaskService.CreateTask("Generating 3D asset from text...");
            var cancellationToken = bgTask.CancellationTokenSource.Token;

            try
            {
                // Create the base request
                var request = new AssetGenerationRequest
                {
                    Provider = provider.ToString(),
                    Prompt = prompt,
                };

                // Parse provider-specific options from JSON
                if (!string.IsNullOrEmpty(providerOptions))
                {
                    try
                    {
                        var options = ParseJsonToDictionary(providerOptions);
                        ApplyProviderOptions(request, options);
                    }
                    catch (Exception ex)
                    {
                        CoplayLogger.LogWarning($"Failed to parse provider options JSON: {ex.Message}");
                    }
                }

                bgTask.UpdateProgress("Starting asset generation job...", 0.1f);

                // Start the generation job
                var jobResponse = await _coplayApiClient.GenerateAssetAsync(request, cancellationToken);
                string jobId = jobResponse.JobId;

                CoplayLogger.Log($"Asset generation job started with ID: {jobId}");

                // Poll for completion
                AssetGenerationStatus status;
                do
                {
                    // Wait between polls
                    await Task.Delay(2000, cancellationToken);

                    // Get status
                    status = await _coplayApiClient.GetAssetGenerationStatusAsync(jobId, cancellationToken);

                    // Update progress with the message from backend
                    float progress = status.Progress.HasValue ? status.Progress.Value / 100f : 0f;
                    string progressMessage = !string.IsNullOrEmpty(status.Error) ? status.Error : 
                                           (!string.IsNullOrEmpty(status.Message) ? status.Message :
                                           $"Asset generation status: {status.Status}");
                    
                    bgTask.UpdateProgress(progressMessage, 0.1f + (progress * 0.8f));

                    CoplayLogger.Log($"Asset generation status: {status.Status}, Progress: {status.Progress}%, Message: {progressMessage}");

                    // Check for completion or failure
                    if (status.Status == AssetGenerationStatusValues.Failed)
                    {
                        throw new Exception($"Asset generation failed: {status.Error}");
                    }

                } while (status.Status != AssetGenerationStatusValues.Completed);

                bgTask.UpdateProgress("Getting final result...", 0.9f);

                // Get the final result
                var result = status.Result;

                // Download the model file if an output path was specified
                if (!string.IsNullOrEmpty(outputPath))
                {
                    await DownloadModel(result, outputPath, bgTask, cancellationToken);
                }

                CoplayLogger.Log("3D asset generation completed successfully!");
                return result;
            }
            catch (OperationCanceledException)
            {
                CoplayLogger.Log("3D asset generation cancelled");
                return null;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error generating 3D asset: {ex.Message}");
                throw;
            }
            finally
            {
                bgTask.Complete();
            }
        }

        /// <summary>
        /// Downloads the generated model to the specified path
        /// </summary>
        private async Task DownloadModel(AssetGenerationResult result, string outputPath, BackgroundTask bgTask, CancellationToken cancellationToken)
        {
            // Determine which URL to use and ensure correct extension
            string modelUrl;
            string expectedExtension;

            if (!string.IsNullOrEmpty(result.ModelGlbUrl))
            {
                modelUrl = result.ModelGlbUrl;
                expectedExtension = ".glb";
                CoplayLogger.Log("Downloading PBR GLB model...");
            }
            else
            {
                throw new Exception("No model URLs available in the result");
            }

            // Ensure correct file extension
            if (Path.GetExtension(outputPath) != expectedExtension)
            {
                outputPath = Path.ChangeExtension(outputPath, expectedExtension);
            }

            // Ensure directory exists
            var directoryPath = Path.GetDirectoryName(outputPath) ?? ".";
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            bgTask.UpdateProgress("Downloading model file...", 0.95f);

            // Download the file
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(modelUrl, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to download model: {response.StatusCode} {response.ReasonPhrase}");
            }

            // Save the file
            await using (var responseStream = await response.Content.ReadAsStreamAsync())
            await using (var fileStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write, FileShare.None))
            {
                await responseStream.CopyToAsync(fileStream, cancellationToken);
            }

            // Refresh Asset Database
            AssetDatabase.Refresh();

            CoplayLogger.Log($"Model downloaded to: {outputPath}");
        }

        /// <summary>
        /// Cancels a running asset generation job
        /// </summary>
        /// <param name="jobId">The job ID to cancel</param>
        /// <returns>True if cancellation was successful</returns>
        public async Task<bool> CancelAssetGeneration(string jobId)
        {
            try
            {
                var result = await _coplayApiClient.CancelAssetGenerationAsync(jobId);
                if (result)
                {
                    CoplayLogger.Log($"Successfully cancelled asset generation job: {jobId}");
                }
                else
                {
                    CoplayLogger.LogWarning($"Failed to cancel asset generation job: {jobId}");
                }
                return result;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error cancelling asset generation job {jobId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates textures for a 3D model using Meshy retexturing
        /// </summary>
        /// <param name="modelPath">Path to the 3D model file</param>
        /// <param name="outputPath">Path where the base color texture should be saved</param>
        /// <param name="options">Dictionary containing texture generation options</param>
        /// <returns>True if texture generation completed successfully</returns>
        public async Task<bool> GenerateTexture(string modelPath, string outputPath, Dictionary<string, string> options = null)
        {
            CoplayLogger.Log("Starting texture generation using backend API...");

            using var bgTask = _backgroundTaskService.CreateTask("Generating texture...");
            var cancellationToken = bgTask.CancellationTokenSource.Token;

            string jobId = null;

            try
            {
                // Get the current thread ID for organizing the upload
                string currentThreadId = _coplayThreadContext.CurrentThreadId;
                if (string.IsNullOrEmpty(currentThreadId))
                {
                    throw new Exception("Error: No active thread found. Cannot upload model file for texture generation.");
                }

                CoplayLogger.Log($"Uploading model file {modelPath} for texture generation in thread {currentThreadId}");
                bgTask.UpdateProgress("Uploading model file...", 0.1f);

                // Upload the model file to Supabase storage
                FileUploadResponse uploadResponse = await _coplayApiClient.UploadFileAsync(modelPath, currentThreadId);
                
                if (!uploadResponse.Success)
                {
                    throw new Exception($"Error: Failed to upload model file: {uploadResponse.Error}");
                }

                if (string.IsNullOrEmpty(uploadResponse.FileUrl))
                {
                    throw new Exception("Error: File upload succeeded but no download URL was provided.");
                }

                CoplayLogger.Log($"Model file uploaded successfully. Using URL: {uploadResponse.FileUrl}");

                // Create the asset generation request for texture generation
                var request = new AssetGenerationRequest
                {
                    Provider = AssetGenerationProviders.Meshy4.ToString(),
                    ModelUrl = uploadResponse.FileUrl,
                    Prompt = options?.GetValueOrDefault("object_prompt", ""),
                    TexturePrompt = options?.GetValueOrDefault("style_prompt", ""),
                    ArtStyle = options?.GetValueOrDefault("art_style", "realistic") ?? "realistic",
                    NegativePrompt = options?.GetValueOrDefault("negative_prompt", "low quality, blurry, distorted"),
                    TextureImageUrl = options?.GetValueOrDefault("texture_image_url", ""),
                    AiModel = options?.GetValueOrDefault("ai_model", "meshy-4") ?? "meshy-4"
                };

                bgTask.UpdateProgress("Starting texture generation job...", 0.1f);

                // Start the asset generation job with texture generation provider
                var jobResponse = await _coplayApiClient.GenerateAssetAsync(request, cancellationToken);
                jobId = jobResponse.JobId;

                CoplayLogger.Log($"Texture generation job started with ID: {jobId}");

                // Poll for completion using existing asset generation status endpoint
                AssetGenerationStatus status;
                do
                {
                    // Wait between polls
                    await Task.Delay(2000, cancellationToken);

                    // Get status
                    status = await _coplayApiClient.GetAssetGenerationStatusAsync(jobId, cancellationToken);

                    // Update progress with the message from backend
                    float progress = status.Progress.HasValue ? status.Progress.Value / 100f : 0f;
                    string progressMessage = !string.IsNullOrEmpty(status.Error) ? status.Error : 
                                           (!string.IsNullOrEmpty(status.Message) ? status.Message :
                                           $"Texture generation status: {status.Status}");
                    
                    bgTask.UpdateProgress(progressMessage, 0.1f + (progress * 0.8f));

                    CoplayLogger.Log($"Texture generation status: {status.Status}, Progress: {status.Progress}%, Message: {progressMessage}");

                    // Check for completion or failure
                    if (status.Status == AssetGenerationStatusValues.Failed)
                    {
                        throw new Exception($"Texture generation failed: {status.Error}");
                    }

                } while (status.Status != AssetGenerationStatusValues.Completed);

                bgTask.UpdateProgress("Downloading texture files...", 0.9f);

                // Get the final result
                var result = status.Result;

                // Download texture files if URLs are available
                if (!string.IsNullOrEmpty(outputPath) && result != null)
                {
                    await DownloadTextures(result, outputPath, bgTask, cancellationToken);
                }

                CoplayLogger.Log("Texture generation completed successfully!");
                return true;
            }
            catch (OperationCanceledException)
            {
                CoplayLogger.Log("Texture generation cancelled");
                await CancelAssetGeneration(jobId);
                throw new Exception("Texture generation cancelled");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error generating texture: {ex.Message}");
                throw;
            }
            finally
            {
                bgTask.Complete();
            }
        }

        /// <summary>
        /// Downloads texture files from the generation result
        /// </summary>
        private async Task DownloadTextures(AssetGenerationResult result, string outputPath, BackgroundTask bgTask, CancellationToken cancellationToken)
        {
            // Ensure correct file extension for base color
            if (Path.GetExtension(outputPath) != ".png")
            {
                outputPath = Path.ChangeExtension(outputPath, ".png");
            }

            // Ensure directory exists
            var directoryPath = Path.GetDirectoryName(outputPath) ?? ".";
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            using var httpClient = new HttpClient();

            // Download base color texture
            if (!string.IsNullOrEmpty(result.TextureUrl))
            {
                var response = await httpClient.GetAsync(result.TextureUrl, cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    await using var responseStream = await response.Content.ReadAsStreamAsync();
                    await using var fileStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write, FileShare.None);
                    await responseStream.CopyToAsync(fileStream, cancellationToken);
                    CoplayLogger.Log($"Base color texture downloaded to: {outputPath}");
                }
            }

            // Refresh Asset Database
            AssetDatabase.Refresh();
        }

        /// <summary>
        /// Simple JSON parser to convert JSON string to dictionary
        /// </summary>
        private Dictionary<string, object> ParseJsonToDictionary(string json)
        {
            var result = new Dictionary<string, object>();
            
            // Remove whitespace and braces
            json = json.Trim();
            if (json.StartsWith("{") && json.EndsWith("}"))
            {
                json = json.Substring(1, json.Length - 2);
            }

            // Simple parsing - split by commas, then by colons
            var pairs = json.Split(',');
            foreach (var pair in pairs)
            {
                var keyValue = pair.Split(':');
                if (keyValue.Length == 2)
                {
                    var key = keyValue[0].Trim().Trim('"');
                    var value = keyValue[1].Trim().Trim('"');
                    result[key] = value;
                }
            }

            return result;
        }

        /// <summary>
        /// Applies provider-specific options from a dictionary to the AssetGenerationRequest
        /// </summary>
        private void ApplyProviderOptions(AssetGenerationRequest request, Dictionary<string, object> options)
        {
            foreach (var option in options)
            {
                switch (option.Key.ToLower())
                {
                    // FAL Hunyuan 2.1 options
                    case "textured_mesh":
                        request.TexturedMesh = bool.TryParse(option.Value?.ToString(), out var texturedMesh) && texturedMesh;
                        break;
                    case "seed":
                        request.Seed = int.TryParse(option.Value?.ToString(), out var seed) ? seed : null;
                        break;
                    case "num_inference_steps":
                        request.NumInferenceSteps = int.TryParse(option.Value?.ToString(), out var steps) ? steps : 50;
                        break;
                    case "guidance_scale":
                        request.GuidanceScale = float.TryParse(option.Value?.ToString(), out var guidance) ? guidance : 7.5f;
                        break;
                    case "octree_resolution":
                        request.OctreeResolution = int.TryParse(option.Value?.ToString(), out var resolution) ? resolution : 256;
                        break;

                    // Meshy text-to-3D options
                    case "art_style":
                        request.ArtStyle = option.Value?.ToString() ?? MeshyArtStyles.Realistic;
                        break;
                    case "negative_prompt":
                        request.NegativePrompt = option.Value?.ToString();
                        break;
                    case "enable_refinement":
                        request.EnableRefinement = bool.TryParse(option.Value?.ToString(), out var refinement) ? refinement : true;
                        break;

                    // Meshy image-to-3D options
                    case "ai_model":
                        request.AiModel = option.Value?.ToString() ?? MeshyAiModels.Meshy4;
                        break;
                    case "topology":
                        request.Topology = option.Value?.ToString() ?? MeshyTopologies.Triangle;
                        break;
                    case "target_polycount":
                        request.TargetPolycount = int.TryParse(option.Value?.ToString(), out var polycount) ? polycount : 30000;
                        break;
                    case "symmetry_mode":
                        request.SymmetryMode = option.Value?.ToString() ?? MeshySymmetryModes.Auto;
                        break;
                    case "should_remesh":
                        request.ShouldRemesh = bool.TryParse(option.Value?.ToString(), out var remesh) ? remesh : true;
                        break;
                    case "should_texture":
                        request.ShouldTexture = bool.TryParse(option.Value?.ToString(), out var texture) ? texture : true;
                        break;
                    case "enable_pbr":
                        request.EnablePbr = bool.TryParse(option.Value?.ToString(), out var pbr) && pbr;
                        break;
                    case "texture_prompt":
                        request.TexturePrompt = option.Value?.ToString();
                        break;
                    case "texture_image_url":
                        request.TextureImageUrl = option.Value?.ToString();
                        break;
                    case "moderation":
                        request.Moderation = bool.TryParse(option.Value?.ToString(), out var moderation) && moderation;
                        break;

                    default:
                        CoplayLogger.LogWarning($"Unknown provider option: {option.Key}");
                        break;
                }
            }
        }
    }
} 
