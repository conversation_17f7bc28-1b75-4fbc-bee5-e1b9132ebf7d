import bpy
import math
import os
import mathutils
import subprocess
import sys
import argparse
import shutil
from pathlib import Path

def rotate_object(obj, start_angle, end_angle, start_frame, end_frame, axis='Z'):
    """
    Rotate an object around specified axis from start_angle to end_angle.
    
    Args:
        obj: The Blender object to rotate
        start_angle: Starting angle in degrees
        end_angle: Ending angle in degrees
        start_frame: Starting frame number
        end_frame: Ending frame number
        axis: Rotation axis ('X', 'Y', or 'Z')
    """
    axis_index = {'X': 0, 'Y': 1, 'Z': 2}[axis.upper()]
    
    # Set start rotation
    obj.rotation_euler[axis_index] = math.radians(start_angle)
    obj.keyframe_insert(data_path="rotation_euler", frame=start_frame)
    
    # Set end rotation
    obj.rotation_euler[axis_index] = math.radians(end_angle)
    obj.keyframe_insert(data_path="rotation_euler", frame=end_frame)

def create_rotating_video(fbx_path, output_path, duration_seconds=1):
    # # Add number to output path
    # base, ext = os.path.splitext(output_path)
    # counter = 1
    # while os.path.exists(output_path):
    #     output_path = f"{base}_{counter}{ext}"
    #     counter += 1

    # Clear existing scene
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete()
    
    # Import FBX
    bpy.ops.import_scene.fbx(filepath=fbx_path)
    
    # Select all imported objects
    bpy.ops.object.select_all(action='SELECT')
    
    # Add debug print to check object scales and locations
    for obj in bpy.context.selected_objects:
        print(f"Object: {obj.name}")
        print(f"Location: {obj.location}")
        print(f"Scale: {obj.scale}")
        
    # Center objects if needed
    bpy.ops.object.origin_set(type='ORIGIN_GEOMETRY', center='BOUNDS')
    
    # Get the bounds of all objects
    min_x = min_y = min_z = float('inf')
    max_x = max_y = max_z = float('-inf')
    
    for obj in bpy.context.selected_objects:
        for point in obj.bound_box:
            world_point = obj.matrix_world @ mathutils.Vector(point)
            min_x = min(min_x, world_point.x)
            max_x = max(max_x, world_point.x)
            min_y = min(min_y, world_point.y)
            max_y = max(max_y, world_point.y)
            min_z = min(min_z, world_point.z)
            max_z = max(max_z, world_point.z)
    
    # Calculate dimensions
    dimensions = mathutils.Vector((max_x - min_x, max_y - min_y, max_z - min_z))
    scale_factor = max(dimensions) / 3.0
    
    bpy.ops.object.camera_add(location=(10, 0, 4))
    camera = bpy.context.active_object
    
    camera.location = (20 * scale_factor, 0, 8 * scale_factor)
    
    # Create an empty at the center
    pivot_empty = bpy.data.objects.new("PivotEmpty", None)
    bpy.context.scene.collection.objects.link(pivot_empty)
    pivot_empty.location = (0, 0, 0)  # Center of the scene

    # Parent camera to the pivot empty
    camera.parent = pivot_empty
    
    # (Optional) Add a track-to constraint to make the camera always look at the origin
    constraint = camera.constraints.new(type='TRACK_TO')
    constraint.target = pivot_empty  # If you want the camera to point at the pivot
    constraint.track_axis = 'TRACK_NEGATIVE_Z'
    constraint.up_axis = 'UP_Y'
    
    # Set up rendering settings
    scene = bpy.context.scene
    scene.camera = camera
    scene.render.image_settings.file_format = 'FFMPEG'
    scene.render.ffmpeg.format = 'MPEG4'
    scene.render.ffmpeg.codec = 'H264'
    scene.render.filepath = output_path
    # Resolution should be good enough to see the model.
    scene.render.resolution_x = 1920
    scene.render.resolution_y = 1080
    
    # Animation setup
    fps = 5 # We only need a few frames for the VLM to understand things.
    scene.render.fps = fps
    scene.frame_end = duration_seconds * fps
    
    # Rotate pivot empty around Z-axis from 0 to 90 degrees
    rotate_object(pivot_empty, 0, -90, 1, scene.frame_end, 'Z')
    
    # After creating the pivot empty, add the coordinate axes gizmo
    def create_arrow(name, direction, color):
        # Create arrow mesh
        bpy.ops.mesh.primitive_cone_add(radius1=0.1, radius2=0, depth=0.3)
        cone = bpy.context.active_object
        cone.name = f"{name}_head"
        
        bpy.ops.mesh.primitive_cylinder_add(radius=0.02, depth=0.7)
        cylinder = bpy.context.active_object
        cylinder.name = f"{name}_shaft"
        
        # Position and rotate arrow parts
        if direction == 'x':
            cylinder.rotation_euler = (0, math.radians(90), 0)
            cone.rotation_euler = (0, math.radians(90), 0)
            cone.location = (0.55, 0, 0)
            cylinder.location = (0.25, 0, 0)
        elif direction == 'y':
            cylinder.rotation_euler = (math.radians(-90), 0, 0)
            cone.rotation_euler = (math.radians(-90), 0, 0)
            cone.location = (0, 0.55, 0)
            cylinder.location = (0, 0.25, 0)
        else:  # z
            cone.location = (0, 0, 0.55)
            cylinder.location = (0, 0, 0.25)
        
        # Set material color
        material = bpy.data.materials.new(name=f"{name}_material")
        material.use_nodes = False
        material.diffuse_color = (*color, 1)
        
        cone.data.materials.append(material)
        cylinder.data.materials.append(material)
        
        # Parent both parts to an empty
        arrow_empty = bpy.data.objects.new(f"{name}_arrow", None)
        bpy.context.scene.collection.objects.link(arrow_empty)
        
        cone.parent = arrow_empty
        cylinder.parent = arrow_empty
        return arrow_empty
    
    def convert_arrow_to_unity_coordinates(arrow_obj):
        """
        Rotates the given arrow so that
        Blender's (Z up) coordinate system
        matches Unity's (Y up) coordinate system.
        This is effectively a -90° rotation around the X-axis.
        """
        arrow_obj.rotation_euler.x -= math.radians(90)
        if arrow_obj.name == "Y_arrow":
            # arrow_obj.rotation_euler.y -= math.radians(-90)
            arrow_obj.rotation_euler.x -= math.radians(180)
        elif arrow_obj.name == "Z_arrow":
            arrow_obj.rotation_euler.x -= math.radians(180)
        elif arrow_obj.name == "X_arrow":
            arrow_obj.rotation_euler.y -= math.radians(180)

    # Create gizmo container
    gizmo_empty = bpy.data.objects.new("GizmoEmpty", None)
    bpy.context.scene.collection.objects.link(gizmo_empty)
    gizmo_empty.empty_display_type = 'PLAIN_AXES'
    
    # Create arrows (using original Blender coordinates)
    x_arrow = create_arrow("X", "x", (1, 0, 0))  # Red
    y_arrow = create_arrow("Y", "y", (0, 1, 0))  # Green
    z_arrow = create_arrow("Z", "z", (0, 0, 1))  # Blue

    convert_arrow_to_unity_coordinates(x_arrow)
    convert_arrow_to_unity_coordinates(y_arrow)
    convert_arrow_to_unity_coordinates(z_arrow)

    # Parent arrows to gizmo empty
    x_arrow.parent = gizmo_empty
    y_arrow.parent = gizmo_empty
    z_arrow.parent = gizmo_empty
    
    # Set up gizmo position and scale
    gizmo_empty.scale = (0.3, 0.3, 0.3)  # Make gizmo smaller
    
    # Create driver to keep gizmo in corner of view
    def create_gizmo_driver():
        # Create empty that will follow camera
        follow = bpy.data.objects.new("GizmoFollow", None)
        bpy.context.scene.collection.objects.link(follow)
        
        # Parent gizmo to follow empty
        gizmo_empty.parent = follow
        
        # Parent follow empty to camera
        follow.parent = camera
        
        # Position relative to camera
        follow.location = (0.4, -0.02, -2)  # Adjust these values to position in top-right corner

        # # Copy rotation from pivot empty
        copy_rot = follow.constraints.new(type='COPY_ROTATION')
        copy_rot.target = pivot_empty

        # Make sure gizmo and all its children are visible in frame 1
        gizmo_empty.hide_viewport = False
        gizmo_empty.hide_render = False
        for child in gizmo_empty.children_recursive:
            child.hide_viewport = False
            child.hide_render = False
            child.keyframe_insert(data_path="hide_render", frame=1)
        gizmo_empty.keyframe_insert(data_path="hide_render", frame=1)
        
        # Hide gizmo and all its children after frame 1
        for child in gizmo_empty.children_recursive:
            child.hide_render = True
            child.keyframe_insert(data_path="hide_render", frame=2)
        gizmo_empty.hide_render = True
        gizmo_empty.keyframe_insert(data_path="hide_render", frame=2)

    create_gizmo_driver()
    # Render animation
    bpy.ops.render.render(animation=True)
    print(f"Rendered animation")

def test_create_rotating_video():
    # Define paths for both models
    model_paths = [
        "../coplay/test_data/CuteCat.fbx",
        "../coplay/test_data/water_location_japanes_island.fbx"
    ]
    
    # Create videos for each model
    for model_path in model_paths:
        # Generate output path based on model name
        model_name = os.path.splitext(os.path.basename(model_path))[0]
        output_path = f"{model_name}_video.mp4"
        
        print(f"Creating video for {model_name}...")
        create_rotating_video(model_path, output_path)
        print(f"Finished creating video: {output_path}")

def create_rotating_video_subprocess(fbx_path: str, output_path: str):
    """
    We have to run the Blender process as a separate process to avoid race conditions with Blender.
    """
    current_file = str(Path(__file__).resolve())
    blender_executable = find_blender_executable()
    if not blender_executable:
        raise FileNotFoundError("Blender executable not found. Please ensure Blender is installed and in your PATH.")
     
    cmd = [
        blender_executable,
        "--background",
        "--python", current_file,
        "--",
        fbx_path,
        output_path,
    ]
    
    print(f"Executing command: {' '.join(cmd)}")
    try:
        if sys.platform.startswith('linux'):
            # We need this virtual display for the rendering to work fast
            from pyvirtualdisplay import Display   
            with Display() as disp:
                print(f"Display: {disp.is_alive()}")
                result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=300)
        else:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=300)
    except subprocess.TimeoutExpired as e:
        print("Blender timed out after 300 seconds")
        print(e.stdout)
        print(e.stderr)
        return False
    except subprocess.CalledProcessError as e:
        print(f"Blender failed with error: {e}")
        print(e.stdout)
        print(e.stderr)
        return False

    if result.stderr:
        print("Blender error output:")
        print(result.stderr)
        if "Blender Texture Not Loaded" in result.stderr:
            print("We know of this error, it's fine for now.")
        return False
    
    return True

def find_blender_executable():
    """
    Try to locate the Blender executable dynamically.
    
    Returns:
        The path to the Blender executable if found, or None otherwise.
    """
    # Check if 'blender' is in the system PATH
    blender_path = shutil.which("blender")
    if blender_path:
        return blender_path

    # If not in PATH, try OS-specific fallback locations
    if sys.platform.startswith('win'):
        possible_paths = [
            r"C:\Program Files\Blender Foundation\Blender\blender.exe",
            r"C:\Program Files (x86)\Blender Foundation\Blender\blender.exe"
        ]
    elif sys.platform.startswith('darwin'):
        possible_paths = [
            "/Applications/Blender.app/Contents/MacOS/blender",
            "/Applications/Blender.app/Contents/MacOS/Blender"
        ]
    else:
        # Assume Linux or similar Unix system
        possible_paths = [
            "/usr/bin/blender",
            "/usr/local/bin/blender"
        ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    print("Blender executable not found.")
    return None


if __name__ == "__main__":
    # Set up argparse to handle command-line arguments.
    parser = argparse.ArgumentParser(
        description="Create a rotating video from an FBX file using Blender"
    )
    parser.add_argument("fbx_path", nargs="?", help="Path to the FBX file")
    parser.add_argument("output_path", nargs="?", help="Path to save the output video")
    parser.add_argument("--test", action="store_true", help="Run test_create_rotating_video instead")
    
    # When running through Blender, arguments for our script are after '--'
    if "--" in sys.argv:
        argv = sys.argv[sys.argv.index("--") + 1:]
    else:
        argv = sys.argv[1:]
    
    args = parser.parse_args(argv)
    
    if args.test:
        test_create_rotating_video()
    elif args.fbx_path and args.output_path:
        print(f"Starting to process FBX: {args.fbx_path}")
        create_rotating_video(args.fbx_path, args.output_path)
        print(f"Finished processing. Video saved to: {args.output_path}")
    else:
        parser.print_help()