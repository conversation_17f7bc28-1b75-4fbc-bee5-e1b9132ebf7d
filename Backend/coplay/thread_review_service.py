import asyncio
from datetime import datetime, timezone
from typing import Optional, List

from langchain.schema import HumanMessage, SystemMessage
from langchain_core.load import load as lc_load

from .supabase_db import connection_pool
from .models import AIModel, AssistantMode
from .assistants.model_config import ModelConfig
from .logger import logger
from .notifications import DiscordNotifier

THREAD_REVIEW_NOT_SHOWN_TO_USER = "notShownToUser"

THREAD_REVIEW_PROMPT = """You are a helpful assistant that analyzes conversation threads to suggest improvements to user rules.

Your task is to review the conversation history and identify ONE specific, actionable improvement that could be added to the user's custom rules to make future conversations more effective.

Focus on:
- Patterns where the AI misunderstood user intent
- Repeated corrections the user had to make
- Areas where clearer instructions could prevent confusion
- Workflow improvements based on how the user actually works

IMPORTANT: If you don't find any meaningful patterns or issues that would benefit from a new rule, respond with "NO_IMPROVEMENTS_NEEDED" and do NOT use any tools. Only suggest improvements when there are clear, actionable patterns that would genuinely help future interactions.

If you do identify an improvement, use the replace_in_file tool to add your suggestion to the user's .coplayrules.md file. Your suggestion should be:
- Concise (1-2 sentences max)
- Specific and actionable
- Generalizable to future tasks the user may want to accomplish in Unity
- Use the SEARCH block to match the content to extend or replace in the existing rules file. If the rules are empty, the SEARCH block should be empty.

Example good suggestions:
- "Always ask for confirmation before deleting or removing existing code"
- "When creating UI elements, use the project's existing color scheme from the style guide"
- "Prefer async/await patterns over callbacks in C# code"

If the .coplayrules.md file is empty, add your suggestion as the first rule.

Use the replace_in_file tool to make this change."""


MODEL_CONFIG = ModelConfig.create_for_model(AIModel.GEMINI_2_5_PRO) # TODO: we should turn off caching since it's more expensive and we don't need caching here.


async def should_review_thread(device_id: str, thread_id: str) -> bool:
    """
    Check if we should review the specified thread for this device.
    Returns the thread_id if it should be reviewed, None otherwise.
    """
    # TODO: we should also ensure that the thread has not already been reviewed. If so, skip it.
    async with connection_pool.connection() as conn:
        # Check the specific thread provided, ensuring it belongs to this device
        result = await conn.execute("""
            SELECT id, messages FROM threads 
            WHERE id = %(thread_id)s AND device_id = %(device_id)s
        """, {"thread_id": thread_id, "device_id": device_id})
        
        thread_row = await result.fetchone()
        
        if not thread_row:
            return False
        
        # Check if this thread has more than 1 user message
        try:
            messages = lc_load(thread_row["messages"] or [], valid_namespaces=["coplay"])
            
            user_message_count = 0
            for message in messages:
                if hasattr(message, 'additional_kwargs') and message.additional_kwargs.get("type") == "coplay_human_message":
                    user_message_count += 1
            
            if user_message_count > 1:
                return True
                
        except Exception as e:
            logger.error(f"Error parsing thread messages for review check: {e}")
            
    return False

async def create_thread_review(review_id: str, device_id: str, thread_id: str, custom_rules: Optional[str] = None):
    """
    Create a thread review by running LLM processing and storing the result.
    This is meant to be run as a background task.
    
    Args:
        review_id: Unique identifier for the review
        device_id: Device ID associated with the review
        thread_id: Thread ID to review
        custom_rules: Optional custom rules content to use for the review
    """
    try:
        # Get the thread data
        async with connection_pool.connection() as conn:
            result = await conn.execute("""
                SELECT messages FROM threads WHERE id = %(thread_id)s
            """, {"thread_id": thread_id})
            
            thread_row = await result.fetchone()
            if not thread_row:
                logger.warning(f"Thread {thread_id} not found for review {review_id}")
                return
            
            # Parse messages
            messages = lc_load(thread_row["messages"] or [], valid_namespaces=["coplay"])
            
            # TODO: Ensure that the review model can also see all of the available tools that the original model had access to.
            conversation_context = _extract_conversation_context(messages)
            
            if not conversation_context:
                logger.info(f"No reviewable content found in thread {thread_id} for review {review_id}")
                return
            
            # Create the review record
            await conn.execute("""
                INSERT INTO thread_reviews (id, device_id, thread_id, suggestion, diff_content, status, created_at)
                VALUES (%(id)s, %(device_id)s, %(thread_id)s, %(suggestion)s, %(diff_content)s, 'pending', %(created_at)s)
            """, {
                "id": review_id,
                "device_id": device_id,
                "thread_id": thread_id,
                "suggestion": "",
                "diff_content": "",
                "created_at": datetime.now(tz=timezone.utc)
            })
            
            # Generate review suggestion
            suggestion = await _generate_review_suggestion(conversation_context, custom_rules) # TODO: we should charge tokens for this.
            
            if not suggestion:
                logger.warning(f"Failed to generate review suggestion for thread {thread_id}, review {review_id}")
                return
            
            # Update the review record with the LLM results
            await conn.execute("""
                UPDATE thread_reviews 
                SET status = %(status)s, suggestion = %(suggestion)s, diff_content = %(diff_content)s
                WHERE id = %(id)s
            """, {
                "id": review_id,
                "status": THREAD_REVIEW_NOT_SHOWN_TO_USER,
                "suggestion": suggestion.get("suggestion", ""),
                "diff_content": suggestion.get("diff_content", ""),
            })
            
            logger.info(f"Created thread review {review_id} for device {device_id}")
            
    except Exception as e:
        logger.error(f"Error creating thread review {review_id}: {e}")

def _extract_conversation_context(messages: List) -> str:
    """
    Extract relevant conversation context from messages for review.
    """
    context_parts = []
    
    # Extract system prompt
    system_message = None
    for message in messages:
        if hasattr(message, 'type') and message.type == "system":
            system_message = message.content
            if isinstance(system_message, list) and len(system_message) > 0:
                system_message = system_message[0].get("text", "")
            break
    
    if system_message:
        context_parts.append(f"SYSTEM PROMPT:\n{system_message}\n")
    
    # Extract conversation turns (user messages and AI responses)
    conversation_turns = []
    for message in messages:
        if hasattr(message, 'additional_kwargs'):
            msg_type = message.additional_kwargs.get("type")
            if msg_type == "coplay_human_message":
                user_msg = message.additional_kwargs.get("user_message", "")
                if user_msg:
                    conversation_turns.append(f"USER: {user_msg}")
            elif hasattr(message, 'type') and message.type == "ai":
                ai_content = message.content
                if isinstance(ai_content, list):
                    # Extract text content from structured content
                    text_parts = []
                    for part in ai_content:
                        if isinstance(part, dict) and part.get("type") == "text":
                            text_parts.append(part.get("text", ""))
                    ai_content = " ".join(text_parts)
                elif isinstance(ai_content, str):
                    pass  # Already a string
                else:
                    ai_content = str(ai_content)
                
                if ai_content and len(ai_content.strip()) > 0:
                    # Truncate very long AI responses for review
                    if len(ai_content) > 1000:
                        ai_content = ai_content[:1000] + "..."
                    conversation_turns.append(f"AI: {ai_content}")
    
    if conversation_turns:
        context_parts.append("CONVERSATION:\n" + "\n\n".join(conversation_turns))
    
    return "\n\n".join(context_parts)


async def _generate_review_suggestion(conversation_context: str, custom_rules: Optional[str] = None) -> Optional[dict]:
    """
    Generate a review suggestion using the LLM with tools.
    Returns a dict with 'suggestion' and 'diff_content' keys.
    
    Args:
        conversation_context: The conversation context to analyze
        custom_rules: Optional custom rules content to consider for the review
    """
    try:
        
        messages = [
            SystemMessage(content=THREAD_REVIEW_PROMPT),
            HumanMessage(content=conversation_context)
        ]
        
        # We add the custom rules again explicitly (it's already in first human msg) because we want to focus the model on it and avoid needle in a haystack issues.
        if custom_rules:
            messages.append(HumanMessage(content="Here is the user's current custom rules file -- .coplayrules.md:\n" + custom_rules + "\nEndOfFile\n\n Please make your suggestion based on the user's current rules."))
        else:
            messages.append(HumanMessage(content="The user's current custom rules file, .coplayrules.md, is empty. Please make your suggestion based on general best practices."))
        
        # Configure the model with tools using the new THREAD_REVIEW mode
        tool_context = {"projectRoot": "{{ projectRoot }}"}
        llm_with_tools = MODEL_CONFIG.with_tools(AssistantMode.THREAD_REVIEW, tool_context)
        
        response = await llm_with_tools.ainvoke(messages, timeout=30)
        
        # Check if the model indicated no improvements are needed
        response_content = response.content.strip() if hasattr(response, 'content') else ""
        if "NO_IMPROVEMENTS_NEEDED" in response_content:
            logger.info("Model indicated no improvements needed")
            return None
        
        # Extract tool calls from the response
        if hasattr(response, 'tool_calls') and response.tool_calls:
            tool_call = response.tool_calls[0]  # Get the first tool call
            if tool_call['name'] == 'replace_in_file':
                # Get the diff content directly
                diff_content = tool_call['args'].get('diff', '')
                # Extract the suggestion text from the diff for display
                suggestion_text = _extract_suggestion_from_diff(diff_content)
                
                return {
                    "suggestion": suggestion_text,
                    "diff_content": diff_content
                }
        
        # Fallback if no tool call was made but model didn't say no improvements needed
        suggestion_text = response_content if response_content else "No suggestion generated"
        return {
            "suggestion": suggestion_text,
            "diff_content": ""
        }
        
    except Exception as e:
        logger.error(f"Error generating review suggestion: {e}")
        return None

def _extract_suggestion_from_diff(diff_content: str) -> str:
    """
    Extract the suggestion text from the diff content for display purposes.
    """
    try:
        # Look for content between ======= and +++++++ REPLACE
        separator_index = diff_content.find('\n=======\n')
        if separator_index == -1:
            return "New rule suggestion"
        
        replace_end_index = diff_content.find('\n+++++++ REPLACE')
        if replace_end_index == -1:
            return "New rule suggestion"
        
        replace_content = diff_content[separator_index + len('\n=======\n'):replace_end_index]
        
        # Extract just the new content (the suggestion)
        lines = replace_content.strip().split('\n')
        if lines:
            # Return the last non-empty line as the suggestion
            for line in reversed(lines):
                if line.strip():
                    return line.strip()
        
        return "New rule suggestion"
    except Exception as e:
        logger.error(f"Error extracting suggestion from diff: {e}")
        return "New rule suggestion"

async def get_review_by_id(review_id: str, device_id: str) -> Optional[dict]:
    """
    Get a specific review by review_id, ensuring it belongs to the correct device.
    Returns review data with status, or None if the review doesn't exist.
    """
    async with connection_pool.connection() as conn:
        result = await conn.execute("""
            SELECT id, thread_id, suggestion, diff_content, created_at, status FROM thread_reviews 
            WHERE id = %(review_id)s AND device_id = %(device_id)s
        """, {"review_id": review_id, "device_id": device_id})
        
        review_row = await result.fetchone()
        if review_row:
            return {
                "review_id": review_row["id"],
                "thread_id": review_row["thread_id"],
                "suggestion": review_row["suggestion"],
                "diff_content": review_row["diff_content"],
                "created_at": review_row["created_at"],
                "status": review_row["status"]
            }
    return None

async def _get_user_email_from_device_id(device_id: str) -> Optional[str]:
    """
    Get the user email from device_id by joining devices and users tables.
    """
    try:
        async with connection_pool.connection() as conn:
            result = await conn.execute("""
                SELECT u.email 
                FROM devices d 
                JOIN auth.users u ON d.user_id = u.id 
                WHERE d.id = %(device_id)s
            """, {"device_id": device_id})
            
            row = await result.fetchone()
            if row:
                return row["email"]
            return None
            
    except Exception as e:
        logger.error(f"Error getting user email for device {device_id}: {e}")
        return None

async def respond_to_review(review_id: str, accepted: bool) -> bool:
    """
    Mark a review as accepted or rejected.
    """
    try:
        status = "accepted" if accepted else "rejected"
        async with connection_pool.connection() as conn:
            # First, get the review data for the notification
            review_result = await conn.execute("""
                SELECT device_id, thread_id, suggestion FROM thread_reviews 
                WHERE id = %(review_id)s
            """, {"review_id": review_id})
            
            review_row = await review_result.fetchone()
            if not review_row:
                logger.warning(f"Review {review_id} not found")
                return False
            
            # Get user email from device_id
            user_email = await _get_user_email_from_device_id(review_row["device_id"])
            
            # Update the review status
            result = await conn.execute("""
                UPDATE thread_reviews 
                SET status = %(status)s, responded_at = %(responded_at)s
                WHERE id = %(review_id)s
            """, {
                "review_id": review_id,
                "status": status,
                "responded_at": datetime.now(tz=timezone.utc)
            })

            # Send Discord notification asynchronously (don't await)
            if result.rowcount > 0:
                # TODO: get the langsmith trace working for this and also send it to discord for easy checks.
                asyncio.create_task(DiscordNotifier.send_thread_review_notification(
                    review_id=review_id,
                    device_id=review_row["device_id"],
                    thread_id=review_row["thread_id"],
                    suggestion=review_row["suggestion"],
                    accepted=accepted,
                    user_email=user_email or "unknown"
                ))
            
            # TODO: we should probably send info to bigquery as well?
            
            return result.rowcount > 0
            
    except Exception as e:
        logger.error(f"Error responding to review {review_id}: {e}")
        return False
