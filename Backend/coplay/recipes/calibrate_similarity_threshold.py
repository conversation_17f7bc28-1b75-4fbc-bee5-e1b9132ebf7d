"""
This script finds all .txt files in its directory, generates embeddings for their content,
and upserts them into the 'recipes' namespace in Turbopuffer.
"""
import os
import asyncio
import sys
import argparse
from pathlib import Path
import json

# Add the project root to the python path to allow for absolute imports.
# This allows the script to be run directly.
# The project root is 3 levels up from the script file.
# coplay/Backend/coplay/recipes/embed_recipes.py
sys.path.append(str(Path(__file__).resolve().parents[2]))

from coplay.turbopuffer import get_text_embedding_similarities
from coplay.logger import logger

from prompt_utils import load_prompts_for_keyword
    
current_dir = Path(__file__).parent

async def main(keyword: str) -> tuple[float, float]:
    """
    Calibrate similarity threshold for a given keyword.
    
    Args:
        keyword: The keyword to calibrate for
        
    Returns:
        Tuple of (min_positive_similarity, max_negative_similarity)
    """
    logger.info(f"Starting calibration for keyword: {keyword}")
    
    # Load prompts using the new utility function
    positive_prompts, negative_prompts = load_prompts_for_keyword(keyword, current_dir)
    
    if not positive_prompts and not negative_prompts:
        logger.error(f"No prompts found for keyword '{keyword}' in either JSON or folder format.")
        return None, None
    
    if not positive_prompts:
        logger.error(f"No positive prompts found for keyword '{keyword}'. Cannot calibrate threshold.")
        return None, None
        
    if not negative_prompts:
        logger.error(f"No negative prompts found for keyword '{keyword}'. Cannot calibrate threshold.")
        return None, None
    
    logger.info(f"Found {len(positive_prompts)} positive prompts and {len(negative_prompts)} negative prompts")
    
    # Test positive prompts
    print("\n=== Testing positive prompts ===")
    min_positive_similarity = await test_prompts(positive_prompts, keyword, expected_matches=True)
    
    # Test negative prompts
    print("\n=== Testing negative prompts ===")
    max_negative_similarity = await test_prompts(negative_prompts, keyword, expected_matches=False)
    
    return min_positive_similarity, max_negative_similarity

async def test_prompts(prompts: list[str], keyword: str, expected_matches: bool) -> float:
    """
    Test a set of prompts and return similarity statistics.
    
    Args:
        prompts: List of prompts to test
        keyword: The keyword to test against
        expected_matches: Whether these prompts are expected to match (True for positive, False for negative)
        
    Returns:
        Min similarity for positive prompts, max similarity for negative prompts
    """
    successful_prompts, successful_embeddings = await get_text_embedding_similarities(prompts, namespace="recipes", context_id=keyword)

    assert len(successful_prompts) == len(successful_embeddings), "Number of prompts and embeddings do not match"
    total_analyzed_prompts = len(successful_prompts)
    
    if total_analyzed_prompts == 0:
        logger.warning("No embeddings were generated successfully")
        return 2.0 if expected_matches else 0.0

    matches = 0
    rejected = 0

    cosine_similarity_threshold = 0.172 # Arbitrary value. Run this script, the rerun it with the resulting calibration value by changing this value.
    similarities = []
    
    for i in range(total_analyzed_prompts):
        similarity = successful_embeddings[i].dist
        similarities.append(similarity)
        
        # For positive prompts, we expect the keyword to be in the prompt
        if expected_matches:
            assert keyword.lower() in successful_prompts[i].lower(), f"Keyword '{keyword}' not found in positive prompt: {successful_prompts[i]}"
        
        if similarity > cosine_similarity_threshold:
            matches += 1
            print(f"Match - score: {similarity:.4f} | {successful_prompts[i][:10]}...")
        else: # rejections are if the prompt matches the negative prompts
            rejected += 1
            print(f"Rejected - score: {similarity:.4f} | {successful_prompts[i][:10]}...")

    print(f"Matches: {matches} ({matches/total_analyzed_prompts*100:.2f}%)")
    print(f"Rejected: {rejected} ({rejected/total_analyzed_prompts*100:.2f}%)")
    
    if expected_matches:
        # For positive prompts, return the minimum similarity (worst case)
        min_similarity = min(similarities)
        print(f"Min similarity: {min_similarity:.4f}")
        return min_similarity
    else:
        # For negative prompts, return the maximum similarity (worst case)
        max_similarity = max(similarities)
        print(f"Max similarity: {max_similarity:.4f}")
        return max_similarity

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calibrate similarity threshold for a specific keyword")
    parser.add_argument("keyword", help="The keyword to calibrate the threshold for")
    args = parser.parse_args()
    
    keyword = args.keyword
    
    print(f"Calibrating similarity threshold for keyword: {keyword}")
    
    # 100% of the positive prompts should be accepted and 0% of the negative prompts should be accepted
    min_positive_similarity, max_negative_similarity = asyncio.run(main(keyword))
    
    if min_positive_similarity is None or max_negative_similarity is None:
        print("Calibration failed. Please check the prompts and try again.")
        sys.exit(1)
    
    print(f"\n=== Calibration Results ===")
    print(f"Min positive similarity: {min_positive_similarity:.4f}")
    print(f"Max negative similarity: {max_negative_similarity:.4f}")

    # Autocalibrate threshold: should be the minimum of all the positive prompts. and double check that that minimum is larger than the maximum of all the negative prompts.
    if min_positive_similarity <= max_negative_similarity:
        logger.error(f"Calibration failed: min positive similarity ({min_positive_similarity:.4f}) <= max negative similarity ({max_negative_similarity:.4f})")
        print("ERROR: The minimum positive similarity is not greater than the maximum negative similarity.")
        print("This means the current embedding model cannot reliably distinguish between positive and negative prompts for this keyword.")
        sys.exit(1)
    
    print(f"✓ Calibration successful: {min_positive_similarity:.4f} > {max_negative_similarity:.4f}")
    print(f"Saving {min_positive_similarity:.3f} as the cosine similarity threshold")
    
    # Ensure the recipe_thresholds directory exists
    thresholds_dir = current_dir / "recipe_thresholds"
    thresholds_dir.mkdir(exist_ok=True)
    
    threshold_file = thresholds_dir / f"{keyword}.json"
    with open(threshold_file, "w", encoding='utf-8') as f:
        json.dump({"cosine_similarity_threshold": round(min_positive_similarity, 3)}, f, indent=2) # save threshold to 3 decimal places
    
    print(f"Threshold saved to: {threshold_file}")
