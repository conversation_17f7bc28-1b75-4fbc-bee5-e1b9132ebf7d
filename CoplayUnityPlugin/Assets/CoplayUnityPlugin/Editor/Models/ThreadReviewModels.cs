using System;
using Newtonsoft.Json;

namespace Coplay.Models.ThreadReview
{
    [Serializable]
    public class ThreadReviewResponse
    {
        [JsonProperty("review_id")]
        public string ReviewId { get; set; }
        
        [JsonProperty("suggestion")]
        public string Suggestion { get; set; }
        
        [JsonProperty("diff_content")]
        public string DiffContent { get; set; }
        
        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }
        
        [JsonProperty("status")]
        public string Status { get; set; }
    }

    [Serializable]
    public class ThreadReviewStatusRequest
    {
        [JsonProperty("accepted")]
        public bool Accepted { get; set; }
    }

    [Serializable]
    public class ThreadReviewStatusResponse
    {
        [JsonProperty("status")]
        public string Status { get; set; }
        
        [JsonProperty("review_id")]
        public string ReviewId { get; set; }
    }

    [Serializable]
    public class ThreadShouldReviewResponse
    {
        [JsonProperty("should_review")]
        public bool ShouldReview { get; set; }
        
        [JsonProperty("review_id")]
        public string ReviewId { get; set; }
    }
}
