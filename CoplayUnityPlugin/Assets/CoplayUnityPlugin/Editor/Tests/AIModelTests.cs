using NUnit.Framework;
using Coplay.Models;
using Coplay.Views.Components;
using System;
using System.Linq;

namespace Coplay.Tests
{
    public class AIModelTests
    {
        [Test]
        public void AIModelInfo_AllModels_ShouldHaveValidData()
        {
            // Arrange & Act
            var allModels = AIModelInfo.All;

            // Assert
            Assert.IsNotNull(allModels, "All models collection should not be null");
            Assert.Greater(allModels.Count, 0, "Should have at least one model");

            foreach (var modelInfo in allModels)
            {
                Assert.IsNotNull(modelInfo.BackendId, $"Backend ID should not be null for {modelInfo.EnumValue}");
                Assert.IsNotEmpty(modelInfo.BackendId, $"Backend ID should not be empty for {modelInfo.EnumValue}");
                Assert.IsNotNull(modelInfo.DisplayName, $"Display name should not be null for {modelInfo.EnumValue}");
                Assert.IsNotEmpty(modelInfo.DisplayName, $"Display name should not be empty for {modelInfo.EnumValue}");
            }
        }

        [Test]
        public void AIModelInfo_FromEnum_ShouldReturnCorrectInfo()
        {
            // Arrange
            var model = AIModel.GPT41;

            // Act
            var modelInfo = AIModelInfo.FromEnum(model);

            // Assert
            Assert.AreEqual(AIModel.GPT41, modelInfo.EnumValue);
            Assert.AreEqual("gpt-4.1", modelInfo.BackendId);
            Assert.AreEqual("OpenAI GPT-4.1", modelInfo.DisplayName);
        }

        [Test]
        public void AIModelInfo_FromBackendId_ShouldReturnCorrectEnum()
        {
            // Arrange
            var backendId = "gpt-4.1";

            // Act
            var modelInfo = AIModelInfo.FromBackendId(backendId);

            // Assert
            Assert.AreEqual(AIModel.GPT41, modelInfo.EnumValue);
            Assert.AreEqual("gpt-4.1", modelInfo.BackendId);
            Assert.AreEqual("OpenAI GPT-4.1", modelInfo.DisplayName);
        }

        [Test]
        public void AIModelExtensions_ToModelString_ShouldReturnBackendId()
        {
            // Arrange
            var model = AIModel.Claude4Sonnet;

            // Act
            var result = model.ToModelString();

            // Assert
            Assert.AreEqual("claude-4-sonnet", result);
        }

        [Test]
        public void AIModelExtensions_ToDisplayName_ShouldReturnDisplayName()
        {
            // Arrange
            var model = AIModel.Claude4Sonnet;

            // Act
            var result = model.ToDisplayName();

            // Assert
            Assert.AreEqual("Claude 4 Sonnet", result);
        }


        [Test]
        public void AIModelExtensions_ToAIModel_ShouldReturnCorrectEnum()
        {
            // Arrange
            var backendId = "qwen3-coder";

            // Act
            var result = backendId.ToAIModel();

            // Assert
            Assert.AreEqual(AIModel.Qwen3Coder, result);
        }

        [Test]
        public void AIModelInfo_UnknownModel_ShouldThrowException()
        {
            // Arrange
            var unknownBackendId = "unknown-model-id";

            // Act & Assert
            Assert.Throws<ArgumentException>(() => AIModelInfo.FromBackendId(unknownBackendId));
        }

        [Test]
        public void AIModelExtensions_GetAllModels_ShouldIncludeAllEnumValues()
        {
            // Act
            var allModels = AIModelExtensions.GetAllModels();
            var allInfoModels = AIModelInfo.All.Select(x => x.EnumValue).ToArray();

            // Assert
            Assert.AreEqual(allInfoModels.Length, allModels.Length, "GetAllModels should return same count as AIModelInfo.All");

            foreach (var model in allInfoModels)
            {
                Assert.Contains(model, allModels, $"GetAllModels should contain {model}");
            }
        }

        [Test]
        public void AIModelExtensions_GetAllModelStrings_ShouldIncludeAllBackendIds()
        {
            // Act
            var allModelStrings = AIModelExtensions.GetAllModelStrings();
            var allInfoBackendIds = AIModelInfo.All.Select(x => x.BackendId).ToArray();

            // Assert
            Assert.AreEqual(allInfoBackendIds.Length, allModelStrings.Length, "GetAllModelStrings should return same count as AIModelInfo.All");

            foreach (var backendId in allInfoBackendIds)
            {
                Assert.Contains(backendId, allModelStrings, $"GetAllModelStrings should contain {backendId}");
            }
        }

        [Test]
        public void ModelDropdownHelper_GetDisplayName_ShouldReturnCorrectDisplayName()
        {
            // Arrange
            var backendId = "gpt-4.1";

            // Act
            var result = ModelDropdownHelper.GetDisplayName(backendId);

            // Assert
            Assert.AreEqual("OpenAI GPT-4.1", result);
        }

        [Test]
        public void ModelDropdownHelper_GetDisplayName_UnknownBackendId_ShouldReturnBackendId()
        {
            // Arrange
            var unknownBackendId = "unknown-model";

            // Act
            var result = ModelDropdownHelper.GetDisplayName(unknownBackendId);

            // Assert
            Assert.AreEqual(unknownBackendId, result);
        }

        [Test]
        public void ModelDropdownHelper_GetBackendId_ShouldReturnCorrectBackendId()
        {
            // Arrange
            var displayName = "OpenAI GPT-4.1";

            // Act
            var result = ModelDropdownHelper.GetBackendId(displayName);

            // Assert
            Assert.AreEqual("gpt-4.1", result);
        }

        [Test]
        public void ModelDropdownHelper_GetBackendId_UnknownDisplayName_ShouldReturnNull()
        {
            // Arrange
            var unknownDisplayName = "Unknown Model";

            // Act
            var result = ModelDropdownHelper.GetBackendId(unknownDisplayName);

            // Assert
            Assert.IsNull(result);
        }

        [Test]
        public void ModelDropdownHelper_DisplayNameMappings_ShouldBeConsistent()
        {
            // Arrange & Act
            foreach (var modelInfo in AIModelInfo.All)
            {
                var backendId = modelInfo.BackendId;
                var displayName = modelInfo.DisplayName;

                // Test round-trip: backend ID -> display name -> backend ID
                var retrievedDisplayName = ModelDropdownHelper.GetDisplayName(backendId);
                var retrievedBackendId = ModelDropdownHelper.GetBackendId(retrievedDisplayName);

                // Assert
                Assert.AreEqual(displayName, retrievedDisplayName, $"Display name mismatch for {backendId}");
                Assert.AreEqual(backendId, retrievedBackendId, $"Backend ID mismatch for {displayName}");
            }
        }
    }
}
