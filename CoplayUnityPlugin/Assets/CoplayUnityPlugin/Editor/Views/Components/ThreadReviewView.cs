using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine.UIElements;
using Coplay.Common;
using Coplay.Controllers.Functions.Implementations;

namespace Coplay.Views.Components
{
    /// <summary>
    /// Bottom panel view for thread review suggestions that doesn't block the UI
    /// </summary>
    public class ThreadReviewView
    {
        private readonly UIResourcesService _resourcesService;
        private VisualElement _threadReviewPanel;
        private Action<bool> _onResponseCallback;
        private Action<bool, string> _onResponseWithDiffCallback;
        private List<TextField> _editableReplaceFields = new List<TextField>();

        public ThreadReviewView(UIResourcesService resourcesService)
        {
            _resourcesService = resourcesService;
        }

        /// <summary>
        /// Creates the thread review panel that appears at the bottom of the UI
        /// </summary>
        /// <param name="parentContainer">The parent container to add the panel to</param>
        /// <returns>The created thread review panel</returns>
        public VisualElement CreateThreadReviewPanel(VisualElement parentContainer)
        {
            // Load the UXML template
            VisualTreeAsset uxml = _resourcesService.FindAsset<VisualTreeAsset>("thread_review_panel.uxml");
            if (uxml == null)
            {
                CoplayLogger.LogError("Could not find thread_review_panel.uxml");
                return null;
            }

            var instantiated = uxml.Instantiate();
            
            // The UXML root might be the actual panel, or we might need to find it
            _threadReviewPanel = instantiated.Q<VisualElement>("thread_review_panel");
            if (_threadReviewPanel == null)
            {
                // If we can't find it by name, the instantiated element might be the panel itself
                _threadReviewPanel = instantiated;
            }
            
            // Set up event handlers
            var closeButton = _threadReviewPanel.Q<Button>("thread_review_close");
            if (closeButton != null)
            {
                closeButton.clicked += () => Hide();
            }

            var rejectButton = _threadReviewPanel.Q<Button>("thread_review_reject");
            if (rejectButton != null)
            {
                rejectButton.clicked += () => HandleResponse(false);
            }

            var acceptButton = _threadReviewPanel.Q<Button>("thread_review_accept");
            if (acceptButton != null)
            {
                acceptButton.clicked += () => HandleResponse(true);
            }

            // Add to parent container
            parentContainer.Add(_threadReviewPanel);

            return _threadReviewPanel;
        }

        /// <summary>
        /// Shows the thread review panel with the specified suggestion and diff content
        /// </summary>
        /// <param name="diffContent">The diff content to display and edit</param>
        /// <param name="onResponse">Callback when user responds (accepted, edited diff content)</param>
        public void Show(string diffContent, Action<bool, string> onResponse)
        {
            if (_threadReviewPanel == null)
            {
                CoplayLogger.LogError("Thread review panel not created. Call CreateThreadReviewPanel first.");
                return;
            }

            _onResponseWithDiffCallback = onResponse;
            _editableReplaceFields.Clear();

            // Update the message
            var messageLabel = _threadReviewPanel.Q<Label>("thread_review_message");
            if (messageLabel != null)
            {
                messageLabel.text = $"Based on your previous conversation, here's a suggestion to improve future interactions. Would you like to add this to your custom rules?";
            }

            // Handle diff content display
            var diffContainer = _threadReviewPanel.Q<VisualElement>("thread_review_diff_container");
            if (diffContainer != null && !string.IsNullOrEmpty(diffContent))
            {
                DisplayDiffContent(diffContent);
                diffContainer.style.display = DisplayStyle.Flex;
            }
            else if (diffContainer != null)
            {
                diffContainer.style.display = DisplayStyle.None;
            }

            // Show the panel
            _threadReviewPanel.style.display = DisplayStyle.Flex;
        }

        /// <summary>
        /// Hides the thread review panel
        /// </summary>
        public void Hide()
        {
            if (_threadReviewPanel != null)
            {
                _threadReviewPanel.style.display = DisplayStyle.None;
                _onResponseCallback = null;
                _onResponseWithDiffCallback = null;
                _editableReplaceFields.Clear();
                CoplayLogger.Log("Thread review panel hidden");
            }
        }

        /// <summary>
        /// Handles user response (accept/reject)
        /// </summary>
        /// <param name="accepted">True if accepted, false if rejected</param>
        private void HandleResponse(bool accepted)
        {
            CoplayLogger.Log($"Thread review response: {(accepted ? "accepted" : "rejected")}");
            
            string editedDiffContent = null;
            if (accepted && _editableReplaceFields.Count > 0)
            {
                // Reconstruct the diff content with edited replace blocks
                editedDiffContent = ReconstructDiffContent();
            }
            
            // Store the callbacks before hiding (which sets them to null)
            var callback = _onResponseCallback;
            var diffCallback = _onResponseWithDiffCallback;
            
            // Hide the panel
            Hide();
            
            // Call the appropriate response callback
            if (diffCallback != null)
            {
                CoplayLogger.Log("Invoking diff response callback");
                diffCallback.Invoke(accepted, editedDiffContent);
            }
            else if (callback != null)
            {
                CoplayLogger.Log("Invoking simple response callback");
                callback.Invoke(accepted);
            }
            else
            {
                CoplayLogger.LogError("No response callback available");
            }
        }

        /// <summary>
        /// Checks if the panel is currently visible
        /// </summary>
        public bool IsVisible => _threadReviewPanel?.style.display == DisplayStyle.Flex;

        /// <summary>
        /// Displays the diff content with editable replace blocks
        /// </summary>
        /// <param name="diffContent">The diff content to parse and display</param>
        private void DisplayDiffContent(string diffContent)
        {
            var diffContentContainer = _threadReviewPanel.Q<VisualElement>("thread_review_diff_content");
            if (diffContentContainer == null)
            {
                CoplayLogger.LogError("Could not find thread_review_diff_content container");
                return;
            }

            // Clear existing content
            diffContentContainer.Clear();
            _editableReplaceFields.Clear();

            // Parse the diff content using regex to find SEARCH/REPLACE blocks
            var diffBlocks = ParseDiffContent(diffContent);

            foreach (var block in diffBlocks)
            {
                // Create a container for this diff block
                var blockContainer = new VisualElement();
                blockContainer.AddToClassList("diff-block");

                // Add SEARCH section (read-only)
                var searchLabel = new Label("SEARCH:");
                searchLabel.AddToClassList("diff-section-label");
                blockContainer.Add(searchLabel);

                var searchField = new TextField { multiline = true, isReadOnly = true };
                searchField.value = block.SearchContent;
                searchField.AddToClassList("diff-search-field");
                blockContainer.Add(searchField);

                // Add REPLACE section (editable)
                var replaceLabel = new Label("REPLACE:");
                replaceLabel.AddToClassList("diff-section-label");
                blockContainer.Add(replaceLabel);

                var replaceField = new TextField { multiline = true };
                replaceField.value = block.ReplaceContent;
                replaceField.AddToClassList("diff-replace-field");
                blockContainer.Add(replaceField);

                // Store the editable field for later reconstruction
                _editableReplaceFields.Add(replaceField);

                diffContentContainer.Add(blockContainer);
            }
        }

        /// <summary>
        /// Parses diff content into search/replace blocks using string splitting instead of regex
        /// </summary>
        /// <param name="diffContent">The raw diff content</param>
        /// <returns>List of parsed diff blocks</returns>
        private List<DiffBlock> ParseDiffContent(string diffContent)
        {
            var blocks = new List<DiffBlock>();

            if (string.IsNullOrEmpty(diffContent))
            {
                return blocks;
            }

            // Use the constants from FileFunctions to ensure consistency
            var searchMarker = FileFunctions.SearchMarkerStart;
            var separator = FileFunctions.SeparatorMarker;
            var replaceMarker = FileFunctions.ReplaceMarkerEnd;
            var currentIndex = 0;

            while (true)
            {
                var searchMarkerIndex = diffContent.IndexOf(searchMarker, currentIndex, StringComparison.Ordinal);
                if (searchMarkerIndex == -1)
                    break;

                var contentStartIndex = searchMarkerIndex + searchMarker.Length - 1; // We need the minus one in cases where the search content is empty

                // Find the separator between search and replace content
                var separatorIndex = diffContent.IndexOf(separator, contentStartIndex, StringComparison.Ordinal);
                if (separatorIndex == -1)
                {
                    CoplayLogger.LogWarning($"No separator found after search marker at {searchMarkerIndex}");
                    currentIndex = contentStartIndex;
                    continue;
                }

                // Find the replace marker
                var replaceMarkerIndex = diffContent.IndexOf(replaceMarker, separatorIndex, StringComparison.Ordinal);
                if (replaceMarkerIndex == -1)
                {
                    CoplayLogger.LogWarning($"No replace marker found after separator at {separatorIndex}");
                    currentIndex = contentStartIndex;
                    continue;
                }

                // Extract search content (between search marker and separator)
                var searchContent = diffContent.Substring(contentStartIndex, separatorIndex - contentStartIndex).Trim();
                
                // Extract replace content (between separator and replace marker)
                var replaceStartIndex = separatorIndex + separator.Length;
                // Skip any newlines after separator
                while (replaceStartIndex < diffContent.Length && char.IsWhiteSpace(diffContent[replaceStartIndex]))
                {
                    replaceStartIndex++;
                }
                
                // Find the actual end of replace content (before the replace marker, trimming whitespace)
                var replaceEndIndex = replaceMarkerIndex;
                while (replaceEndIndex > replaceStartIndex && char.IsWhiteSpace(diffContent[replaceEndIndex - 1]))
                {
                    replaceEndIndex--;
                }
                
                var replaceContent = replaceStartIndex < replaceEndIndex 
                    ? diffContent.Substring(replaceStartIndex, replaceEndIndex - replaceStartIndex)
                    : "";

                blocks.Add(new DiffBlock
                {
                    SearchContent = searchContent,
                    ReplaceContent = replaceContent
                });

                // Move to the next potential block
                currentIndex = replaceMarkerIndex + replaceMarker.Length;
            }

            // If no blocks found, treat the entire content as a single replace block
            if (blocks.Count == 0 && !string.IsNullOrEmpty(diffContent))
            {
                CoplayLogger.LogWarning("No blocks found in diff content: " + diffContent);
                blocks.Add(new DiffBlock
                {
                    SearchContent = "",
                    ReplaceContent = diffContent.Trim()
                });
            }

            return blocks;
        }

        /// <summary>
        /// Reconstructs the diff content with edited replace blocks
        /// </summary>
        /// <returns>The reconstructed diff content</returns>
        private string ReconstructDiffContent()
        {
            var diffContentContainer = _threadReviewPanel.Q<VisualElement>("thread_review_diff_content");
            if (diffContentContainer == null || _editableReplaceFields.Count == 0)
            {
                return null;
            }

            var reconstructedBlocks = new List<string>();
            var blockContainers = diffContentContainer.Children().ToList();

            for (int i = 0; i < blockContainers.Count && i < _editableReplaceFields.Count; i++)
            {
                var blockContainer = blockContainers[i];
                var searchField = blockContainer.Q<TextField>("diff-search-field") ?? 
                                 blockContainer.Children().OfType<TextField>().FirstOrDefault(f => f.isReadOnly);
                var replaceField = _editableReplaceFields[i];

                if (searchField != null && replaceField != null)
                {
                    var searchContent = searchField.value ?? "";
                    var replaceContent = replaceField.value ?? "";

                    var block = $"{FileFunctions.SearchMarkerStart}{searchContent}{FileFunctions.SeparatorMarker}{replaceContent}{FileFunctions.ReplaceMarkerEnd}";
                    reconstructedBlocks.Add(block);
                }
            }

            return string.Join("\n\n", reconstructedBlocks);
        }

        /// <summary>
        /// Represents a single diff block with search and replace content
        /// </summary>
        private class DiffBlock
        {
            public string SearchContent { get; set; }
            public string ReplaceContent { get; set; }
        }
    }
}
