/**
 * Validates that a URL is safe to use as a base URL
 */
function isValidBaseUrl(url: string): boolean {
  try {
    const parsed = new URL(url);
    // Only allow HTTP and HTTPS protocols
    return parsed.protocol === 'http:' || parsed.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Validates that a device ID is in a safe format
 */
function isValidDeviceId(deviceId: string): boolean {
  if (!deviceId || typeof deviceId !== 'string') return false;
  if (deviceId.length === 0 || deviceId.length > 100) return false;

  // Device IDs should only contain safe characters
  const deviceIdRegex = /^[a-zA-Z0-9_-]+$/;
  return deviceIdRegex.test(deviceId);
}

/**
 * Sanitizes error messages from API responses
 */
function sanitizeApiError(error: unknown): string {
  if (typeof error === 'string') {
    return error.substring(0, 200); // Limit length
  }
  if (error instanceof Error) {
    return error.message.substring(0, 200);
  }
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;
    if (typeof errorObj.message === 'string') {
      return errorObj.message.substring(0, 200);
    }
  }
  return "An error occurred";
}

/**
 * Fetches device information by device ID from the API
 */
export async function getDevice(deviceId: string) {
  try {
    // Validate device ID
    if (!isValidDeviceId(deviceId)) {
      return {
        device: null,
        error: "Invalid device ID",
      };
    }

    // Get and validate base URL
    const rawBaseUrl = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000";
    if (!isValidBaseUrl(rawBaseUrl)) {
      console.warn("Invalid base URL detected:", rawBaseUrl);
      return {
        device: null,
        error: "Configuration error. Please contact support.",
      };
    }

    // Construct URL safely using URL constructor to prevent CRLF injection
    const apiUrl = new URL(`api/device/${encodeURIComponent(deviceId)}`, rawBaseUrl);

    const response = await fetch(apiUrl.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        device: null,
        error: sanitizeApiError(errorData.error) || "Device not found",
      };
    }

    const data = await response.json();
    return { device: data.device, error: null };
  } catch (error) {
    console.error("Error fetching device:", error);
    return {
      device: null,
      error: "We can't process your login. Please try again at a later time.",
    };
  }
}
