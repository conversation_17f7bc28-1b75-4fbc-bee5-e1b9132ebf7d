Assets\\Scripts\\TerrainGenerator.cs(382,14): error CS0101: The namespace \u0027\u003cglobal namespace\u003e\u0027 already contains a definition for \u0027MeshData\u0027\n
instead on terrain materials switch to terrain layer in the chuncked terrain generator
instead on terrain materials switch to terrain layer
I dont want to ever manually input things in inspector. Please change that and continue building the RTS game. I want a 2D terrain (512x512) and 3D models. We can use cubes as placeholders for units/buildings.
NullReferenceException: Object reference not set to an instance of an object\nKrytallis.Terrain.TerrainGizmoDrawer.DrawSpawnPoints () (at Assets/Scripts/Terrain/TerrainGizmoDrawer.cs:204)\nKrytallis.Terrain.TerrainGizmoDrawer.OnDrawGizmos () (at Assets/Scripts/Terrain/TerrainGizmoDrawer.cs:110)\nUnityEngine.GUIUtility:ProcessEvent(Int32, IntPtr, Boolean\u0026)\n\r\n
We have a bunch in the Assets/TerrainTextures, take a look.
The resources need to be aligned on the terrain, i need to be able to see them in the preview gizmo
Warcraft 3 style perhaps, but this terrain needs to be massive in scope, so we need to reconsider that approach too. I also want a way to preview it, as we need to be able to house 100 players on 1 single map with resources, it should not feel too cramped.
I removed it i didnt like it.Can you delete our terrain generation stuff? Assets\\Scripts\\TerrainGeneratorController.cs(2,7): error CS0246: The type or namespace name \u0027Assets\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\nAssets\\Scripts\\TerrainInitializer.cs(2,7): error CS0246: The type or namespace name \u0027Assets\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\nAssets\\Scripts\\TerrainGeneratorController.cs(6,13): error CS0246: The type or namespace name \u0027HeightsGenerator\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\nAssets\\Scripts\\TerrainGeneratorController.cs(7,13): error CS0246: The type or namespace name \u0027TexturesGenerator\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\nAssets\\Scripts\\TerrainGeneratorController.cs(8,13): error CS0246: The type or namespace name \u0027GrassGenerator\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\nAssets\\Scripts\\TerrainGeneratorController.cs(9,13): error CS0246: The type or namespace name \u0027TreeGenerator\u0027 could not be found (are you missing a using directive or an assembly reference?)\n\r\n
the terrain is a bit too bouncy, and in the editor view i am unable to preview the terrain. Maybe you can make a gizmo that will preview the terrain, and i can see arrows pointing down to spawn points for the RTS game and resources so we can get a bit of a overview?
We have a terrain generator and a grid system, but in the editor view i am not able to preview spawn points and resource placements. Can you take a look at it and refactor it appropriately? We are developing a MMORTS game server authoritive. The terrain is showing and so is the grid but no spawns or resources can be found on top of the terrain. Start by investigating the current setup.
NullReferenceException: Object reference not set to an instance of an object\nKrytallis.Terrain.TerrainGizmoDrawer.DrawSpawnPoints () (at Assets/Scripts/Terrain/TerrainGizmoDrawer.cs:193)\nKrytallis.Terrain.TerrainGizmoDrawer.OnDrawGizmos () (at Assets/Scripts/Terrain/TerrainGizmoDrawer.cs:99)\nUnityEngine.GUIUtility:ProcessEvent(Int32, IntPtr, Boolean\u0026)\n\r\n
Create a RTS game with no animations, make the terrain 2D sprite.
I applied the BeachSandMaterial to the basic terrain portion of the terrain object and am receiving the warning shown in the attached image.