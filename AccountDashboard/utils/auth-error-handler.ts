import { createClient } from "@/lib/supabase/client";

/**
 * Type for error objects that might be authentication-related
 */
type AuthError = {
  message?: string;
  error_description?: string;
  status?: number;
  __isAuthError?: boolean;
} | string | Error;

/**
 * Checks if an error is related to invalid refresh tokens
 */
export function isRefreshTokenError(error: unknown): boolean {
  if (!error) {
    return false;
  }

  // Handle non-object, non-string types
  if (typeof error !== 'string' && typeof error !== 'object') {
    return false;
  }

  // Handle arrays specifically
  if (Array.isArray(error)) {
    return false;
  }

  // Type guard for auth error objects
  const isAuthErrorObject = (obj: object): obj is Record<string, unknown> => {
    return obj !== null && typeof obj === 'object' && !Array.isArray(obj);
  };

  // Check for special auth error conditions first
  if (typeof error === 'object' && error !== null && isAuthErrorObject(error)) {
    const authError = error as Record<string, unknown>;
    if (authError.__isAuthError === true && authError.status === 401) {
      return true;
    }
  }

  // Extract error messages - check both message and error_description
  const messagesToCheck: string[] = [];
  if (typeof error === 'string') {
    messagesToCheck.push(error);
  } else if (error && typeof error === 'object' && isAuthErrorObject(error)) {
    const errorObj = error as Record<string, unknown>;
    if (typeof errorObj.message === 'string' && errorObj.message !== '') {
      messagesToCheck.push(errorObj.message);
    }
    if (typeof errorObj.error_description === 'string' && errorObj.error_description !== '') {
      messagesToCheck.push(errorObj.error_description);
    }
  }

  // If no string messages are available, check for specific 400 + refresh case
  if (messagesToCheck.length === 0) {
    if (typeof error === 'object' && error !== null && isAuthErrorObject(error)) {
      const errorObj = error as Record<string, unknown>;
      if (errorObj.status === 400 &&
        typeof errorObj.message === 'string' &&
        errorObj.message.toLowerCase().includes('refresh')) {
        return true;
      }
    }
    return false;
  }

  // Check all available messages for refresh token error patterns
  for (const message of messagesToCheck) {
    const lowerMessage = message.toLowerCase();

    const hasRefreshTokenError = lowerMessage.includes('refresh token not found') ||
      lowerMessage.includes('invalid refresh token') ||
      lowerMessage.includes('session expired') ||
      lowerMessage.includes('refresh_token_not_found');

    // Check for 400 status with refresh in message
    const has400RefreshError = typeof error === 'object' &&
      error !== null &&
      isAuthErrorObject(error) &&
      (error as Record<string, unknown>).status === 400 &&
      lowerMessage.includes('refresh');

    if (hasRefreshTokenError || has400RefreshError) {
      return true;
    }
  }

  return false;
}

/**
 * Handles authentication errors by clearing sessions and redirecting
 */
export async function handleAuthError(error: unknown, redirectToLogin = true): Promise<void> {
  if (!isRefreshTokenError(error)) {
    return;
  }

  // Extract error message safely for logging - keep original error object
  const getErrorMessage = (error: unknown): unknown => {
    if (typeof error === 'string') return error;
    if (error instanceof Error) return error.message;
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;
      if (typeof errorObj.message === 'string') return errorObj.message;
    }
    return error; // Return original error object instead of converting to string
  };

  console.warn("Authentication error detected, clearing session:", getErrorMessage(error));

  // Clear the invalid session
  try {
    const supabase = createClient();
    await supabase.auth.signOut();
  } catch (signOutError) {
    console.warn("Failed to sign out during auth error cleanup:", signOutError);
  }

  // Redirect to login if requested and we're in a browser environment
  if (redirectToLogin && typeof window !== 'undefined') {
    try {
      window.location.href = '/login';
    } catch (redirectError) {
      // Gracefully handle redirect failures (e.g., in test environments)
      console.warn("Failed to redirect during auth error cleanup:", redirectError);
    }
  }
}

/**
 * Wrapper for fetch requests that automatically handles auth errors
 */
export async function fetchWithAuthErrorHandling(
  url: string,
  options?: RequestInit
): Promise<Response> {
  try {
    const response = await fetch(url, options);

    // Check for auth errors in response
    if (!response.ok && response.status === 401) {
      try {
        const errorData = await response.json();

        if (isRefreshTokenError(errorData.error)) {
          await handleAuthError(errorData.error);
          throw new Error("Session expired. Please log in again.");
        }
      } catch (jsonError: unknown) {
        // If JSON parsing fails, just return the response
        if (jsonError instanceof Error && jsonError.message === "Session expired. Please log in again.") {
          throw jsonError; // Re-throw our custom error
        }
        // For other JSON parsing errors, return the response as-is
        return response;
      }
    }

    return response;
  } catch (error: unknown) {
    // Handle auth errors that might be thrown during fetch
    // But avoid double-handling if we already handled it above
    const errorMessage = error instanceof Error ? error.message : '';
    if (errorMessage !== "Session expired. Please log in again." && isRefreshTokenError(error)) {
      await handleAuthError(error);
    }
    throw error;
  }
}
