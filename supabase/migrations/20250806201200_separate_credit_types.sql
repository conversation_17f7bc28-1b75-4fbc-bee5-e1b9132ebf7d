-- Modify credits table to separate subscription and top-up credits
-- Drop the old credit_balance column and add two new columns (only if needed)

DO $$
BEGIN
    -- Add subscription_credits column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'credits' 
        AND column_name = 'subscription_credits'
    ) THEN
        ALTER TABLE public.credits ADD COLUMN subscription_credits decimal(10,4) NOT NULL DEFAULT 0.0;
    END IF;

    -- Add topup_credits column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'credits' 
        AND column_name = 'topup_credits'
    ) THEN
        ALTER TABLE public.credits ADD COLUMN topup_credits decimal(10,4) NOT NULL DEFAULT 0.0;
    END IF;

    -- Drop credit_balance column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'credits' 
        AND column_name = 'credit_balance'
    ) THEN
        ALTER TABLE public.credits DROP COLUMN credit_balance;
    END IF;
END $$;

-- Update the comment on the table
COMMENT ON TABLE public.credits IS 'User credit balances with separate tracking for subscription and top-up credits';
COMMENT ON COLUMN public.credits.subscription_credits IS 'Monthly subscription credits (reset to $40 each month for active subscribers)';
COMMENT ON COLUMN public.credits.topup_credits IS 'Persistent credits from manual/automatic top-ups';
