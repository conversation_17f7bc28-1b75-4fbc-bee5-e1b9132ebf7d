import { useMemo } from "react";
import { usePathname } from "next/navigation";
import { Session } from "@supabase/supabase-js";
import { isAuthPage } from "@/services/authService";

export interface UseSidebarVisibilityReturn {
  shouldShowSidebar: boolean;
  isAuthPage: boolean;
  isAuthenticated: boolean;
}

/**
 * Custom hook to determine sidebar visibility
 * Based on authentication state and current page
 */
export function useSidebarVisibility(session: Session | null): UseSidebarVisibilityReturn {
  const pathname = usePathname();

  const visibility = useMemo(() => {
    const isCurrentAuthPage = isAuthPage(pathname);
    const isAuthenticated = Boolean(session);
    const shouldShowSidebar = !isCurrentAuthPage && isAuthenticated;

    return {
      shouldShowSidebar,
      isAuthPage: isCurrentAuthPage,
      isAuthenticated,
    };
  }, [pathname, session]);

  return visibility;
}
