using System;
using System.IO;
using System.IO.Abstractions;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Newtonsoft.Json;

using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Common.Exceptions;
using Coplay.Controllers.Functions;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Reactive;
using Coplay.Models;
using Coplay.Models.Assistants;
using Coplay.Models.Configuration;
using Coplay.Views.Components;
using Microsoft.Extensions.Logging;

namespace Coplay.Services.Chat
{
    public class ChatThreadsState
    {
        public CoplayThread CurrentThread;
        public List<CoplayThread> Threads;
        public List<ChatMessage> Messages;
        public bool IsOrchestratorMode;
    }

    public class ChatThreads : IChatThreads
    {
        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.EscapeHtml,
            DefaultValueHandling = DefaultValueHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore,
        };

        private readonly ICoplayApiClient _coplayApiClient;
        private readonly IApplicationPaths _applicationPaths;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly ChatHistoryRepository _chatHistory;
        private readonly IFileSystem _fileSystem;
        private readonly ThreadReviewService _threadReviewService;
        private readonly CompositeDisposable _disposables = new();
        private readonly SemaphoreSlim _loadingThreadsLock = new(1, 1);
        private readonly SemaphoreSlim _loadingThreadMessagesLock = new(1, 1);
        private readonly SemaphoreSlim _saveStateLock = new(1, 1);

        private List<CoplayThread> _threads = new();
        public IReadOnlyList<CoplayThread> Threads => _threads;
        public bool IsLoadingThreads { get; private set; } = true;
        public bool IsDeletingThread { get; private set; } = false;
        public bool IsLoadingThreadMessages { get; private set; } = true;
        public bool IsCreatingThread { get; private set; } = false;
        public bool IsDoingSomething => IsLoadingThreads || IsDeletingThread || IsLoadingThreadMessages || IsCreatingThread;
        private CoplayThread _currentThread;
        private bool _isOrchestratorMode;
        public CoplayThread CurrentThread => _currentThread;
        private Dictionary<string, List<ChatMessage>> _cachedThreadMessages = new();

        private readonly ReplaySubject<IReadOnlyList<CoplayThread>> _threadsLoadedSubject = new();
        public IObservable<IReadOnlyList<CoplayThread>> ThreadsLoadedObservable => _threadsLoadedSubject.AsObservable();

        private readonly ReplaySubject<CoplayThread> _threadLoadedSubject = new();
        public IObservable<CoplayThread> ThreadLoadedObservable => _threadLoadedSubject.AsObservable();

        private readonly Subject<Unit> _loadingThreadSubject = new();
        public IObservable<Unit> LoadingThreadObservable => _loadingThreadSubject.AsObservable();

        private readonly Subject<Unit> _creatingThreadSubject = new();
        public IObservable<Unit> CreatingThreadObservable => _creatingThreadSubject.AsObservable();

        private readonly Subject<Unit> _threadCreatedSubject = new();
        public IObservable<Unit> ThreadCreatedObservable => _threadCreatedSubject.AsObservable();

        public ChatThreads(
            ILogger<ChatThreads> logger,
            ICoplayApiClient coplayApiClient,
            IApplicationPaths applicationPaths,
            AssistantConfiguration assistantConfiguration,
            ChatHistoryRepository chatHistory,
            IFileSystem fileSystem,
            ThreadReviewService threadReviewService)
        {
            _coplayApiClient = coplayApiClient;
            _applicationPaths = applicationPaths;
            _assistantConfiguration = assistantConfiguration;
            _chatHistory = chatHistory;
            _fileSystem = fileSystem;
            _threadReviewService = threadReviewService;

            _chatHistory.MessageAdded
                .Merge(_chatHistory.MessageUpdated)
                .Throttle(TimeSpan.FromSeconds(1))
                .Select(_ => Observable.FromAsync(() => SaveState()))
                .Switch()
                .Finally(() => Task.Run(() => SaveState()).Wait())
                .Subscribe(_ => { }, ex => logger.LogError(ex, $"Unhandled error in {nameof(SaveState)}"))
                .AddTo(_disposables);
        }

        public void Dispose()
        {
            _disposables.Dispose();
            _threadLoadedSubject.Dispose();
            _threadsLoadedSubject.Dispose();
            _loadingThreadSubject.Dispose();
            _creatingThreadSubject.Dispose();
            _threadCreatedSubject.Dispose();
            _saveStateLock.Dispose();
            _loadingThreadsLock.Dispose();
            _loadingThreadMessagesLock.Dispose();
        }

        public async Task LoadState()
        {
            try
            {
                if (!_fileSystem.File.Exists(_applicationPaths.ChatThreadsPath))
                {
                    await LoadThreads();
                }
                else
                {
                    CoplayLogger.Log($"Loading threads state");
                    await _saveStateLock.WaitAsync();
                    string json;
                    try
                    {
                        json = await _fileSystem.File.ReadAllTextAsync(_applicationPaths.ChatThreadsPath);
                    }
                    finally
                    {
                        _saveStateLock.Release();
                    }
                    
                    ChatThreadsState state = null;
                    try
                    {
                        state = JsonConvert.DeserializeObject<ChatThreadsState>(json, JsonSettings);
                    }
                    catch (JsonException jsonEx)
                    {
                        CoplayLogger.LogError($"JSON corruption detected in chat threads state file. This is often caused by Windows file path serialization issues. Error: {jsonEx.Message}", jsonEx);
                        await HandleCorruptedStateFile();
                        return;
                    }
                    
                    if (state?.CurrentThread == null || state?.Threads == null || state.Threads.Count == 0) {
                        CoplayLogger.Log("State file exists but contains invalid data, loading threads from server");
                        await LoadThreads();
                    }
                    else
                    {
                        _currentThread = state.CurrentThread;

                        _threads = state.Threads;
                        _isOrchestratorMode = state.IsOrchestratorMode;
                        IsLoadingThreads = false;
                        _threadsLoadedSubject.OnNext(_threads.AsReadOnly());

                        _cachedThreadMessages[_currentThread.id] = state.Messages;
                        await LoadThreadMessages(_currentThread.id);
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to load state", ex);
                // If we encounter any other error, try to recover by loading from server
                try
                {
                    await LoadThreads();
                }
                catch (Exception recoveryEx)
                {
                    CoplayLogger.LogError("Failed to recover from state loading error", recoveryEx);
                }
            }
        }

        private async Task SaveState(bool force = false)
        {
            if (_currentThread == null && !force)
            {
                return;
            }

            await _saveStateLock.WaitAsync();
            try
            {
                var state = new ChatThreadsState
                {
                    CurrentThread = _currentThread,
                    Threads = _threads,
                    Messages = _chatHistory.Messages.ToList(),
                    IsOrchestratorMode = _isOrchestratorMode
                };
                CoplayLogger.Log($"Saving state");
                // Use atomic write to prevent corruption
                await SaveStateAtomically(state);
            }
            catch (Exception ex)
            {
                string message = $"{ex.GetType()}: {ex.Message}\n{ex.StackTrace}";

                // Include inner exception details if present
                var innerException = ex.InnerException;
                while (innerException != null)
                {
                    message += $"\n\nCaused by: {innerException.GetType()}: {innerException.Message}\n{innerException.StackTrace}";
                    innerException = innerException.InnerException;
                }
                CoplayLogger.LogWarning($"Failed to save state: {message}");
            }
            finally
            {
                _saveStateLock.Release();
            }
        }

        /// <summary>
        /// Saves state atomically by writing to a temporary file first, then renaming.
        /// This prevents corruption if the write operation is interrupted.
        /// </summary>
        private async Task SaveStateAtomically(ChatThreadsState state)
        {
            var tempPath = _applicationPaths.ChatThreadsPath + ".tmp";
            
            try
            {
                var json = JsonConvert.SerializeObject(state, JsonSettings);
                
                // Write to temporary file first
                await _fileSystem.File.WriteAllTextAsync(tempPath, json);

                // Use File.Replace for an atomic operation, which is safer than Delete+Move.
                _fileSystem.File.Replace(tempPath, _applicationPaths.ChatThreadsPath, null);
            }
            catch (FileNotFoundException)
            {
                // If the destination file doesn't exist, File.Replace throws. We can just move the temp file.
                _fileSystem.File.Move(tempPath, _applicationPaths.ChatThreadsPath);
            }
            catch (Exception ex)
            {
                // Clean up temp file if it exists
                if (_fileSystem.File.Exists(tempPath))
                {
                    try
                    {
                        _fileSystem.File.Delete(tempPath);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
                throw new Exception($"Failed to save state atomically: {ex.Message}", ex);
            }
        }

        public async Task ClearState()
        {
            _cachedThreadMessages.Clear();
            _threads.Clear();
            _currentThread = null;
            _chatHistory.Clear();
            await SaveState(true);
        }

        public async Task CreateThread(CancellationToken cancellationToken = default, bool overrideToNormalMode = false)
        {
            CoplayLogger.Log($"Creating thread");
            IsCreatingThread = true;
            try
            {
                _creatingThreadSubject.OnNext(Unit.Default);
                var effectiveMode = _assistantConfiguration.Mode;
                _isOrchestratorMode = false;
                if (overrideToNormalMode) {
                    _isOrchestratorMode = true;
                    effectiveMode = AssistantMode.Normal;
                }
                var response = await _coplayApiClient.CreateAssistant(effectiveMode, cancellationToken);
                _threads.Insert(0, response);
                _cachedThreadMessages[response.id] = new List<ChatMessage>();
                await SaveState();
                await LoadThreadMessages(response.id, cancellationToken);
                IsCreatingThread = false;
                _threadsLoadedSubject.OnNext(_threads.AsReadOnly());
                _threadCreatedSubject.OnNext(Unit.Default);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to create thread", ex);
            }
            finally
            {
                IsCreatingThread = false;
            }
        }

        public async Task UpdateThread(string threadId, string name, AssistantMode mode, CancellationToken cancellationToken = default)
        {
            CoplayLogger.Log($"Updating thread {threadId} to {name}");
            var updatedThread = await _coplayApiClient.UpdateThread(threadId, name, mode, cancellationToken);
            var thread = _threads.FirstOrDefault(t => t.id == threadId);
            if (thread != null)
            {
                thread.name = updatedThread.name;
                thread.assistant_mode = updatedThread.assistant_mode;
                _threadLoadedSubject.OnNext(thread);
                _threadsLoadedSubject.OnNext(_threads.AsReadOnly());
            }
            await SaveState();
        }

        public async Task DeleteThread(CoplayThread thread, CancellationToken cancellationToken = default)
        {
            CoplayLogger.Log($"Deleting thread {thread.id}");
            IsDeletingThread = true;
            try
            {
                await _coplayApiClient.DeleteThread(thread.id, cancellationToken);
                _threads.Remove(thread);
                await SaveState();
                _cachedThreadMessages.Remove(thread.id);
                IsDeletingThread = false;
                _threadsLoadedSubject.OnNext(_threads.AsReadOnly());

                if (_currentThread.id == thread.id)
                {
                    var newThread = _threads.FirstOrDefault(t => t.assistant_mode != AssistantMode.OneShotSceneGen);
                    if (newThread != null)
                    {
                        await LoadThreadMessages(newThread.id, cancellationToken);
                    }
                    else
                    {
                        await CreateThread(cancellationToken);
                    }
                }
            }
            finally
            {
                IsDeletingThread = false;
            }
        }

        public async Task LoadThreads(string threadId = null, CancellationToken cancellationToken = default)
        {
            CoplayLogger.Log($"Loading threads");
            await _loadingThreadsLock.WaitAsync(cancellationToken);
            IsLoadingThreads = true;
            try
            {
                var threads = await _coplayApiClient.GetThreads(cancellationToken);
                _threads.Clear();
                _threads.AddRange(threads);

                if (_threads.Count > 0)
                {
                    if (threadId == null || !_threads.Any(t => t.id == threadId))
                    {
                        threadId = _threads.FirstOrDefault()?.id;
                    }

                    await LoadThreadMessages(threadId, cancellationToken);
                }
                else
                {
                    await CreateThread(cancellationToken);
                }
            }
            finally
            {
                IsLoadingThreads = false;

                if (_loadingThreadsLock.CurrentCount == 0)
                    _loadingThreadsLock.Release();
            }
            _threadsLoadedSubject.OnNext(_threads.AsReadOnly());
        }

        public async Task LoadThreadMessages(string threadId, CancellationToken cancellationToken = default, bool avoidCache = false)
        {
            CoplayLogger.Log($"Loading thread messages for thread {threadId}");
            await _loadingThreadMessagesLock.WaitAsync(cancellationToken);
            IsLoadingThreadMessages = true;
            _loadingThreadSubject.OnNext(Unit.Default);
            try
            {
                var thread = _threads.FirstOrDefault(t => t.id == threadId);
                if (thread != null)
                {
                    if (_currentThread?.id != thread.id)
                    {
                        if (_currentThread != null)
                        {
                            _cachedThreadMessages[_currentThread.id] = _chatHistory.Messages.ToList();
                        }
                        _currentThread = thread;
                    }

                    _chatHistory.Clear();
                    if (!_isOrchestratorMode) {
                        _assistantConfiguration.SetMode(thread.assistant_mode);
                    }

                    // In case last message was mine - load thread messages from server
                    if (_cachedThreadMessages.TryGetValue(thread.id, out var cachedMessages) && !avoidCache)
                    {
                        _chatHistory.AddMessages(cachedMessages, false);
                    }
                    else
                    {
                        try
                        {
                            var messages = await _coplayApiClient.GetThreadMessages(threadId, cancellationToken);
                            var chatMessages = new List<ChatMessage>();
                            for (var i = 0; i < messages.Count; i++)
                            {
                                var message = messages[i];
                                if (message.type == "ai")
                                {
                                    var chatMessage = new ChatMessage(message.message, id: message.message_id, costUsd: message.cost);
                                    var functionCalls = new List<FunctionCall>();
                                    foreach (var toolCall in message.tool_calls)
                                    {
                                        var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, message.message_id);
                                        // FunctionExecutionController will add these to the queue.
                                        functionCalls.Add(functionCall);
                                    }
                                    chatMessage.FunctionCalls = functionCalls;
                                    chatMessages.Add(chatMessage);
                                }
                                else if (message.type == "error")
                                {
                                    chatMessages.Add(new ChatMessage(message.message, id: message.message_id));
                                }
                                else
                                {
                                    chatMessages.Add(new ChatMessage(message.message, ChatMessageRole.You));
                                }
                            }
                            _chatHistory.AddMessages(chatMessages, false);
                        }
                        catch (ThreadNotFoundException ex)
                        {
                            await HandleMissingThread(threadId, cancellationToken);
                            return;
                        }
                    }
                    await SaveState();
                }
            }
            finally
            {
                // Ensure we do not release the semaphore twice if it was already
                // released inside the 404 handler.
                if (_loadingThreadMessagesLock.CurrentCount == 0)
                {
                    _loadingThreadMessagesLock.Release();
                }
                IsLoadingThreadMessages = false;

                if (_loadingThreadMessagesLock.CurrentCount == 0)
                    _loadingThreadMessagesLock.Release();
            }
            _threadLoadedSubject.OnNext(_currentThread);
        }

        /// <summary>
        /// Handles corrupted state file by backing it up and loading fresh state from server.
        /// </summary>
        private async Task HandleCorruptedStateFile()
        {
            try
            {
                // Create backup of corrupted file for debugging
                var backupPath = _applicationPaths.ChatThreadsPath + ".corrupted." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                if (_fileSystem.File.Exists(_applicationPaths.ChatThreadsPath))
                {
                    _fileSystem.File.Copy(_applicationPaths.ChatThreadsPath, backupPath);
                    CoplayLogger.Log($"Backed up corrupted state file to: {backupPath}");
                }

                // Clear the corrupted state and load fresh from server
                await ClearState();
                await LoadThreads();
                
                CoplayLogger.Log("Successfully recovered from corrupted state file by loading fresh data from server");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to handle corrupted state file", ex);
                // If backup/recovery fails, still try to load threads
                try
                {
                    await LoadThreads();
                }
                catch (Exception recoveryEx)
                {
                    CoplayLogger.LogError("Failed final recovery attempt", recoveryEx);
                }
            }
        }

        /// <summary>
        /// Checks for thread reviews for the current thread and shows popup if available
        /// </summary>
        /// <param name="chatView">The ChatView to show the review in</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CheckForThreadReview(ChatView chatView, CancellationToken cancellationToken = default)
        {
            if (_currentThread != null && _threadReviewService != null && chatView != null)
            {
                try
                {
                    await _threadReviewService.CheckAndShowThreadReview(_currentThread.id, chatView, cancellationToken);
                }
                catch (Exception ex)
                {
                    CoplayLogger.LogError($"Error checking for thread review: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Handles the case where a thread is not found on the server.
        /// </summary>
        /// <param name="missingThreadId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task HandleMissingThread(string missingThreadId, CancellationToken cancellationToken)
        {
            CoplayLogger.LogWarning($"Thread {missingThreadId} not found on server (404). Clearing local state.");
            _loadingThreadMessagesLock.Release();
            await ClearState();
            await LoadState();
        }
    }
}
