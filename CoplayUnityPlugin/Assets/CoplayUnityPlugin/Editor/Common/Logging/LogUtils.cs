using System.Text.RegularExpressions;

namespace Coplay.Common.Logging
{
    public static class LogUtils
    {
        public static string SanitizeForLogging(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                return message;
            }

            // This pattern specifically looks for "base64," followed by a long string of characters
            // that are typical for base64 encoding, and truncates it for logging purposes.
            const string pattern = @"(base64,)([^""}""']{100})[^""}""']*";
            var match = Regex.Match(message, pattern);

            if (match.Success)
            {
                // Return the original string with the base64 part truncated.
                return message.Substring(0, match.Groups[2].Index + 100) + "...\"";
            }

            return message;
        }
    }
}
