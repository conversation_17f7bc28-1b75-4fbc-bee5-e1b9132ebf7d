using System;
using Newtonsoft.Json;

namespace Coplay.Common.CoplayApi.Models
{
    /// <summary>
    /// Model for auto top-up settings
    /// </summary>
    [Serializable]
    public class AutoTopUpSettings
    {
        /// <summary>
        /// Whether auto top-up is enabled
        /// </summary>
        [JsonProperty("enabled")]
        public bool Enabled { get; set; }

        /// <summary>
        /// Amount in USD to top-up when balance reaches zero
        /// </summary>
        [JsonProperty("amount_usd")]
        public float AmountUsd { get; set; }

        /// <summary>
        /// Stripe payment method ID to use for auto top-up
        /// </summary>
        [JsonProperty("payment_method_id")]
        public string PaymentMethodId { get; set; }
    }

    /// <summary>
    /// Request model for updating auto top-up settings
    /// </summary>
    [Serializable]
    public class UpdateAutoTopUpRequest
    {
        /// <summary>
        /// Whether auto top-up is enabled
        /// </summary>
        [JsonProperty("enabled")]
        public bool Enabled { get; set; }

        /// <summary>
        /// Amount in USD to top-up when balance reaches zero
        /// </summary>
        [JsonProperty("amount_usd")]
        public float AmountUsd { get; set; }

        /// <summary>
        /// Stripe payment method ID to use for auto top-up
        /// </summary>
        [JsonProperty("payment_method_id")]
        public string PaymentMethodId { get; set; }
    }

    /// <summary>
    /// Response model for auto top-up settings operations
    /// </summary>
    [Serializable]
    public class AutoTopUpResponse
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        [JsonProperty("success")]
        public bool Success { get; set; }

        /// <summary>
        /// The updated auto top-up settings
        /// </summary>
        [JsonProperty("settings")]
        public AutoTopUpSettings Settings { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        [JsonProperty("error")]
        public string Error { get; set; }
    }
}
