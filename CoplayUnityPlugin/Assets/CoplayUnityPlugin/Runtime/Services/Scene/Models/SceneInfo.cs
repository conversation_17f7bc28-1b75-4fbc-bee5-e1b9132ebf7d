using System.Collections.Generic;
using Newtonsoft.Json;

namespace Coplay.Services.Scene.Models
{
    /// <summary>
    /// Information about a Unity scene and its game objects
    /// </summary>
    public class SceneInfo
    {
        /// <summary>
        /// Gets or sets the name of the scene
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the path of the scene file
        /// </summary>
        [JsonProperty("path")]
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the scene is the active scene
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the game objects in this scene
        /// </summary>
        [JsonProperty("gameObjects")]
        public List<GameObjectInfo> GameObjects { get; set; } = new();
    }
}
