using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using Coplay.Common;
using Coplay.Controllers.Systems;

namespace Coplay.Services
{
    public class SceneAutoSaveService
    {
        private readonly SettingsController _settingsController;
        private int _actionCount = 0;

        public SceneAutoSaveService(SettingsController settingsController)
        {
            _settingsController = settingsController;
        }

        /// <summary>
        /// Increments the action count and auto-saves the scene if the threshold is reached
        /// </summary>
        public void IncrementActionCount()
        {
            int autoSaveThreshold = _settingsController.AutoSaveSceneAfterNActions;
            
            // If auto-save is disabled (0), do nothing
            if (autoSaveThreshold <= 0)
            {
                return;
            }

            _actionCount++;

            // Check if we've reached the threshold
            if (_actionCount >= autoSaveThreshold)
            {
                AutoSaveActiveScene();
                _actionCount = 0; // Reset counter after saving
            }
        }

        /// <summary>
        /// Auto-saves the currently active Unity scene if one is loaded
        /// </summary>
        private void AutoSaveActiveScene()
        {
            try
            {
                var activeScene = SceneManager.GetActiveScene();
                
                // Check if there's an active scene and it's valid
                if (!activeScene.IsValid())
                {
                    CoplayLogger.Log("SceneAutoSaveService: No valid active scene to save");
                    return;
                }

                // Check if the scene has been saved before (has a path)
                if (string.IsNullOrEmpty(activeScene.path))
                {
                    CoplayLogger.Log("SceneAutoSaveService: Active scene has not been saved before, skipping auto-save");
                    return;
                }

                // Check if the scene has unsaved changes
                if (!activeScene.isDirty)
                {
                    CoplayLogger.Log($"SceneAutoSaveService: Scene '{activeScene.name}' has no unsaved changes, skipping auto-save");
                    return;
                }

                // Save the scene
                bool saveResult = EditorSceneManager.SaveScene(activeScene);
                
                if (!saveResult)
                {
                    CoplayLogger.LogWarning($"SceneAutoSaveService: Failed to auto-save scene '{activeScene.name}'");
                }
            }
            catch (System.Exception ex)
            {
                CoplayLogger.LogError("SceneAutoSaveService: Error during auto-save", ex);
            }
        }
    }
}
