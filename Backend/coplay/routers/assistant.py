from uuid import uuid4
import asyncio
import traceback

from fastapi import APIRout<PERSON>, Depends, HTTPException, Request, Response, BackgroundTasks
from supabase_auth.types import User

from .auth import get_current_user, get_validated_device_id, validate_thread_belongs_to_user
from ..opentelemetry.helpers import CoplayAttributes, set_trace_attribute
from ..models import AssistantMode, CoplayTask, Prompt, PromptResponse, AssistantThreadRequest, AssistantThreadUpdate, ThreadReviewCheckResponse, ThreadReviewResponse, ThreadReviewStatusRequest
from ..logger import logger
from ..jobs import BACKGROUND_TASK_RESULTS
from ..thread_review_service import should_review_thread, create_thread_review, get_review_by_id, respond_to_review
from ..assistants.chat.agent import send_message_to_llm
from ..assistants.chat.threads import get_thread, get_threads_by_device, create_thread, update_thread, delete_thread
from ..assistants.chat.messages import build_chat_history


router = APIRouter(
    prefix="/assistant",
    tags=["assistant"],
)

@router.post("/stream")
async def stream_assistant(prompt: Prompt, request: Request, background_tasks: BackgroundTasks, current_user: User = Depends(get_current_user), device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(prompt.thread_id, device_id)
    set_trace_attribute(CoplayAttributes.THREAD_ID, prompt.thread_id)
    background_tasks.add_task(_stream_assistant, prompt.thread_id, prompt, request, current_user, device_id)
    BACKGROUND_TASK_RESULTS[prompt.thread_id] = CoplayTask(
        chunk=PromptResponse(type="chunk", finished=False, task_id=prompt.thread_id, thread_id=prompt.thread_id, message=""),
        cancel_scope=asyncio.Event()
    )
    return Response(BACKGROUND_TASK_RESULTS[prompt.thread_id].chunk.model_dump_json(), status_code=202)

async def _stream_assistant(thread_id: str, prompt: Prompt, request: Request, current_user: User = Depends(get_current_user), device_id: str = Depends(get_validated_device_id)):
    try:
        async for chunk in send_message_to_llm(prompt, device_id, current_user, request, task_id=thread_id, cancel_scope=BACKGROUND_TASK_RESULTS[thread_id].cancel_scope):
            BACKGROUND_TASK_RESULTS[thread_id].update_id += 1
            BACKGROUND_TASK_RESULTS[thread_id].chunk = chunk
    except Exception as e:
        logger.error(f"Error in stream_assistant: {e}")
        BACKGROUND_TASK_RESULTS[thread_id].update_id += 1
        BACKGROUND_TASK_RESULTS[thread_id].chunk = PromptResponse(type="error", finished=True, task_id=thread_id, thread_id=thread_id, message=str(e))

@router.get("/stream/{thread_id}")
async def get_message_stream(thread_id: str, last_update_id: int = 0, current_user: User = Depends(get_current_user), device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(thread_id, device_id)
    if thread_id not in BACKGROUND_TASK_RESULTS:
        raise HTTPException(status_code=404, detail="Task not found")

    set_trace_attribute(CoplayAttributes.THREAD_ID, thread_id)

    async def get_new_chunk():
        while True:
            task = BACKGROUND_TASK_RESULTS[thread_id]
            if task.update_id > last_update_id or task.cancel_scope.is_set():
                return
            else:
                await asyncio.sleep(0.01)
    
    try:
        await asyncio.wait_for(get_new_chunk(), timeout=5)
    except asyncio.TimeoutError:
        pass

    task = BACKGROUND_TASK_RESULTS[thread_id].model_copy(deep=True)
    task.chunk.update_id = task.update_id

    # Clear out the task if it's finished or errored since the client now has it
    if task.chunk.finished or task.chunk.type == "error":
        del BACKGROUND_TASK_RESULTS[thread_id]

    return task.chunk

@router.post("/stream/{thread_id}/cancel")
async def cancel_stream(thread_id: str, current_user: User = Depends(get_current_user), device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(thread_id, device_id)
    if thread_id not in BACKGROUND_TASK_RESULTS:
        raise HTTPException(status_code=404, detail="Task not found")
    
    set_trace_attribute(CoplayAttributes.THREAD_ID, thread_id)
    
    BACKGROUND_TASK_RESULTS[thread_id].cancel_scope.set()
    logger.info(f"Stream {thread_id} cancelled")

    del BACKGROUND_TASK_RESULTS[thread_id]

    return Response(status_code=200)

@router.get("/threads/{thread_id}")
async def get_assistant_thread(thread_id: str, device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(thread_id, device_id)
    thread = await get_thread(thread_id)
    if thread is None or thread.assistant_mode in [AssistantMode.ONE_SHOT_SCENE_GEN]:
        return []
    return build_chat_history(thread.messages, thread_id)

@router.get("/threads")
async def get_assistant_threads(device_id: str = Depends(get_validated_device_id)):
    threads = await get_threads_by_device(device_id)
    return threads

@router.post("")
async def create_assistant_thread(assistant_thread: AssistantThreadRequest, device_id: str = Depends(get_validated_device_id)):
    """Create a new assistant with specified instructions type"""
    try:
        return await create_thread(assistant_thread.assistant_mode, device_id, assistant_thread.name)
    except Exception as ex:
        logger.error(f"Failed to create assistant: {str(ex)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create assistant")

@router.post("/threads/{thread_id}")
async def update_assistant_thread(thread_id: str, assistant_thread: AssistantThreadUpdate, device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(thread_id, device_id)
    thread = await update_thread(thread_id, assistant_thread.name, assistant_thread.assistant_mode)
    return thread

@router.delete("/threads/{thread_id}")
async def delete_assistant_thread(thread_id: str, device_id: str = Depends(get_validated_device_id)):
    await validate_thread_belongs_to_user(thread_id, device_id)
    await delete_thread(thread_id)
    return {"status": "deleted"}

@router.post("/check-and-start-thread-review")
async def check_and_start_thread_review(request: dict, background_tasks: BackgroundTasks, device_id: str = Depends(get_validated_device_id)):
    """Check if a thread should be reviewed for this device and create the review if needed"""
    
    thread_id = request.get("thread_id")
    custom_rules = request.get("custom_rules")
    
    if not thread_id:
        raise HTTPException(status_code=400, detail="thread_id is required")
    
    set_trace_attribute(CoplayAttributes.THREAD_ID, thread_id)
    
    should_review = await should_review_thread(device_id, thread_id)
    
    review_id = None
    if should_review:
        review_id = str(uuid4())
        
        background_tasks.add_task(create_thread_review, review_id, device_id, thread_id, custom_rules)
    
    return ThreadReviewCheckResponse(should_review=should_review, review_id=review_id)

@router.get("/thread-reviews/{review_id}")
async def get_specific_thread_review(review_id: str, device_id: str = Depends(get_validated_device_id)):
    """Get a specific thread review by review_id"""
    
    review = await get_review_by_id(review_id, device_id)
    if not review:
        raise HTTPException(status_code=404, detail="Review not found")
    
    return ThreadReviewResponse(
        review_id=review["review_id"],
        suggestion=review["suggestion"],
        diff_content=review["diff_content"],
        created_at=review["created_at"],
        status=review["status"]
    )

@router.post("/thread-reviews/{review_id}/respond")
async def respond_to_thread_review(review_id: str, response: ThreadReviewStatusRequest, current_user: User = Depends(get_current_user)):
    """Accept or reject a thread review suggestion"""
    success = await respond_to_review(review_id, response.accepted)
    
    if not success:
        raise HTTPException(status_code=404, detail="Review not found or already responded to")
    
    return {"status": "accepted" if response.accepted else "rejected", "review_id": review_id}
