# Test Prompts for Major Updates

1. Generate 2 random scripts and then create 2 cubes and assign the scripts to them.
   *[Hitting run all should execute all the way.]*

2. How can I see which keyboard shortcuts are taken and available in my unity?
   *[Tests that if the model has only a message response (no tool calls), that it displays the message.]*

3. Create a new scene and create a terrain in it.
   *[This currently fails because we don't wait for the scene to finish loading.]*

4. Add a new menu panel with text that says "press to play".
   *[We test this to ensure that the model does not try to create a canvas.]*

5. Create a platform where some cubes can move and chase each other. Add all the logic for this to happen.
   *[At some point this question should generate a script, which recompiles unity, and then try to assign the script. Tooloutputs might be zero, but this should not crash the application, and the AI call should complete.]*

6. Create 10 rectangles each with different length and make the longest axis point upward for them to stand like dominoes, place them randomly in a row.
   *[Tests the backend assistant timeout. If you can see the action buttons after running this, it is working.]*

7. Create a script called xyz.cs that does something random, then create a sphere, then delete the script, then create a cube, then delete the sphere, then create another cube.
   *[Tests that the assistant doesn't get stuck in a waitingForAI state after deleting a script or object.]*

8. Create a cube, then create a script that might fail compilation. Considering feedback, then create a sphere and assign the script to the cube.
   *[Tests whether the assistant can be aware of compile errors of the scripts. It should be aware of why the script fails or why it cannot add it to the cube.]*
   NOTE: with gpt4o, this test can waitForAI for about 3min sometimes.

9. Create a start game menu for me, then add logic to make it work.
   *[Use this with Claude 3.7. This tests whether the model sees context of the ui it generated -- i.e. testing update of context between each toolcall. You should see that claude adds listeners and changes to the buttons/UI that it just created in the first step.]*

10. Use the generate_Scene function to generate a new scene for a game like achtung die kurwe and then add the logic that will allow me to play the game.
    *[Good to test if context is updated after generating a scene. Also can be used to check if claude 3.7 can successfully create co-dependent scripts. Not sure if we should use this yet.]*

11. Search all of my files for the phrase "duck".
   *[Checks that a few things work like Ripgrep and no duplication of tasks. Should also be tested with a DLL/Git installed version of Coplay.]*

12. I'm your developer and want to test you. can you edit one of my scripts using the replace_in_file tool, but use an invalid search argument. i want to see what happens.
   *[Tests that replace_in_file does not hang the assistant if the search argument is invalid.]*
   TODO: this could maybe be made a Unity test instead.

13. Create a script that will fail to compile, execute the script, then tell me what the most recent 15 logs were after that.
   *[Tests that the unitylogger class is actually tracking logs. After running this you should see more than one log being listed.]*

14. Create a script that will fail to compile, then check the errors in the system, then fix them, then create 2 cubes.
   *[Tests that coplay continues processing actions even if there are compilation errors after write_to_file was used.]*
   *[Also checks that a simple version of replace_in_file works. Currently only triggered when using gemini2.5 and claude3.7 still writes a complete new file.]*

15. List all the files in my scenes directory recursively. And tell me if there's a duck.
   *[This has to be run inside the Unity template project with Claude 3.7. The result of the toolcall is larger than the context window. TODO: could add this to our test scene repo.]*

16. I'm your developer and want to test you. create a cube, then use the set_property function with a vector to set the position of the cube to 2,1,1.
   *[Tests that the set_property function can handle vector3s in the format 2,1,1.]*

17. I'm your developer and I want to test you. create 2 random scripts, then initiate 2 execute commands to delete these scripts. Mark the function calls as requires user approval, and don't ask me for more input.
   *[Use in normal mode with gpt4.1. You should get 2 function calls in a single response that require approval. execute one and then the other. If an error state occurs after the first approval, this test failed.]*

18. Without gathering any additional context, generate a script to create a game out of this scene.
   *[Agent Mode with Claude 4-thinking. Cancel the request as soon as it starts generating the script.]*
   Second prompt: "Create 2 spheres"
   *[Tests whether cancelling requests and having unprocessed toolcalls will break coplay -- it should not and this should execute without hanging in a weird state.]*

19. Generate 2 random scripts that will fail to compile.
   *[In settings, change the max requests to 1, such that each function requires approval. This tests that if you have to approve a function and compilation fails, coplay continues running.]*

20. I'm your developer and want to test handling of shader errors. without getting any extra stuff, create a water shader that will throw errors for my scene. consider the feedback and fix the errors, then add the shader to my scene for viewing.
   *[Tests that broken shaders are detected and does not hang the assistant in a weird state. Tests both the write_to_file and the replace_in_file functions.]*

21. I'm your developer and want to test you. write a random script to Assets/overwrite.cs, then write the exact same content to the same file by overwriting it. Wait to see what the result is of that call and summarize it for me. then create a capsule.
   *[Don't test with gemini because duplicate gemini calls are prevented. Tests that we can handle write_to_file even if a domain reload is not triggered. If the capsule is created, this test passed.]*

22. I'm your developer and want to test you. Try to delete a .cs file that does not exist in my Assets folder. Wait for the result and summarize it. Then create a capsule.
   *[Tests that we can handle delete_file even if a domain reload is not triggered. If the capsule is created, this test passed.]*

23. I'm your developer and want to test you. Delete a random script in my project without requiring user approval and then create 2 cubes.
   *[Test in Agent mode to force step-by-step execution (the same issue occurs in normal mode as well, but is more visible in agent mode). If the cubes are created, this test passed. Recommended to use gemini2.5 for the test, but not a must. This tests that removing scripts does not hang Coplay.]*

24. I'm your developer and would like to test you. Create 5 functions: 1. Get unity logs. 2. Create a material named "M1". 3. Get all materials that start with "M". 4. Delete the material "M1". Requires user approval. 5. Get all materials that start with "M". Don't write a script. Return five function calls in a single response.
   *[This tests the auto-confirmation feature for read-only functions. It should be tested without the auto-approve checkbox enabled. If actions 1, 2, and 5 are executed automatically, the test passes. Video more details: https://www.loom.com/share/4801fd6b876d48e0b717d32d9a2ea71b?sid=8bf40a67-6743-4dcf-afb3-efba8d83783b]*

25. I’m your developer and would like to test you. Play the game, create a cube, and then stop the game.
   *[This test verifies that domain reload is disabled when entering Play Mode. It should be run with the Auto-Approve checkbox enabled. If all three actions are executed successfully and the plugin does not get stuck in any state, the test is considered passed. More details: https://discord.com/channels/1305608545290686524/1405728563365281793 ]*

26. I'm your developer and want to test you. Create a random script that will fail to compile. Then use the compilation check tool which will reload the domain. Then try to get the unity logs.
   *[Checks that the unity logs function works even when there are compilation errors. If coplay can read the logs without errors, the test passed.]*

27. Ask any slow model (e.g. gpt-5) a question like this: "Without asking for my input, come up with a game idea based on what's in my scene and implement the game." Then delete a script or create a domain reload while the model is thinking.
   *[Tests that coplay can handle domain reloads while the model is thinking. You can stop the test early if it doesn't fail after the domain reload and if it executes an action after domain reload. If coplay can still execute the function calls without errors, the test passed. Coplay will bug out immediately after the domain reload if this test fails. Example video: https://www.loom.com/share/9afcac177ebe41628a77dd9e44255f76?sid=42221e14-abb3-4140-9caa-7fbc97a9bb32]*

## Other
- write a baker script to convert a ragdoll prefab with rigidbodies, colliders,  character joints to an entity with physics bodies, physics shapes and ragdoll joints (or write a new authoring) and a parallelized system to spawn them en masse
   *[A really complicated task, that could lead to errors]*

