from ...notifications import DiscordNotifier
from ...logger import logger


async def handle_system_mention_notification(prompt_text: str, user_email: str, thread_id: str, coplay_version: str) -> None:
    """Handle system mention notification (assumes check already done)."""
    try:
        await DiscordNotifier.send_system_mention_notification(
            user_email=user_email,
            prompt=prompt_text,
            thread_id=thread_id,
            coplay_version=coplay_version
        )
    except Exception as e:
        logger.warning(f"Failed to send system mention notification: {e}")

async def handle_prompt_violation_notification(prompt_text: str, user_email: str, thread_id: str, coplay_version: str) -> None:
    """Handle prompt violation notification (assumes check already done)."""
    try:
        await DiscordNotifier.send_prompt_violation_notification(
            user_email=user_email,
            prompt=prompt_text,
            thread_id=thread_id,
            coplay_version=coplay_version
        )
    except Exception as e:
        logger.warning(f"Failed to send prompt violation notification: {e}")
