import os

OPENAI_KEY = os.getenv("OPENAI_KEY", "********************************************************************************************************************************************************************")
# ANTHROPIC_API_KEY = "************************************************************************************************************" # Remio account
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "************************************************************************************************************") # Coplay account
TURBOPUFFER_API_KEY = os.getenv("TURBOPUFFER_API_KEY", "Lqlg8IeuUOYEAxqvKE4HLYY1XIk6dm60")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyDq19Np6HP5Lcj3Ew64x8TLUssCHqPsH9w")
GROK_API_KEY = os.getenv("GROK_API_KEY", "************************************************************************************")
CODESTRAL_API_KEY = os.getenv("CODESTRAL_API_KEY", "vNoRtLF7scqau1UY8DujLgjp8RVGZgx9")
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID", "coplay-backend")
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://gqoqjkkptwfbkwyssmnj.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.DiPGaWtWEt2AELMHgLIAvDmaghYIZDb6Mh_mETbvwoA")
SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8L1qRF38TaRkdQIyXdpoCKkdhT0Pdt0Oabgiy-8t28E")
SUPABASE_BUCKET = os.getenv("SUPABASE_BUCKET", "coplay-prod")
SUPABASE_DEPENDENCIES_BUCKET = os.getenv("SUPABASE_DEPENDENCIES_BUCKET", "coplay-dependencies-prod")
AVERAGE_CHARS_PER_TOKEN = 4
MAX_LOG_CHUNK_SIZE = 250000
CONTEXT_SYNC_MAX_LOCK_WAIT = 300
COHERE_API_KEY = os.getenv("COHERE_API_KEY", "pW2NGhg4Ks5RdMYayWnQjOaQ2lH6ubSIDFrnWUNP")
LANGCHAIN_ORG_ID = os.getenv("LANGCHAIN_ORG_ID", "15a10c85-35da-4b6c-8508-3c7747783245")
MAX_TOKEN_COUNT_BUFFER = 100
# SUPABASE_POSTGRES_POOLER_URL = "postgres://postgres.gqoqjkkptwfbkwyssmnj:<EMAIL>:5432/postgres"
SUPABASE_POSTGRES_URL = os.getenv("SUPABASE_POSTGRES_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
MERCURY_CODER_API_KEY = os.getenv("MERCURY_CODER_API_KEY", "sk_8c7b895db2728816f3eddb400cd35ad0")
STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY", "***********************************************************************************************************")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET", "whsec_QVTxvYPSC5mweubWX4zBLJ3XOOie6U6V")
FREE_TRIAL_USER_CREDITS_USD = 20.0
SUBSCRIPTION_RENEWAL_CREDITS_USD = 40.0
ZERO_CREDITS_USD = 0.0
MESHY_KEY = os.getenv("MESHY_KEY", "msy_Jrq3BajXewNyryRXUSiJqW3CvgiHFThBizKn")
FAL_KEY = os.getenv("FAL_KEY", "6816878c-9bff-4475-827a-01a47749e202:acfeb1ebd2e005fd3319acc239caa245")
