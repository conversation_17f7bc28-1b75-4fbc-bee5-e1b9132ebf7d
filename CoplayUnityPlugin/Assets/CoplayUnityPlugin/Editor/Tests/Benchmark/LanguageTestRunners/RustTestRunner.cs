using System;
using System.IO;
using System.Threading.Tasks;
using Coplay.Common;

namespace Coplay.Tests.Benchmark.LanguageTestRunners
{
    public class RustTestRunner : LanguageTestRunner
    {
        public override string Language => "rust";
        
        public override async Task<bool> IsLanguageAvailable()
        {
            try
            {
                var cargoResult = await ExecuteCommand("cargo", "--version", Environment.CurrentDirectory, 10, false);
                if (!cargoResult.Success)
                {
                    CoplayLogger.LogWarning("Cargo not available on this system");
                    return false;
                }
                
                var rustcResult = await ExecuteCommand("rustc", "--version", Environment.CurrentDirectory, 10, false);
                if (!rustcResult.Success)
                {
                    CoplayLogger.LogWarning("Rustc not available on this system");
                    return false;
                }
                
                CoplayLogger.Log($"Cargo available: {cargoResult.Output.Trim()}");
                CoplayLogger.Log($"Rustc available: {rustcResult.Output.Trim()}");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogWarning($"Rust/Cargo not available on this system: {ex.Message}");
                return false;
            }
        }
        
        public override async Task<TestExecutionResult> RunTests(BenchmarkExercise exercise, string workingDirectory)
        {
            var result = new TestExecutionResult();
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Prepare test environment (no file copying needed since we're running in original folder)
                PrepareTestEnvironment(exercise, workingDirectory);
                
                // Check if Cargo.toml exists, if not create a minimal one
                var cargoTomlPath = Path.Combine(workingDirectory, "Cargo.toml");
                if (!File.Exists(cargoTomlPath))
                {
                    await CreateMinimalCargoToml(workingDirectory, exercise.Name);
                }
                
                // Ensure src directory exists
                var srcDir = Path.Combine(workingDirectory, "src");
                if (!Directory.Exists(srcDir))
                {
                    Directory.CreateDirectory(srcDir);
                }
                
                // Run cargo test with appropriate flags
                var testResult = await ExecuteCommand("cargo", "test --quiet -- --include-ignored", workingDirectory, TestTimeoutSeconds);
                
                result.Success = testResult.Success;
                result.Output = CleanTestOutput(testResult.Output, workingDirectory);
                result.Error = CleanTestOutput(testResult.Error, workingDirectory);
                result.TimedOut = testResult.TimedOut;
                result.Duration = DateTime.UtcNow - startTime;
                
                // Count compilation and syntax errors in the output
                if (!string.IsNullOrEmpty(result.Output) || !string.IsNullOrEmpty(result.Error))
                {
                    var combinedOutput = result.Output + " " + result.Error;
                    
                    // Rust compilation errors
                    result.CompilationErrors = CountOccurrences(combinedOutput, "error:");
                    result.CompilationErrors += CountOccurrences(combinedOutput, "error[E");
                    
                    // Rust syntax errors (subset of compilation errors)
                    result.SyntaxErrors = CountOccurrences(combinedOutput, "expected");
                    result.SyntaxErrors += CountOccurrences(combinedOutput, "unexpected token");
                    result.SyntaxErrors += CountOccurrences(combinedOutput, "syntax error");
                }
                
                CoplayLogger.Log($"Rust test completed for {exercise.Name}: Success={result.Success}, Duration={result.Duration.TotalSeconds:F1}s");
                
                return result;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.Duration = DateTime.UtcNow - startTime;
                CoplayLogger.LogError($"Exception running Rust tests for {exercise.Name}: {ex.Message}", ex);
                return result;
            }
        }
        
        protected override void PrepareTestEnvironment(BenchmarkExercise exercise, string exerciseDirectory)
        {
            // Since we're running tests in the original exercise folder, no file copying is needed
            // This method can be used for any Rust-specific environment setup if needed
            CoplayLogger.Log($"Preparing Rust test environment for {exercise.Name} in {exerciseDirectory}");
        }
        
        private async Task CreateMinimalCargoToml(string workingDirectory, string exerciseName)
        {
            var cargoToml = $@"[package]
name = ""{exerciseName.Replace('-', '_')}""
version = ""0.1.0""
edition = ""2021""

[dependencies]

[[bin]]
name = ""{exerciseName.Replace('-', '_')}""
path = ""src/main.rs""
";
            
            var cargoTomlPath = Path.Combine(workingDirectory, "Cargo.toml");
            await File.WriteAllTextAsync(cargoTomlPath, cargoToml);
            CoplayLogger.Log("Created minimal Cargo.toml for Rust exercise");
        }
        
        
        private int CountOccurrences(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return 0;
            
            return text.Split(new[] { pattern }, StringSplitOptions.None).Length - 1;
        }
    }
}
