-- Create table for USD credit tracking (free trial credits)
create table if not exists public.credits (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid not null references auth.users(id) on delete cascade,
    credit_balance decimal(10,4) not null default 20.0,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    constraint credits_unique_user_id unique (user_id)
);

-- Enable RLS
alter table public.credits enable row level security;

-- Create policy for users to view their own credit balance (only if it doesn't exist)
do $$
begin
    if not exists (
        select 1 from pg_policies 
        where schemaname = 'public' 
        and tablename = 'credits' 
        and policyname = 'Users can view their own credit balance'
    ) then
        create policy "Users can view their own credit balance"
            on public.credits
            for select
            to authenticated
            using (auth.uid() = user_id);
    end if;
end $$;

-- Create trigger to update timestamp
drop trigger if exists update_credits_timestamp on public.credits;
create trigger update_credits_timestamp
before update on public.credits
for each row
execute function public.handle_updated_at();

-- Create index for faster lookups
create index if not exists credits_user_id_idx on public.credits(user_id);
