import { useEffect, useState, useCallback } from "react";
import { Session } from "@supabase/supabase-js";
import { Profile } from "@/types";
import { getAuthData, onAuthStateChange, type SidebarAuthData } from "@/services/authService";

export interface UseSidebarAuthReturn {
  session: Session | null;
  userData: Profile | null;
  loading: boolean;
  error: string | null;
  refreshAuth: () => Promise<void>;
}

/**
 * Custom hook to manage sidebar authentication state
 * Handles user session and profile data fetching
 */
export function useSidebarAuth(): UseSidebarAuthReturn {
  const [session, setSession] = useState<Session | null>(null);
  const [userData, setUserData] = useState<Profile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAuthData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const authData = await getAuthData();
      setSession(authData.session);
      setUserData(authData.userData);
    } catch (err) {
      console.error("Error fetching auth data:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch auth data");
      setSession(null);
      setUserData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAuth = useCallback(async () => {
    await fetchAuthData();
  }, [fetchAuthData]);

  useEffect(() => {
    // Initial fetch
    fetchAuthData();

    // Listen for auth changes
    const { data: { subscription } } = onAuthStateChange((newSession: Session | null) => {
      setSession(newSession);
      if (!newSession) {
        setUserData(null);
      }
    });

    return () => subscription.unsubscribe();
  }, [fetchAuthData]);

  return {
    session,
    userData,
    loading,
    error,
    refreshAuth,
  };
}
