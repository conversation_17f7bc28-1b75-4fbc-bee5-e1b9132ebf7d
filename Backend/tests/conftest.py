import os
import sys

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unittest.mock import patch

from fastapi.testclient import TestClient
import pytest
import uuid
from unittest.mock import AsyncMock
from datetime import datetime, timezone
from pydantic import BaseModel
import httpx

from coplay.models import CoplayThread
from main import app, get_current_user, get_validated_device_id

# Set environment variable to indicate we're in a test environment
os.environ['TESTING'] = 'true'

# Mock user
class MockUser(BaseModel):
    email: str = "<EMAIL>"
    id: str = str(uuid.uuid4())

@pytest.fixture
def mock_user() -> MockUser:
    """Provides a mock user object."""
    return MockUser()

@pytest.fixture
def test_client():
    '''Returns a test client for the FastAPI app'''
    # Import the app after setting up the test environment
    # This ensures the autocomplete_assistant doesn't try to preload models
    with patch('coplay.assistants.autocomplete_assistant.AutocompleteAssistant.preload_models'):
        from main import app
        return TestClient(app)

@pytest.fixture
def mock_db_calls():
    """Mocks database-related function calls within the assistant service to prevent real DB access."""
    def mock_dispatch_func(event_name, payload):
        return None
    
    with patch('coplay.assistants.chat.agent.can_user_make_requests', new_callable=AsyncMock, return_value=True), \
         patch('coplay.assistants.chat.agent.get_user_anthropic_key', new_callable=AsyncMock, return_value=None), \
         patch('coplay.assistants.chat.agent.check_if_user_has_subscription', new_callable=AsyncMock, return_value=True), \
         patch('coplay.assistants.chat.agent.dispatch', side_effect=mock_dispatch_func):
        yield

@pytest.fixture(scope="function")
def app_with_mock_db(mock_db_calls):
    """
    Patches AssistantService methods for an in-memory thread store,
    then provides the FastAPI app.
    """
    thread_store = {}

    async def mock_create_assistant(assistant_mode, device_id, name=None):
        thread_id = f"cpl-thread-{uuid.uuid4()}".replace("-", "_")
        new_thread = CoplayThread(
            id=thread_id,
            assistant_mode=assistant_mode,
            name=name,
            created_at=datetime.now(timezone.utc),
            messages=[],
            turn=0
        )
        thread_store[thread_id] = new_thread
        return new_thread.model_copy(deep=True)

    async def mock_get_assistant_thread(tid):
        if tid in thread_store:
            return thread_store[tid].model_copy(deep=True)
        return CoplayThread(id=tid, messages=[], turn=0, assistant_mode='normal', created_at=datetime.now(timezone.utc), name='test')

    async def mock_update_assistant_messages(tid, messages, turn):
        if tid not in thread_store:
            thread_store[tid] = CoplayThread(id=tid, assistant_mode='normal', created_at=datetime.now(timezone.utc), name='test', messages=[], turn=0)
        thread_store[tid].messages = messages
        thread_store[tid].turn = turn

    async def mock_delete_assistant(tid):
        if tid in thread_store:
            del thread_store[tid]

    async def mock_validate_thread_belongs_to_user(thread_id, device_id):
        # Mock that does nothing, just passes
        pass

    with patch('coplay.routers.assistant.create_thread', mock_create_assistant), \
         patch('coplay.routers.assistant.delete_thread', mock_delete_assistant), \
         patch('coplay.assistants.chat.agent.get_thread', mock_get_assistant_thread), \
         patch('coplay.assistants.chat.agent.update_thread_messages', mock_update_assistant_messages), \
         patch('coplay.routers.assistant.validate_thread_belongs_to_user', mock_validate_thread_belongs_to_user), \
         patch('coplay.routers.assets.validate_thread_belongs_to_user', mock_validate_thread_belongs_to_user):
        yield app

@pytest.fixture
async def async_client_with_auth(app_with_mock_db, mock_user: MockUser):
    """
    Provides an httpx.AsyncClient with authentication dependencies overridden.
    """
    app_instance = app_with_mock_db

    async def override_get_current_user():
        return mock_user

    def override_get_validated_device_id():
        return "test_device"

    app_instance.dependency_overrides[get_current_user] = override_get_current_user
    app_instance.dependency_overrides[get_validated_device_id] = override_get_validated_device_id
    
    transport = httpx.ASGITransport(app=app_instance)
    async with httpx.AsyncClient(transport=transport, base_url="http://test") as client:
        yield client
    
    app_instance.dependency_overrides = {}

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )

@pytest.fixture(autouse=True)
def fresh_model_instances(monkeypatch: pytest.MonkeyPatch):
    import coplay.assistants.chat.graph as graph
    import coplay.assistants.chat.agent as agent

    # Rebuild the dict once
    new_instances = graph._make_model_instances()

    # Update both modules so lookups use the fresh instances
    monkeypatch.setattr(graph, "MODEL_INSTANCES", new_instances, raising=True)
    monkeypatch.setattr(agent, "MODEL_INSTANCES", new_instances, raising=True)

    yield

pytest_plugins = ["psycopg_fixtures"]
