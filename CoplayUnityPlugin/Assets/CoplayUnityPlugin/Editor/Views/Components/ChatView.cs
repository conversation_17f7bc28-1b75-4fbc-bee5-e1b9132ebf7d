using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor;
using Coplay.Controllers.Systems;
using Coplay.Models.Context;
using Coplay.Models.Configuration;
using Coplay.Views.Windows;
using Coplay.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using Coplay.Controllers.Functions;
using System.IO;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Controllers.Subscription;
using Coplay.Models.Assistants;
using Coplay.Services.Chat;
using System.Reactive.Disposables;
using Coplay.Common.Rx;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Threading.Tasks;
using System.Reactive;
using Coplay.Controllers.Orchestrator;
using Coplay.Services;
using Microsoft.Extensions.Logging;

namespace Coplay.Views.Components
{
    /// <summary>
    /// Static class for rendering chat UI elements
    /// </summary>
    /// 
    public class ChatView
    {
        private const string FunctionCallsContainerName = "function_calls";
        private const string DraftKeyPrefix = "coplay_draft_";

        private readonly ILogger<ChatView> _logger;
        private readonly ThumbnailManager _thumbnailManager;
        private readonly ChatHistoryRepository _chatHistory;
        private readonly FullContext _context;
        private readonly StateController _stateManager;
        private readonly CoplayStateModel _stateModel;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly IChatThreads _chatThreads;
        private readonly FunctionExecutionController _functionExecutionController;
        private readonly SettingsController _settingsController;
        private readonly FunctionQueueViewFactory _functionQueueViewFactory;
        private readonly SettingsViewFactory _settingsViewFactory;
        private readonly AccountViewFactory _accountViewFactory;
        private readonly ConfirmationDialogView _confirmationDialogView;
        private readonly TaskProgressView _taskProgressView;
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly PipelineRecordingViewFactory _pipelineRecordingViewFactory;
        private readonly SubscriptionManager _subscriptionManager;
        private readonly UIResourcesService _resourcesService;
        private readonly OrchestratorProcessor _orchestratorProcessor;
        private readonly IApplicationPaths _applicationPaths;
        private readonly IEditorStateService _editorStateService;
        private readonly CompositeDisposable _coplayWindowScope;
        private readonly ThreadReviewView _threadReviewView;
        private VisualElement _rootElement;
        private VisualElement _feedbackPopup;
        private Button _submitButton;
        private TextField _chatInput;
        private VisualElement _imageCompatibilityDialog;
        private VisualElement _endOfFreeTrialDialog;
        private VisualElement _topUpCreditsDialog;
        private VisualElement _subscribeDialog;
        private Label _threadCostLabel;
        public VisualElement RootElement => _rootElement ??= CreateRootElement();

        private readonly Subject<string> _messageSubmittedSubject = new Subject<string>();
        public IObservable<string> MessageSubmitted => _messageSubmittedSubject.AsObservable();

        // Drawer management
        private VisualElement _historyDrawer;
        private VisualElement _settingsDrawer;
        private VisualElement _accountDrawer;
        private VisualElement[] _drawers;

        public ChatView(
            ILogger<ChatView> logger,
            ThumbnailManager thumbnailManager,
            ChatHistoryRepository chatHistory,
            FullContext context,
            StateController stateManager,
            AssistantConfiguration assistantConfiguration,
            IChatThreads chatThreads,
            FunctionExecutionController functionExecutionController,
            SettingsController settingsController,
            FunctionQueueViewFactory functionQueueViewFactory,
            CoplayStateModel stateModel,
            SettingsViewFactory settingsViewFactory,
            AccountViewFactory accountViewFactory,
            ConfirmationDialogView confirmationDialogView,
            TaskProgressView taskProgressView,
            ICoplayApiClient coplayApiClient,
            PipelineRecordingViewFactory pipelineRecordingViewFactory,
            SubscriptionManager subscriptionManager,
            UIResourcesService resourcesService,
            OrchestratorProcessor orchestratorProcessor,
            IApplicationPaths applicationPaths,
            CompositeDisposable coplayWindowScope,
            ThreadReviewView threadReviewView,
            IEditorStateService editorStateService)
        {
            _logger = logger;
            _thumbnailManager = thumbnailManager;
            _chatHistory = chatHistory;
            _context = context;
            _stateManager = stateManager;
            _assistantConfiguration = assistantConfiguration;
            _chatThreads = chatThreads;
            _functionExecutionController = functionExecutionController;
            _settingsController = settingsController;
            _functionQueueViewFactory = functionQueueViewFactory;
            _stateModel = stateModel;
            _settingsViewFactory = settingsViewFactory;
            _accountViewFactory = accountViewFactory;
            _confirmationDialogView = confirmationDialogView;
            _taskProgressView = taskProgressView;
            _coplayApiClient = coplayApiClient;
            _pipelineRecordingViewFactory = pipelineRecordingViewFactory;
            _subscriptionManager = subscriptionManager;
            _resourcesService = resourcesService; 
            _orchestratorProcessor = orchestratorProcessor;
            _orchestratorProcessor.OnProcessingStateChanged += (_) => UpdateSubmitButtonState();
            _applicationPaths = applicationPaths;
            _editorStateService = editorStateService;
            _coplayWindowScope = coplayWindowScope;
            _threadReviewView = threadReviewView;
        }

        private VisualElement CreateRootElement()
        {
            VisualTreeAsset uxml = _resourcesService.FindAsset<VisualTreeAsset>("coplay.uxml");
            StyleSheet uss = _resourcesService.FindAsset<StyleSheet>("style.uss");
            StyleSheet lightUss = _resourcesService.FindAsset<StyleSheet>("style_light.uss");
            StyleSheet darkUss = _resourcesService.FindAsset<StyleSheet>("style_dark.uss");
            VisualElement ui = uxml.Instantiate();
            ui.style.height = new StyleLength(Length.Percent(100));

            // Add base stylesheet
            ui.styleSheets.Add(uss);

            // Add theme stylesheet based on Unity's theme
            if (EditorGUIUtility.isProSkin)
            {
                if (darkUss != null)
                {
                    ui.styleSheets.Add(darkUss);
                }
            }
            else
            {
                if (lightUss != null)
                {
                    ui.styleSheets.Add(lightUss);
                }
            }

            _settingsViewFactory.Setup(ui, ToggleSettingsDrawer);

            // Setup pipeline recording panel
            if (_pipelineRecordingViewFactory != null)
            {
                _pipelineRecordingViewFactory.SetupPipelineRecording(ui, _context, _chatHistory);

                // Set initial visibility based on current mode
                UpdatePipelineRecordingPanelVisibility();
            }
            _accountViewFactory.Setup(ui, ToggleAccountDrawer);

            // Initialize centralized drawer management
            InitializeDrawers(ui);

            var scroller = ui.Q<ScrollView>("messages");

            // Messages
            var messagesView = scroller.Q<VisualElement>("chat");
            VisualTreeAsset chatMessageUxml =
                _resourcesService.FindAsset<VisualTreeAsset>("chat_message.uxml");

            _chatHistory.MessageAdded
                .Merge(_chatHistory.MessageUpdated)
                // Skip empty message updates when there are no tool calls or UI message
                // Also skip messages that start with STOP (LLM termination marker)
                .Where(message => !string.IsNullOrWhiteSpace(message.Content) || message.FunctionCalls.Count != 0)
                .ObserveOnEditorMainThread()
                .Do(message =>
                {
                    var chatMessage = messagesView.Q<TemplateContainer>(message.Id);
                    if (chatMessage != null)
                    {
                        // Clean up textures for the old message before replacing it
                        _thumbnailManager.CleanupElementTextures(chatMessage);
                        messagesView.Insert(messagesView.IndexOf(chatMessage),
                            CreateChatMessage(message, chatMessageUxml));
                        chatMessage.RemoveFromHierarchy();
                    }
                    else
                    {
                        messagesView.Add(CreateChatMessage(message, chatMessageUxml));
                    }

                    UpdateThreadCostLabel();
                    ScrollToBottom(scroller);
                })
                .Subscribe(_ => { }, ex => _logger.LogError(ex, "Unhandled error in Add/Update message handler"))
                .AddTo(_coplayWindowScope);

            _chatHistory.Cleared
                .ObserveOnEditorMainThread()
                .Do(_ =>
                {
                    // Clean up textures before clearing messages
                    _thumbnailManager.CleanupElementTextures(messagesView);
                    messagesView.Clear();
                    ScrollToBottom(scroller);
                    UpdateThreadCostLabel();
                })
                .Subscribe(_ => { }, ex => _logger.LogError(ex, "Unhandled error in clear chat handler"))
                .AddTo(_coplayWindowScope);

            // Threads
            UpdateChatHistory(messagesView, chatMessageUxml, scroller);
            var threadName = ui.Q<Label>("thread_name");
            threadName.text = _chatThreads.CurrentThread?.name ?? "Loading chat...";
            _threadCostLabel = ui.Q<Label>("thread_cost");
            if (_threadCostLabel != null)
            {
                _threadCostLabel.text = "$0.0000";
            }
            var history = ui.Q<ScrollView>("history");

            _chatThreads.LoadingThreadObservable
                .ObserveOnEditorMainThread()
                .Subscribe(_ =>
                {
                    threadName.text = "Loading chat...";
                    UpdateChatInputState(ui);
                })
                .AddTo(_coplayWindowScope);

            _chatThreads.ThreadLoadedObservable
                .ObserveOnEditorMainThread()
                .Subscribe(thread =>
                {
                    threadName.text = thread?.name ?? thread?.id;
                    foreach (var child in history.Children().ToList())
                    {
                        if (child.name == thread.id)
                        {
                            child.AddToClassList("current_thread");
                        }
                        else
                        {
                            child.RemoveFromClassList("current_thread");
                        }
                    }
                    UpdateChatInputState(ui);
                    UpdateChatHistory(messagesView, chatMessageUxml, scroller);
                    UpdateThreadCostLabel();
                    UpdatePipelineRecordingPanelVisibility(); // TODO: this should not really be hanlded this way. Instead we should fire the onmodechanged event.

                    // Restore draft for the newly loaded thread
                    var chatInputField = ui.Q<TextField>("chat_input");
                    if (chatInputField != null)
                    {
                        chatInputField.value = LoadDraft();
                    }
                })
                .AddTo(_coplayWindowScope);

            _chatThreads.ThreadsLoadedObservable
                .ObserveOnEditorMainThread()
                .Subscribe((threads) =>
                {
                    history.Clear();
                    foreach (var thread in threads)
                    {
                        if (thread.assistant_mode == AssistantMode.OneShotSceneGen)
                        {
                            continue;
                        }
                        var item = new VisualElement();
                        item.AddToClassList("history_item");
                        item.name = thread.id;
                        if (thread.id == _chatThreads.CurrentThread?.id)
                        {
                            item.AddToClassList("current_thread");
                        }
                        item.Add(new Label()
                        {
                            name = "thread_label",
                            text = thread.name ?? thread.id,
                        });
                        item.Add(new Label()
                        {
                            name = "thread_created_at",
                            text = DateTime.Parse(thread.created_at).ToString("MMMM d, HH:mm")
                        });
                        var deleteButton = new Button
                        {
                            text = "Delete"
                        };
                        deleteButton.clicked += async () =>
                        {
                            if (_chatThreads.IsDeletingThread)
                            {
                                return;
                            }
                            item.SetEnabled(false);
                            deleteButton.SetEnabled(false);
                            deleteButton.text = "Deleting...";
                            await _chatThreads.DeleteThread(thread);
                            history.style.display = DisplayStyle.None;
                        };
                        item.Add(new VisualElement()
                        {
                            style = {
                                flexGrow = 1
                            }
                        });
                        item.Add(deleteButton);
                        item.RegisterCallback<MouseDownEvent>(async e =>
                        {
                            if (e.button == 0 && _chatThreads.CurrentThread.id != thread.id)
                            {
                                history.style.display = DisplayStyle.None;
                                await _chatThreads.LoadThreadMessages(thread.id);
                            }
                        });
                        history.Add(item);
                    }
                    UpdateChatInputState(ui);
                }).AddTo(_coplayWindowScope);

            // Context selection
            var selectedContextList = ui.Q<VisualElement>("selected_context");
            VisualTreeAsset contextItemUxml =
                _resourcesService.FindAsset<VisualTreeAsset>("context_item.uxml");
            VisualTreeAsset removableContextItemUxml =
                _resourcesService.FindAsset<VisualTreeAsset>("removable_context_item.uxml");
            VisualTreeAsset imageContextItemUxml =
                _resourcesService.FindAsset<VisualTreeAsset>("image_context_item.uxml");
            CreateContextItems(selectedContextList, removableContextItemUxml, imageContextItemUxml);

            SetupDragAndDrop(ui);

            _context.SelectedContextChanged
                .ObserveOnEditorMainThread()
                .Subscribe(_ =>
                {
                    selectedContextList.Clear();
                    CreateContextItems(selectedContextList, removableContextItemUxml, imageContextItemUxml);
                })
                .AddTo(_coplayWindowScope);

            // Chat input
            _chatInput = ui.Q<TextField>("chat_input");

            // Add event to track changes in the TextField, especially for paste operations
            // When we copy and paste long text, the scrollbars won't appear without this code
            // We need to force a layout recalculation after multiline content is inserted
            _chatInput.RegisterValueChangedCallback(evt =>
            {
                if (evt.newValue != null && evt.newValue.Contains("\n"))
                {
                    _chatInput.style.height = new StyleLength(StyleKeyword.Auto);
                }

                // Save draft on every change
                SaveDraft(evt.newValue);
            });

            // Restore draft after chat input is set up
            _chatInput.value = LoadDraft();

#if UNITY_2022_1_OR_NEWER
            var suggestions = ui.Q<ScrollView>("suggestions");
            var suggestionsView = new SuggestionsView(_chatInput, suggestions);
            suggestionsView.OnSuggestionSelected += (item) => {
                TryAttachAsset(item.ToObject());
            };
#endif

            // Create image compatibility warning dialog
            _imageCompatibilityDialog = _confirmationDialogView.CreateConfirmationDialog(
                title: "Image Processing Not Supported",
                message: "The selected model does not support image processing. Please remove any attached images or select a different model.",
                confirmButtonText: "OK",
                cancelButtonText: "Cancel",
                onConfirm: () =>
                {
                    // User clicked OK - do nothing, they'll need to remove images or change model
                },
                onCancel: () =>
                {
                    // User clicked Cancel - do nothing
                },
                showCheckbox: false
            );
            ui.Add(_imageCompatibilityDialog);

            // Create free trial over dialog
            _endOfFreeTrialDialog = _confirmationDialogView.CreateConfirmationDialog(
                title: "Free Trial Over",
                message: "Your Free Trial is over. Please subscribe to continue or use your own Anthropic API key.",
                confirmButtonText: "Subscribe",
                cancelButtonText: "Ok",
                onConfirm: () =>
                {
                    Application.OpenURL(Constants.AccountDashboardUrl + "/dashboard");
                },
                onCancel: () =>
                {
                    // Do nothing
                },
                showCheckbox: false
            );
            ui.Add(_endOfFreeTrialDialog);

            // Create top up credits dialog
            _topUpCreditsDialog = _confirmationDialogView.CreateConfirmationDialog(
                title: "Top Up Credits",
                message: "You're out of credits. Your $4 use-it-or-lose-it credits reset tomorrow. Please top up now to continue using Coplay.",
                confirmButtonText: "Ok", 
                cancelButtonText: "Cancel",
                onConfirm: () =>
                {
                    // TODO: make the topup button functional. E.g. open the usage panel in coplay ui
                },
                // onCancel: () =>
                // {
                //     // Do nothing
                // },
                showCheckbox: false
            );
            ui.Add(_topUpCreditsDialog);

            // Create subscribe dialog
            _subscribeDialog = _confirmationDialogView.CreateConfirmationDialog(
                title: "Subscription Required",
                message: "You need an active subscription to continue using Coplay with your current model selection.",
                confirmButtonText: "Subscribe",
                cancelButtonText: "Ok",
                onConfirm: () =>
                {
                    Application.OpenURL(Constants.AccountDashboardUrl + "/dashboard");
                },
                onCancel: () =>
                {
                    // Do nothing
                },
                showCheckbox: false
            );
            ui.Add(_subscribeDialog);

            _chatInput.RegisterCallback<KeyDownEvent>(e =>
            {
                // NOTE: Unity 6 only uses the last registered callback for key events. Thus we need to check for all key combos in the same callback.
                if (e.keyCode == KeyCode.V && (e.modifiers.HasFlag(EventModifiers.Control) || e.modifiers.HasFlag(EventModifiers.Command)))
                {
                    // Schedule the clipboard check to happen after the default paste behavior
                    _chatInput.schedule.Execute(() =>
                    {
                        _ = HandleClipboardPaste();
                    });
                }

                // This is needed because Unity sends two events one with KeyCode.Return and one with character '\n'
                // See https://docs.unity3d.com/6000.1/Documentation/Manual/UIE-Keyboard-Events.html
                if (e.character == '\n')
                {
                    e.StopImmediatePropagation();
                    e.PreventDefault();
                    return;
                }

                // Check for Shift+Enter and allow newline insertion
                if ((e.keyCode == KeyCode.Return || e.keyCode == KeyCode.KeypadEnter) &&
                    e.modifiers.HasFlag(EventModifiers.Shift))
                {
                    if (e.currentTarget is TextField textField)
                    {
                        int cursorIndex = textField.cursorIndex;
                        string currentText = textField.value;
                        // Clamp the cursor index to the valid range
                        if (cursorIndex < 0 || cursorIndex > currentText.Length)
                        {
                            cursorIndex = currentText.Length;
                        }

                        string newText = currentText.Insert(cursorIndex, "\n");

                        // Save current selection state
                        int selStart = textField.selectIndex;
                        int selEnd = textField.cursorIndex;

                        // Update the text value
                        textField.value = newText;

                        // Save draft after Shift+Enter
                        SaveDraft(newText);

                        // Set cursor after the newline
                        int newCursorPos = cursorIndex + 1;

                        // Schedule setting cursor position on next frame to avoid selection issues
                        textField.schedule.Execute(() =>
                        {
                            textField.SelectRange(newCursorPos, newCursorPos);
                        }).ExecuteLater(0);
                    }

                    e.StopImmediatePropagation();
                    return;
                }

                // Existing behavior for Enter without Shift
                if (e.keyCode == KeyCode.Return || e.keyCode == KeyCode.KeypadEnter)
                {
                    e.StopImmediatePropagation();
                    if (e.currentTarget is TextField textField)
                    {
#if UNITY_2022_1_OR_NEWER
                        if (suggestionsView.HandleSuggestionSelection()) {
                            return;
                        }
#endif
                        var message = textField.value.Replace("<b>", "").Replace("</b>", "");
                        HandleMessageSubmission(message);
                    }
                    return;
                }

                if (e.keyCode == KeyCode.Backspace && e.modifiers.HasFlag(EventModifiers.Shift))
                {
                    _stateManager.CancelCurrentRequest();
                    _functionExecutionController.CancelAllFunctions();
                }
            }, TrickleDown.TrickleDown);

            _submitButton = ui.Q<Button>("submit");
            _submitButton.clicked += OnSubmitClicked;

            // Toolbar
            var newThreadButton = ui.Q<Button>("new_thread");
            newThreadButton.clicked += async () =>
            {
                await _chatThreads.CreateThread();
                _stateManager.ClearState();
            };

            _chatThreads.CreatingThreadObservable
                .ObserveOnEditorMainThread()
                .Subscribe(_ =>
                {
                    threadName.text = "Creating new chat...";
                    UpdateThreadButtons(ui);
                    UpdateChatInputState(ui);
                    Task.Run(async () => await CheckForThreadReviewAsync(ui));
                })
                .AddTo(_coplayWindowScope);

            _chatThreads.ThreadCreatedObservable
                .Merge(_chatThreads.ThreadLoadedObservable.Select(_ => Unit.Default))
                .ObserveOnEditorMainThread()
                .Subscribe(thread =>
                {
                    UpdateThreadButtons(ui);
                })
                .AddTo(_coplayWindowScope);

            ui.Q<Button>("history").clicked += () =>
            {
                ToggleHistoryDrawer();
            };

            // Scene generation
            var iterationDropdown = ui.Q<DropdownField>("iterations");
            int currentDepth = EditorPrefs.GetInt("Coplay_SceneGenDepth", 3);
            iterationDropdown.value = currentDepth.ToString();
            if (_assistantConfiguration.Mode == AssistantMode.OneShotSceneGen)
            {
                iterationDropdown.style.display = DisplayStyle.Flex;
            }
            else
            {
                iterationDropdown.style.display = DisplayStyle.None;
            }

            iterationDropdown.RegisterValueChangedCallback(e =>
            {
                EditorPrefs.SetInt("Coplay_SceneGenDepth", int.Parse(e.newValue));
                _assistantConfiguration.Save();
            });

            // Model selection
            var modelDropdown = ModelDropdownHelper.ConfigureModelDropdown(ui, "model", _assistantConfiguration.Model ?? AIModelExtensions.DefaultModelString, true, (newValue) =>
            {
                _assistantConfiguration.Model = newValue;
                _assistantConfiguration.Save();
            });

            // Listen for programmatic model changes and update the dropdown
            Action onModelChanged = () =>
            {
                if (modelDropdown != null)
                {
                    var newModel = _assistantConfiguration.Model ?? AIModelExtensions.DefaultModelString;
                    // Convert backend ID to display name for dropdown update
                    var displayName = ModelDropdownHelper.GetDisplayName(newModel);
                    modelDropdown.SetValueWithoutNotify(displayName);
                }
            };
            _assistantConfiguration.OnModelChanged += onModelChanged;

            // Add cleanup for the model change subscription
            _coplayWindowScope.Add(Disposable.Create(() => 
            {
                _assistantConfiguration.OnModelChanged -= onModelChanged;
            }));

            // Assistant mode selection
            var modeDropdown = ui.Q<DropdownField>("mode");
            // modeDropdown.choices = Enum.GetValues(typeof(AssistantMode)).Cast<AssistantMode>().Select(m => m.ToString())
            modeDropdown.choices = Enum.GetValues(typeof(AssistantMode)).Cast<AssistantMode>()
                .Select(m => m.ToString())
                .ToList();
            modeDropdown.value = _assistantConfiguration.Mode.ToString();
#if UNITY_2022_1_OR_NEWER
            modeDropdown.formatSelectedValueCallback = (value) => {
                return "Mode: " + value;
            };
#endif
            modeDropdown.tooltip = "Normal: reliable and fast, this should be the prefered mode unless you see the Coplay making silly mistakes on your task.\n\n" +
                                   "StepByStep: completes actions strictly step-by-step allowing it to error correct early.";
            modeDropdown.RegisterValueChangedCallback(async e =>
            {
                _assistantConfiguration.SetMode((AssistantMode)Enum.Parse(typeof(AssistantMode), e.newValue), false);
                // Handle UI visibility based on the selected mode
                if (_assistantConfiguration.Mode == AssistantMode.OneShotSceneGen)
                {
                    iterationDropdown.style.display = DisplayStyle.Flex;
                }
                else
                {
                    iterationDropdown.style.display = DisplayStyle.None;
                }

                if (_assistantConfiguration.Mode == AssistantMode.Orchestrator)
                {
                    Utils.CreateDefaultPlanIfNoneExists(_applicationPaths);
                }

                // MCP-specific handling
                if (_assistantConfiguration.Mode == AssistantMode.StepByStep) // DO we still need this? looks wrong?
                {
                    // Change the default selected context for Agent mode because the model can get the less frequently used context itself.
                    // TODO: we should refactor this to be called somewhere else. It also needs to be called when we enable the coplay UI.
                    _context.ClearAll();
                    _context.AddSelectedContext(
                        "Active Scene"); // We keep this in here because we use RAG to only return the most relevant context. Thus it doesn't bloat things too much.
                    var autoApproveContainer = ui.Q<VisualElement>("auto_approve_container");
                    autoApproveContainer.style.display = DisplayStyle.Flex;
                }

                // Show/hide pipeline recording panel based on mode
                UpdatePipelineRecordingPanelVisibility();

                _assistantConfiguration.Save();
                if (_chatHistory.Messages.Count > 0)
                {
                    await _chatThreads.CreateThread();
                }
                else
                {
                    await _chatThreads.UpdateThread(_chatThreads.CurrentThread.id, _chatThreads.CurrentThread.name, _assistantConfiguration.Mode);
                }
                UpdateChatInputPlaceholder(ui);
                UpdateOrchestratorUI(ui);
            });

            _assistantConfiguration.OnModeChanged += () =>
            {
                modeDropdown.SetValueWithoutNotify(_assistantConfiguration.Mode.ToString());
                UpdateOrchestratorUI(ui);
                // UpdatePipelineRecordingPanelVisibility(); // Commented because not needed yet. but it will be when we refactor threads to properly fire mode changes.
            };

            // Auto-execute toggle in Agent mode
            var autoApproveContainer = ui.Q<VisualElement>("auto_approve_container");
            var autoApproveToggle = ui.Q<Toggle>("auto_approve_toggle");

            // Set initial state based on configuration
            autoApproveToggle.value = _assistantConfiguration.AutoApproveFunctions;

            // Show auto-approve toggle for all modes
            autoApproveContainer.style.display = DisplayStyle.Flex;

            // Handle toggle changes
            autoApproveToggle.RegisterValueChangedCallback(evt =>
            {
                _assistantConfiguration.AutoApproveFunctions = evt.newValue;
                _assistantConfiguration.Save();
            });

            // Task progress bar
            _taskProgressView.Setup(ui);

            // Status
            var statusLabel = ui.Q<Label>("status");
            statusLabel.text = $"Current State: {_stateManager.CurrentState}";
            int dotCount = 0;
            var thinkingContainer = ui.Q<VisualElement>("thinking");
            var updateTask = thinkingContainer.schedule.Execute(() =>
            {
                dotCount = (dotCount % 3) + 1;
                thinkingContainer.Q<Label>("thinking_label").text =
                    _stateManager.ThinkingProgress + new string('.', dotCount);
            }).Every(1000);
            UpdateThinkingContainer(ui, updateTask);

            _stateModel.CurrentStateObservable
                .ObserveOnEditorMainThread()
                .Subscribe(state =>
                {
                    statusLabel.text = $"Current State: {_stateManager.CurrentState}";
                    UpdateThinkingContainer(ui, updateTask);
                    UpdateChatInputState(ui);
                    UpdateThreadButtons(ui);
                    ScrollToBottom(scroller);
                })
                .AddTo(_coplayWindowScope);

            var cancelButton = ui.Q<Button>("cancel_thinking");
            cancelButton.Q<Label>("icon").text = "Shift+⌫";
            cancelButton.RegisterCallback<MouseDownEvent>(_ =>
            {
                CoplayLogger.Log("Cancelling current request");
                _stateManager.CancelCurrentRequest();
                UpdateThinkingContainer(ui, updateTask);
                _functionExecutionController.CancelAllFunctions();
            }, TrickleDown.TrickleDown);

            // Initialize feedback popup
            _feedbackPopup = ui.Q<VisualElement>("feedback_popup");
            if (_feedbackPopup != null)
            {
                // Set up close button
                var closeButton = _feedbackPopup.Q<Button>("close_popup_button");
                if (closeButton != null)
                {
                    closeButton.clicked += () =>
                    {
                        _feedbackPopup.style.display = DisplayStyle.None;
                    };
                }

                // Set up submit button
                var submitButton = _feedbackPopup.Q<Button>("submit_feedback_button");
                if (submitButton != null)
                {
                    submitButton.clicked += () =>
                    {
                        // Get the message ID from the popup's userData
                        string messageId = _feedbackPopup.userData as string;
                        if (!string.IsNullOrEmpty(messageId))
                        {
                            // Find the message in the chat history
                            var message = _chatHistory.Messages.FirstOrDefault(m => m.Id == messageId);
                            if (message != null)
                            {
                                // Get selected options and comment
                                var options = new List<string>();
                                if (_feedbackPopup.Q<Toggle>("feedback_option_1").value) options.Add("Not factually correct");
                                if (_feedbackPopup.Q<Toggle>("feedback_option_2").value) options.Add("Not helpful");
                                if (_feedbackPopup.Q<Toggle>("feedback_option_3").value) options.Add("Not following instructions");
                                if (_feedbackPopup.Q<Toggle>("feedback_option_4").value) options.Add("Harmful or offensive");
                                if (_feedbackPopup.Q<Toggle>("feedback_option_5").value) options.Add("Answer took too long");

                                var comment = _feedbackPopup.Q<TextField>("feedback_comment").value;

                                // Submit feedback
                                SubmitFeedbackWithComment(message, 0, options, comment);

                                // Hide popup
                                _feedbackPopup.style.display = DisplayStyle.None;
                            }
                        }
                    };
                }

                // Set up auto-resizing for feedback comment field
                var feedbackCommentField = _feedbackPopup.Q<TextField>("feedback_comment");
                if (feedbackCommentField != null)
                {
                    feedbackCommentField.RegisterValueChangedCallback(evt =>
                    {
                        if (evt.newValue != null && evt.newValue.Contains("\n"))
                        {
                            feedbackCommentField.style.height = new StyleLength(StyleKeyword.Auto);
                        }
                    });
                }

                // Set up Discord button in feedback popup
                var feedbackDiscordButton = _feedbackPopup.Q<Button>("feedback_discord_button");
                if (feedbackDiscordButton != null)
                {
                    feedbackDiscordButton.clicked += () =>
                    {
                        Application.OpenURL("https://discord.gg/Xs364T7D9T");
                    };
                }

                // Set up thread ID copy button in feedback popup
                var copyThreadIdButton = _feedbackPopup.Q<Button>("copy_thread_id_button");
                if (copyThreadIdButton != null)
                {
                    copyThreadIdButton.clicked += () =>
                    {
                        var threadId = _chatThreads?.CurrentThread?.id ?? "unknown";
                        EditorGUIUtility.systemCopyBuffer = threadId;
                        CoplayLogger.Log($"Thread ID copied to clipboard: {threadId}");
                    };
                }
            }

            // Initialize thread review panel
            var threadReviewContainer = ui.Q<VisualElement>("thread_review_container");
            if (threadReviewContainer != null)
            {
                _threadReviewView.CreateThreadReviewPanel(threadReviewContainer);
            }
            else
            {
                CoplayLogger.LogError("Thread review container not found in UI");
            }

            ScrollToBottom(scroller);
            UpdateChatInputState(ui);
            UpdateChatInputPlaceholder(ui);
            UpdateOrchestratorUI(ui);
            UpdateSubmitButtonState();

            return ui;
        }

        private bool UpdateThinkingContainer(VisualElement ui, IVisualElementScheduledItem updateTask)
        {
            var thinkingContainer = ui.Q<VisualElement>("thinking");
            var cancelButton = ui.Q<Button>("cancel_thinking");
            if (_stateManager.CurrentState == CoplayState.WaitingForAI)
            {
                thinkingContainer.style.display = DisplayStyle.Flex;
                cancelButton.style.display = DisplayStyle.Flex;
                updateTask.Resume();
                return true;
            }
            else
            {
                thinkingContainer.style.display = DisplayStyle.None;
                cancelButton.style.display = DisplayStyle.None;
                updateTask.Pause();
                return false;
            }
        }

        private void UpdateChatInputState(VisualElement ui)
        {
            var chatInput = ui.Q<TextField>("chat_input");
            var submitButton = ui.Q<Button>("submit");

            bool isWaiting = _stateManager.CurrentState is CoplayState.WaitingForAI or
                             CoplayState.ExecutingAllFunctions;

            // Null check in case this is called before elements are ready (though unlikely with current setup)
            if (chatInput != null && submitButton != null && _assistantConfiguration.Mode != AssistantMode.Orchestrator)
            {
                chatInput.SetEnabled(!isWaiting && !_chatThreads.IsDoingSomething);
                submitButton.SetEnabled(!isWaiting && !_chatThreads.IsDoingSomething);
            }
        }

        private void UpdateOrchestratorUI(VisualElement ui)
        {
            var chatInput = ui.Q<TextField>("chat_input");
            var instructions = ui.Q<Label>("orchestrator_instructions");

            if (chatInput == null || instructions == null)
            {
                return;
            }

            if (_assistantConfiguration.Mode == AssistantMode.Orchestrator)
            {
                chatInput.style.display = DisplayStyle.None;
                instructions.style.display = DisplayStyle.Flex;
            }
            else
            {
                chatInput.style.display = DisplayStyle.Flex;
                instructions.style.display = DisplayStyle.None;
            }
            UpdateSubmitButtonState();
        }

        private void UpdateChatInputPlaceholder(VisualElement ui)
        {
            var chatInput = ui.Q<TextField>("chat_input");
            if (chatInput != null)
            {
                string placeholderText = _assistantConfiguration.Mode == AssistantMode.PipelineRecording
                    ? "Coplay will repeat your pipeline with the instructions you provide here"
                    : "What can I help with?";

#if UNITY_6000_0_OR_NEWER
                // Update the placeholder text using the correct Unity UI Elements API
                chatInput.textEdition.placeholder = placeholderText;
#else
                // Placeholder functionality not available in Unity versions before 6.0
                // Consider alternative approaches like setting a default value or using a label
                CoplayLogger.Log($"Placeholder text would be: {placeholderText}");
#endif
            }
        }

        private void UpdateThreadButtons(VisualElement ui)
        {
            var newThreadButton = ui.Q<Button>("new_thread");
            var historyButton = ui.Q<Button>("history");
            var enabled = _stateManager.CurrentState is CoplayState.Initialized or CoplayState.WaitingForUserToExecuteFunction or CoplayState.WaitingForUserMessage
                && !_chatThreads.IsDoingSomething;
            newThreadButton.SetEnabled(enabled);
            historyButton.SetEnabled(enabled);
        }

        /// <summary>
        /// Updates the pipeline recording panel visibility based on the current assistant mode
        /// </summary>
        private void UpdatePipelineRecordingPanelVisibility()
        {
            if (_pipelineRecordingViewFactory != null)
            {
                try
                {
                    bool isPipelineRecordingMode = _assistantConfiguration.Mode == AssistantMode.PipelineRecording;
                    _pipelineRecordingViewFactory.SetPanelVisibility(isPipelineRecordingMode);
                }
                catch (System.Exception ex)
                {
                    CoplayLogger.LogError("Error updating pipeline recording panel visibility: " + ex.Message);
                }
            }
        }

        private void SetupDragAndDrop(VisualElement ui)
        {
            ui.RegisterCallback<DragPerformEvent>(e =>
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                e.StopImmediatePropagation();
                DragAndDrop.AcceptDrag();

                _ = HandleDraggedAssets();

                async Task HandleDraggedAssets()
                {
                    foreach (var draggedObject in DragAndDrop.objectReferences)
                    {
                        try
                        {
                            await TryAttachAsset(draggedObject);
                        }
                        catch (Exception ex)
                        {
                            CoplayLogger.LogError($"Error attaching dragged asset: {ex.Message}");
                        }
                    }
                }
            });

            ui.RegisterCallback<DragUpdatedEvent>(e =>
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                e.StopImmediatePropagation();
            });
        }

        /// <summary>
        /// Checks if the current model supports image processing when images are attached
        /// </summary>
        /// <param name="imageCompatibilityDialog">The dialog to show if there's an incompatibility</param>
        /// <returns>True if compatible or no images attached, false if incompatible</returns>
        private bool CheckModelImageCompatibility(VisualElement imageCompatibilityDialog)
        {
            // Check if there are images attached and if the selected model supports image processing
            if (_context.HasAttachedImages() &&
                !string.IsNullOrEmpty(_assistantConfiguration.Model) &&
                !AIModelExtensions.SupportsImageProcessing(_assistantConfiguration.Model))
            {
                // Show error dialog
                _confirmationDialogView.Show(imageCompatibilityDialog);

                CoplayLogger.LogWarning($"Model {_assistantConfiguration.Model} does not support image processing, but images are attached.");
                return false;
            }

            return true;
        }

        private bool CheckCreditBalance()
        {
            bool isUsingClaudeModel = !string.IsNullOrEmpty(_assistantConfiguration.Model) &&
                                          _assistantConfiguration.Model.ToLower().Contains("claude");

            bool hasCreditsRemaining = _settingsController.TotalCredit > 0.0f;

            // Use the smart subscription validation that handles checking in the background when needed
            bool hasActiveSubscription = _subscriptionManager.IsSubscriptionValid();
            bool hasAnthropicKey = !string.IsNullOrEmpty(_settingsController.UserAnthropicKey); // TODO: check if the key will always be valid

            if (hasCreditsRemaining)
            {
                return true;
            }
            else if (isUsingClaudeModel && hasAnthropicKey)
            {
                return true;
            }
            else if (hasActiveSubscription)
            {
                // The user has a subscription but no credits remaining.
                _confirmationDialogView.Show(_topUpCreditsDialog);
                return false;
            }
            else if (hasAnthropicKey)
            {
                // Not using anthropic model but they have a key -- they need to subscribe in this case.
                // TODO: change the confirmation dialog message to not recommend adding an apikey
                _confirmationDialogView.Show(_subscribeDialog);
                return false;
            }
            else
            {
                _confirmationDialogView.Show(_endOfFreeTrialDialog);
                return false;
            }
        }

        private async Task HandleClipboardPaste()
        {
            try
            {
                // Use the ClipboardImageHelper to check if there's an image in the clipboard
                string imagePath = ClipboardImageHelper.GetImageFromClipboard();

                if (!string.IsNullOrEmpty(imagePath))
                {
                    // Image was found and saved to a temporary file
                    // Add the file to the context
                    await _context.AddAttachedFile(imagePath);

                    CoplayLogger.Log("Image Attached: Clipboard image has been attached to the context.");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Error handling clipboard paste: " + ex.Message);
            }
        }

        /// <summary>
        /// Attempts to attach an asset or file to the context.
        /// Works with both Unity assets and external files.
        /// </summary>
        /// <param name="asset">The asset to attach, can be null for external files</param>
        /// <param name="fullPath">Optional full system path for external files</param>
        private async Task TryAttachAsset(UnityEngine.Object asset, string fullPath = null)
        {
            string path = null;

            // If we have an asset object, try to get its path
            if (asset != null)
            {
                // Handle Project window assets
                path = AssetDatabase.GetAssetPath(asset);

                // Handle Scene objects that might be prefab instances
                if (string.IsNullOrEmpty(path) && asset is GameObject sceneObject)
                {
                    // Try to get the specific prefab instance that was dragged
                    GameObject correspondingPrefabObject = PrefabUtility.GetCorrespondingObjectFromSource(sceneObject);
                    if (correspondingPrefabObject != null)
                    {
                        path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(correspondingPrefabObject);
                    }
                    else
                    {
                        // Fallback to getting the prefab asset path directly
                        path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(sceneObject);
                    }
                }
            }
            // If we have a full path but no asset (external file)
            else if (!string.IsNullOrEmpty(fullPath))
            {
                // Check if the file is within the Unity project
                if (fullPath.StartsWith(Application.dataPath))
                {
                    // Convert to a project-relative path for Unity assets
                    string relativePath = "Assets" + fullPath.Substring(Application.dataPath.Length);

                    // Verify it's a valid asset path
                    UnityEngine.Object loadedAsset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(relativePath);
                    if (loadedAsset != null)
                    {
                        path = relativePath;
                    }
                    else
                    {
                        // Not a valid Unity asset, use full path
                        path = fullPath;
                    }
                }
                else
                {
                    // External file, use full path
                    path = fullPath;
                }
            }

            // Add the path to context if valid and not already added
            if (!string.IsNullOrEmpty(path) && !_context.AttachedFiles.Contains(path))
            {
                await _context.AddAttachedFile(path);
            }
        }

        private void CreateContextItems(VisualElement selectedContextList,
            VisualTreeAsset removableContextItemUxml, VisualTreeAsset imageContextItemUxml = null)
        {
            // Clean up existing thumbnail mappings before recreating items
            _thumbnailManager.CleanupElementTextures(selectedContextList);
            
            foreach (var attachedFile in _context.AttachedFiles)
            {
                // Check if this is an image file and we have the image template
                bool isImage = ImageProcessor.IsImage(attachedFile) && imageContextItemUxml != null;
                
                var contextItem = isImage ? imageContextItemUxml.Instantiate() : removableContextItemUxml.Instantiate();
                
                if (isImage)
                {
                    // For images, set tooltip on the main container and load thumbnail
                    contextItem.tooltip = attachedFile;
                    var thumbnail = contextItem.Q<VisualElement>("thumbnail");
                    if (thumbnail != null)
                    {
                        _thumbnailManager.LoadContextThumbnail(attachedFile, thumbnail);
                    }
                }
                else
                {
                    // For non-images, set label text and tooltip as before
                    var label = contextItem.Q<Label>();
                    label.text = Path.GetFileName(attachedFile);
                    label.tooltip = attachedFile;
                }
                
                contextItem.Q<Button>("remove").clicked += () => { _context.RemoveAttachedFile(attachedFile); };
                selectedContextList.Add(contextItem);
            }

            var addAttachmentButton = new Button
            {
                text = "+ Add attachment",
                name = "add_attachment"
            };
            addAttachmentButton.AddToClassList("font-small");
            addAttachmentButton.clicked += async () =>
            {
                try
                {
                    string path = EditorUtility.OpenFilePanel("Select File", "", "");
                    if (!string.IsNullOrEmpty(path) && !_context.AttachedFiles.Contains(path))
                    {
                        await _context.AddAttachedFile(path);
                    }
                }
                catch (Exception ex)
                {
                    CoplayLogger.LogError("Error adding attachment: " + ex.Message);
                }
            };
            selectedContextList.Add(addAttachmentButton);
        }

        private VisualElement CreateChatMessage(ChatMessage message, VisualTreeAsset chatMessageUxml)
        {
            var chatMessageUI = chatMessageUxml.Instantiate();
            chatMessageUI.name = message.Id;
            chatMessageUI.style.flexShrink = 0;

            var chatMessageContainer = chatMessageUI.Q<VisualElement>("chat_message_container");
            chatMessageContainer.viewDataKey = message.Id;
            var feedbackButtons = chatMessageUI.Q<VisualElement>("feedback_buttons");

            var autoApproveLimitReached = message.FunctionCalls.Any(f => !f.HasExecuted && f.AutoApproveLimitReached);
            if (message.ChatMessageRole == ChatMessageRole.Coplay && !autoApproveLimitReached)
            {
                chatMessageContainer.style.alignSelf = Align.FlexStart;
                chatMessageUI.Q<Label>("avatar").style.display = DisplayStyle.Flex;

                // Show feedback buttons for AI messages
                feedbackButtons.style.display = DisplayStyle.Flex;

                // Set up like button
                var likeButton = feedbackButtons.Q<Button>("like_button");
                var dislikeButton = feedbackButtons.Q<Button>("dislike_button");

                // Check if feedback has already been submitted for this message
                if (message.HasFeedback)
                {
                    // Disable both buttons
                    likeButton.SetEnabled(false);
                    dislikeButton.SetEnabled(false);

                    // Optionally, highlight the button that was selected
                    if (message.FeedbackScore.HasValue)
                    {
                        if (message.FeedbackScore.Value > 0)
                        {
                            likeButton.style.backgroundColor = new StyleColor(new Color(0.2f, 0.5f, 0.2f, 0.8f));
                        }
                        else
                        {
                            dislikeButton.style.backgroundColor = new StyleColor(new Color(0.5f, 0.2f, 0.2f, 0.8f));
                        }
                    }
                }
                else
                {
                    // Set up click handlers if feedback hasn't been submitted
                    likeButton.clicked += () =>
                    {
                        _feedbackPopup.style.display = DisplayStyle.None;
                        SubmitFeedback(message, 1);
                    };

                    dislikeButton.clicked += () =>
                    {
                        // Show feedback popup from root view instead of from chat message
                        ShowFeedbackPopup(message);
                    };
                }

                // Feedback popup is now handled from the root view
            }
            else
            {
                chatMessageContainer.style.alignSelf = Align.FlexEnd;
                chatMessageUI.Q<Label>("avatar").style.display = DisplayStyle.None;
                // Hide feedback buttons for user messages
                feedbackButtons.style.display = DisplayStyle.None;
            }

            var contentLabel = chatMessageUI.Q<TextField>("content");
            var reasoningBlock = chatMessageUI.Q<Foldout>("reasoning_block");
            var reasoningLabel = chatMessageUI.Q<Label>("reasoning");
            var hasContent = !string.IsNullOrWhiteSpace(message.Content);

            // If there's no content, or just whitespace, but there are function calls, don't add empty space
            if (!hasContent)
            {
                contentLabel.style.display = DisplayStyle.None;
                reasoningBlock.style.display = DisplayStyle.None;
            }
            else
            {
                // Format thinking blocks with light gray color
                string formattedContent = message.Content.Trim();
                if (!string.IsNullOrEmpty(formattedContent) && formattedContent.Contains("<thinking>"))
                {
                    reasoningBlock.style.display = DisplayStyle.Flex;
                    reasoningBlock.value = true;
                    // Find all thinking blocks and replace them with styled content
                    int startIndex = 0;
                    while ((startIndex = formattedContent.IndexOf("<thinking>", startIndex, StringComparison.Ordinal)) != -1)
                    {
                        int endIndex = formattedContent.IndexOf("</thinking>", startIndex, StringComparison.Ordinal);
                        if (endIndex != -1)
                        {
                            string thinkingContent = formattedContent.Substring(
                                startIndex + "<thinking>".Length,
                                endIndex - startIndex - "<thinking>".Length
                            );
#if UNITY_2022_1_OR_NEWER
                            // Replace the thinking block with color-styled content - add title and vertical bar
                            reasoningLabel.text = MarkdownFormatter.FormatMarkdown(thinkingContent);
#else
                            reasoningLabel.text = thinkingContent;
#endif

                            formattedContent = formattedContent
                                .Remove(startIndex, endIndex + "</thinking>".Length - startIndex);
                        }
                        else
                        {
                            break; // No closing tag found
                        }
                    }
                }
                else
                {
                    reasoningBlock.style.display = DisplayStyle.None;
                }

                // Truncate messages to prevent this error: A VisualElement must not allocate more than 65535 vertices.
                const int maxLength = 15000; // TODO: monitor this value to see if it's enough.
                if (!string.IsNullOrEmpty(formattedContent) && formattedContent.Length > maxLength)
                {
#if UNITY_2022_1_OR_NEWER
                    formattedContent = formattedContent.Substring(0, maxLength) + "\n\n<color=#707070>[Message truncated - exceeded " + maxLength + " characters]</color>";
#else
                    formattedContent = formattedContent.Substring(0, maxLength) + "\n\n[Message truncated - exceeded " +
                                    maxLength + " characters]";
#endif
                }

#if UNITY_2022_1_OR_NEWER
                contentLabel.value = MarkdownFormatter.FormatMarkdown(formattedContent);
                contentLabel.SetVerticalScrollerVisibility(ScrollerVisibility.Auto);
                contentLabel.Q<TextElement>().enableRichText = true;
#else
                contentLabel.value = formattedContent;
                contentLabel.style.maxHeight = new StyleLength(StyleKeyword.Auto);
#endif
            }

            if (message.FunctionCalls.Count > 0)
            {
                ShowFunctionCalls(chatMessageUI, message, hasContent);
            }

            if (autoApproveLimitReached)
            {
                ShowAutoApproveLimitReached(chatMessageUI, message);
            }

            var costLabel = chatMessageUI.Q<Label>("cost_label");
            if (costLabel != null)
            {
                if (message.ChatMessageRole == ChatMessageRole.Coplay && message.CostUsd.HasValue)
                {
                    costLabel.text = $"${message.CostUsd.Value:F4}";
                    costLabel.style.display = DisplayStyle.Flex;
                }
                else
                {
                    costLabel.style.display = DisplayStyle.None;
                }
            }

            // Apply current font size to this chat message
            Utils.ApplyFontSizeToRoot(chatMessageUI, _settingsController.FontSize);

            return chatMessageUI;
        }

        private void UpdateChatHistory(VisualElement messagesView, VisualTreeAsset chatMessageUxml, ScrollView scroller)
        {
            // Clean up textures before clearing messages
            _thumbnailManager.CleanupElementTextures(messagesView);
            messagesView.Clear();
            foreach (var message in _chatHistory.Messages)
            {
                if (!message.IsEmpty())
                {
                    var chatMessage = CreateChatMessage(message, chatMessageUxml);
                    messagesView.Add(chatMessage);
                }
            }
            UpdateThreadCostLabel();
            ScrollToBottom(scroller);
        }

        private void UpdateThreadCostLabel()
        {
            if (_threadCostLabel == null)
            {
                return;
            }
            
            // TODO: this may slow down UI for long threads. For now we're keeping it simple, but would be good to make it better.
            float total = 0f;
            foreach (var msg in _chatHistory.Messages)
            {
                if (msg.ChatMessageRole == ChatMessageRole.Coplay && msg.CostUsd.HasValue)
                {
                    total += msg.CostUsd.Value;
                }
            }
            _threadCostLabel.text = $"${total:F4}";
        }

        private void ScrollToBottom(ScrollView scroller)
        {
            if (scroller.verticalScroller.highValue - scroller.verticalScroller.value < 10)
            {
                // Only auto-scroll if Unity is already focused to prevent stealing focus
                // and we're not in play mode
                if (EditorWindow.focusedWindow != null && !_editorStateService.IsPlaying)
                {
                    scroller.schedule.Execute(() =>
                    {
                        scroller.verticalScroller.value = scroller.verticalScroller.highValue;
                    }).ExecuteLater(50);
                }
            }
        }

        /// <summary>
        /// Shows the feedback popup for a message
        /// </summary>
        /// <param name="message">The message to provide feedback for</param>
        private void ShowFeedbackPopup(ChatMessage message)
        {
            // Only show popup if feedback hasn't been submitted yet and popup exists
            if (!message.HasFeedback && _feedbackPopup != null)
            {
                // Store the message ID in a custom property for reference when submitting
                _feedbackPopup.userData = message.Id;

                // Show the popup
                _feedbackPopup.style.display = DisplayStyle.Flex;

                // Reset form
                _feedbackPopup.Q<Toggle>("feedback_option_1").value = false;
                _feedbackPopup.Q<Toggle>("feedback_option_2").value = false;
                _feedbackPopup.Q<Toggle>("feedback_option_3").value = false;
                _feedbackPopup.Q<Toggle>("feedback_option_4").value = false;
                _feedbackPopup.Q<Toggle>("feedback_option_5").value = false;
                _feedbackPopup.Q<TextField>("feedback_comment").value = "";

                // Update thread ID display
                var threadIdField = _feedbackPopup.Q<TextField>("thread_id_field");
                if (threadIdField != null)
                {
                    var threadId = _chatThreads?.CurrentThread?.id ?? "unknown";
                    threadIdField.value = $"thread_id: {threadId}";
                }
            }
        }

        /// <summary>
        /// Submits feedback with comment for a message
        /// </summary>
        /// <param name="message">The message to provide feedback for</param>
        /// <param name="score">The score for the feedback (1 for like, 0 for dislike)</param>
        /// <param name="options">Selected feedback options</param>
        /// <param name="comment">Additional comment</param>
        private async void SubmitFeedbackWithComment(ChatMessage message, float score, List<string> options, string comment)
        {
            try
            {
                // Format options and comment into a single string
                string optionsStr = options.Count > 0 ? string.Join(", ", options) : "None";
                string feedbackComment = $"Options: [{optionsStr}]";

                // Add user comment if provided
                if (!string.IsNullOrEmpty(comment))
                {
                    feedbackComment += $"\nComment: {comment}";
                }

                // Update the message's feedback state
                message.HasFeedback = true;
                message.FeedbackScore = score;

                // Notify that the message was updated to refresh the UI
                _chatHistory.NotifyMessageUpdated(message);

                // Pass the formatted comment to the SubmitFeedbackAsync method
                if (await _coplayApiClient.SubmitFeedbackAsync(message.Id, "correctness", score, feedbackComment))
                {
                    CoplayLogger.Log($"Feedback submitted for message {message.Id}: score={score}, options=[{optionsStr}], comment=\"{comment}\"");
                }
                else
                {
                    message.HasFeedback = false;
                    message.FeedbackScore = null;

                    // Notify that the message was updated to refresh the UI
                    _chatHistory.NotifyMessageUpdated(message);
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error submitting feedback for message {message.Id}", ex);

                message.HasFeedback = false;
                message.FeedbackScore = null;

                // Notify that the message was updated to refresh the UI
                _chatHistory.NotifyMessageUpdated(message);
            }
        }

        /// <summary>
        /// Submits feedback for a message
        /// </summary>
        /// <param name="message">The message to provide feedback for</param>
        /// <param name="score">The score for the feedback (1 for like, 0 for dislike)</param>
        private async void SubmitFeedback(ChatMessage message, float score)
        {
            try
            {
                // Update the message's feedback state
                message.HasFeedback = true;
                message.FeedbackScore = score;

                // Notify that the message was updated to refresh the UI
                _chatHistory.NotifyMessageUpdated(message);

                if (await _coplayApiClient.SubmitFeedbackAsync(message.Id, "correctness", score))
                {
                    CoplayLogger.Log($"Feedback submitted for message {message.Id}: score={score}");
                }
                else
                {
                    message.HasFeedback = false;
                    message.FeedbackScore = null;

                    // Notify that the message was updated to refresh the UI
                    _chatHistory.NotifyMessageUpdated(message);
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error submitting feedback for message {message.Id}", ex);

                message.HasFeedback = false;
                message.FeedbackScore = null;

                // Notify that the message was updated to refresh the UI
                _chatHistory.NotifyMessageUpdated(message);
            }
        }

        private void HandleMessageSubmission(string inputStr)
        {
            if (_assistantConfiguration.Mode == AssistantMode.Orchestrator && CheckCreditBalance())
            {
                _orchestratorProcessor.StartSession();
            }

            if (!string.IsNullOrEmpty(inputStr))
            {
                if (CheckModelImageCompatibility(_imageCompatibilityDialog) &&
                    CheckCreditBalance())
                {
                    _messageSubmittedSubject.OnNext(inputStr);
                    _chatInput.schedule.Execute(() => _chatInput.SetValueWithoutNotify(string.Empty));
                    // Clear draft after successful message submission
                    SaveDraft(string.Empty);
                }
            }
        }

        private void ShowFunctionCalls(VisualElement chatMessageUi, ChatMessage message, bool hasContent)
        {
            var functionCallsContainer = chatMessageUi.Q<VisualElement>("function_calls");
            functionCallsContainer.style.display = DisplayStyle.Flex;
            functionCallsContainer.style.marginTop = hasContent ? 10 : 0;

            // Create a single container for all function calls to eliminate spacing
            var allFunctionCallsContainer = new VisualElement();
            allFunctionCallsContainer.style.flexDirection = FlexDirection.Column;

            // Check if any function in this message has auto-approve limit reached
            bool isPreviewMode = message.FunctionCalls.Any(f => f.AutoApproveLimitReached);

            foreach (var functionCall in message.FunctionCalls)
            {
                allFunctionCallsContainer.Add(_functionQueueViewFactory.CreateFunctionQueueItem(functionCall, isPreviewMode));
            }

            functionCallsContainer.Add(allFunctionCallsContainer);

            // Only show Run All and Cancel All if fully initialized and no special functions are present
            bool showAggregateButtons = !_stateModel.IsExecutingAllFunctions &&
                                        !message.FunctionCalls.Any(f => f.RequiresUserApproval) &&
                                        !message.FunctionCalls.Any(f => f.AutoApproveLimitReached) &&
                                        message.FunctionCalls.Count(f => !(f.HasExecuted || f.IsCancelled)) > 1; // Only show aggregate buttons if there are more than one function to run

            if (showAggregateButtons)
            {
                var runAllButton = new Button();
                runAllButton.style.display = showAggregateButtons ? DisplayStyle.Flex : DisplayStyle.None;
                runAllButton.text = "Run All";
                runAllButton.clicked += () =>
                {
                    _stateManager.SetState(CoplayState.ExecutingAllFunctions);
                };

                var cancelAllButton = new Button();
                cancelAllButton.style.display = showAggregateButtons ? DisplayStyle.Flex : DisplayStyle.None;
                cancelAllButton.text = "✕";
                cancelAllButton.tooltip = "Cancel All";
                cancelAllButton.style.width = 25;
                cancelAllButton.style.backgroundColor = new Color(1f, 0.3f, 0.3f);
                cancelAllButton.clicked += () =>
                {
                    _functionExecutionController.CancelAllFunctions();
                };

                var allContainer = new VisualElement();
                allContainer.style.flexDirection = FlexDirection.Row;
                allContainer.style.alignItems = Align.Center;
                allContainer.Add(runAllButton);
                allContainer.Add(cancelAllButton);
                functionCallsContainer.Add(allContainer);
            }

            // Handle image previews using UXML structure
            var imagePreviewContainer = chatMessageUi.Q<VisualElement>("image_preview");
            imagePreviewContainer.style.flexDirection = FlexDirection.Row;
            imagePreviewContainer.style.flexWrap = Wrap.Wrap;

            bool hasImages = false;

            foreach (var functionCall in message.FunctionCalls)
            {
                if (!string.IsNullOrEmpty(functionCall.ImagePath) && (File.Exists(functionCall.ImagePath) || functionCall.ImagePath.StartsWith("data:")))
                {
                    hasImages = true;

                    var imageItem = new VisualElement();
                    imageItem.AddToClassList("image-item");

                    var imageView = new VisualElement();
                    imageView.AddToClassList("image-image");
                    imageItem.Add(imageView);

                    // Load and display the image
                    try
                    {
                        var imageBytes = functionCall.ImagePath.StartsWith("data:") ? ContextHelpers.LoadDataUriToTexture(functionCall.ImagePath) : File.ReadAllBytes(functionCall.ImagePath);
                        var texture = new Texture2D(2, 2);
                        if (texture.LoadImage(imageBytes))
                        {
                            // Set the background image
                            imageView.style.backgroundImage = new StyleBackground(texture);

                            // Calculate proper aspect ratio and sizing
                            float aspectRatio = (float)texture.width / texture.height;
                            float maxWidth = 300f;
                            float maxHeight = 200f;

                            float displayWidth = maxWidth;
                            float displayHeight = maxWidth / aspectRatio;

                            if (displayHeight > maxHeight)
                            {
                                displayHeight = maxHeight;
                                displayWidth = maxHeight * aspectRatio;
                            }

                            imageView.style.width = displayWidth;
                            imageView.style.height = displayHeight;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Show error in the image container
                        var errorLabel = new Label($"Error loading image: {ex.Message}");
                        errorLabel.style.color = new Color(1f, 0.5f, 0.5f, 1f);
                        imageView.Clear();
                        imageView.Add(errorLabel);
                    }
                    imagePreviewContainer.Add(imageItem);
                }
            }

            // Show or hide the image preview container
            imagePreviewContainer.style.display = hasImages ? DisplayStyle.Flex : DisplayStyle.None;

            if (!string.IsNullOrEmpty(message.ActionResultToShowInFoldout))
            {
                var foldout = new Foldout();
                foldout.text = "View Raw Result";
                foldout.value = false; // Collapsed by default

                var resultContent = new Label { text = message.ActionResultToShowInFoldout, enableRichText = true };
                resultContent.style.whiteSpace = WhiteSpace.Normal;
                resultContent.style.marginLeft = 15; // Indent the content
                resultContent.style.paddingTop = 5;
                resultContent.style.paddingBottom = 5;

                foldout.Add(resultContent);
                functionCallsContainer.Add(foldout);
            }
        }

        private void ShowAutoApproveLimitReached(VisualElement chatMessageUi, ChatMessage message)
        {
            var autoApproveLimitContainer = chatMessageUi.Q<VisualElement>("auto_approve_limit_container");
            var functionCallsContainer = chatMessageUi.Q<VisualElement>(FunctionCallsContainerName);

            // Show the auto-approve limit container
            autoApproveLimitContainer.style.display = DisplayStyle.Flex;

            // Add pending approval styling to indicate functions are blocked
            functionCallsContainer.AddToClassList("pending-approval");

            // Update the label with the actual count
            var countLabel = autoApproveLimitContainer.Q<Label>("auto_approve_count_label");
            if (countLabel != null)
            {
                var autoFunctionCallLimit = _settingsController.AutoFunctionCallLimit;
                countLabel.text = $"Coplay has auto-approved {autoFunctionCallLimit} API requests. The functions below are pending your approval. Click 'Proceed' to reset the count and execute them.";
            }

            // Configure the proceed button
            var proceedButton = autoApproveLimitContainer.Q<Button>("proceed_button");
            proceedButton.clicked += () =>
            {
                // Reset the auto-approve limit flag for all functions
                foreach (var func in message.FunctionCalls.Where(f => f.AutoApproveLimitReached))
                {
                    func.AutoApproveLimitReached = false;
                    func.RequiresUserApproval = false;
                }

                // Hide the container and remove pending approval styling
                functionCallsContainer.RemoveFromClassList("pending-approval");
                _chatHistory.UpdateMessage(message.Id);

                // Execute all functions
                _stateManager.SetState(CoplayState.ExecutingAllFunctions);
            };

            var adjustLimitButton = autoApproveLimitContainer.Q<Button>("adjust_limit_button");
            adjustLimitButton.clicked += ToggleSettingsDrawer;
        }

        private void OnSubmitClicked()
        {
            HandleMessageSubmission(_chatInput.value);
        }

        private void OnStopOrchestratorClicked()
        {
            _orchestratorProcessor.StopSession();
        }

        private void UpdateSubmitButtonState()
        {
            if (_submitButton == null) return;

            _submitButton.clicked -= OnSubmitClicked;
            _submitButton.clicked -= OnStopOrchestratorClicked;

            if (_assistantConfiguration.Mode == AssistantMode.Orchestrator && _orchestratorProcessor.IsProcessing)
            {
                _submitButton.text = "\uf28d";
                _submitButton.tooltip = "Stop Orchestrator Session";
                _submitButton.AddToClassList("stop-button");
                _submitButton.clicked += OnStopOrchestratorClicked;
            }
            else
            {
                _submitButton.text = "\uf138";
                _submitButton.tooltip = "";
                _submitButton.RemoveFromClassList("stop-button");
                _submitButton.clicked += OnSubmitClicked;
            }
        }
        #region Draft Persistence

        private string GetDraftKey() => DraftKeyPrefix + _chatThreads?.CurrentThread?.id;

        private void SaveDraft(string text)
        {
            var key = GetDraftKey();
            if (string.IsNullOrWhiteSpace(text))
                EditorPrefs.DeleteKey(key);
            else
                EditorPrefs.SetString(key, text);
        }

        private string LoadDraft()
        {
            var key = GetDraftKey();
            return EditorPrefs.HasKey(key) ? EditorPrefs.GetString(key) : string.Empty;
        }

        /// <summary>
        /// Checks for thread reviews asynchronously when a new thread is loaded
        /// </summary>
        /// <param name="ui">The UI container to show the review popup in</param>
        private async Task CheckForThreadReviewAsync(VisualElement ui)
        {
            try
            {
                await _chatThreads.CheckForThreadReview(this);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error checking for thread review in ChatView: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Shows a thread review suggestion with diff content in the bottom panel
        /// </summary>
        /// <param name="diffContent">The diff content to display and edit</param>
        /// <param name="onResponse">Callback when user responds (accepted, edited diff content)</param>
        public void ShowThreadReview(string diffContent, Action<bool, string> onResponse)
        {
            if (_threadReviewView != null)
            {
                _threadReviewView.Show(diffContent, onResponse);
            }
        }

        #endregion

        #region Drawer Management

        /// <summary>
        /// Initializes drawer references for centralized management
        /// </summary>
        private void InitializeDrawers(VisualElement ui)
        {
            _historyDrawer = ui.Q<ScrollView>("history");
            _settingsDrawer = ui.Q<VisualElement>("settings_panel");
            _accountDrawer = ui.Q<VisualElement>("account_panel");
            _drawers = new[] { _historyDrawer, _settingsDrawer, _accountDrawer };
        }

        /// <summary>
        /// Closes all drawers except the specified one
        /// </summary>
        /// <param name="keepOpen">The drawer to keep open, or null to close all</param>
        private void CloseOtherDrawers(VisualElement keepOpen = null)
        {   
            foreach (var drawer in _drawers)
            {
                if (drawer != null && drawer != keepOpen && drawer.style.display == DisplayStyle.Flex)
                {
                    drawer.style.display = DisplayStyle.None;
                }
            }
        }

        /// <summary>
        /// Toggles a drawer, automatically closing others when opening
        /// </summary>
        /// <param name="drawer">The drawer to toggle</param>
        private void ToggleDrawer(VisualElement drawer)
        {
            if (drawer == null) return;

            var newDisplayState = drawer.style.display == DisplayStyle.Flex ? DisplayStyle.None : DisplayStyle.Flex;
            drawer.style.display = newDisplayState;

            // If opening this drawer, close all others
            if (newDisplayState == DisplayStyle.Flex)
            {
                CloseOtherDrawers(drawer);
            }
        }

        /// <summary>
        /// Public method to toggle the history drawer
        /// </summary>
        public void ToggleHistoryDrawer() => ToggleDrawer(_historyDrawer);

        /// <summary>
        /// Public method to toggle the settings drawer
        /// </summary>
        public void ToggleSettingsDrawer() => ToggleDrawer(_settingsDrawer);

        /// <summary>
        /// Public method to toggle the account drawer
        /// </summary>
        public void ToggleAccountDrawer() => ToggleDrawer(_accountDrawer);

        #endregion
    }
}
