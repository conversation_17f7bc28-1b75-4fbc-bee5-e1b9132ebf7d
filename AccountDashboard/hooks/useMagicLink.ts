import { useToast } from "@/components/ui/toast";
import { sendMagicLinkEmail, generateMagicLinkRedirectUrl } from "@/services/authService";
import { useAsyncOperation } from "@/utils/formHelpers";

interface UseMagicLinkProps {
  deviceId?: string | null;
  onSuccess?: () => void;
}

interface UseMagicLinkReturn {
  status: string;
  sendMagicLink: (email: string) => Promise<void>;
  resetStatus: () => void;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
}

export function useMagicLink({ deviceId, onSuccess }: UseMagicLinkProps = {}): UseMagicLinkReturn {
  const toast = useToast();

  const { execute, status, isLoading, resetStatus } = useAsyncOperation(
    async (email: string) => {
      const redirectUrl = generateMagicLinkRedirectUrl(deviceId);
      await sendMagicLinkEmail(email, redirectUrl);
      onSuccess?.();
    },
    () => {
      toast.success("Login link sent. Check your email!");
    },
    (error) => {
      console.error(error);
      toast.error(error.message || "Error sending magic link");
    }
  );

  return {
    status,
    sendMagicLink: execute,
    resetStatus,
    isLoading,
    isSuccess: status === "success",
    isError: status === "error",
  };
}
