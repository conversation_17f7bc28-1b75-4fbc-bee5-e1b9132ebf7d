from unittest.mock import Mock
import pytest
import uuid
 
from datetime import datetime, timezone
import os

from coplay.models import CoplayRun, CoplayUsage, AIModel
import coplay.bigquery.usage as usage_mod
from coplay.bigquery.usage import insert_run_data, USAGE_DATA_SCHEMA, insert_rows_to_table


@pytest.mark.integration
class TestBigQueryIntegration:
    """Integration tests for BigQuery usage data insertion.
    
    These tests require Google Cloud credentials to be configured.
    """
    
    @pytest.fixture
    def sample_run(self) -> CoplayRun:
        """Create a realistic CoplayRun object based on the actual BigQuery schema."""
        return self._create_sample_run_from_schema()
    
    def _create_sample_run_from_schema(self) -> CoplayRun:
        """Generate sample CoplayUsage data based on USAGE_DATA_SCHEMA."""
        # Create a mapping of schema fields to sample values
        schema_field_values = {}
        
        for field in USAGE_DATA_SCHEMA:
            field_name = field.name
            field_type = field.field_type
            
            if field_name == "timestamp":
                # Skip timestamp as it's auto-generated in _usage_to_row
                continue
            elif field_name in ["input_tokens", "output_tokens", "total_tokens", "cache_read_tokens",
                               "cache_write_tokens", "non_cache_tokens", "payable_tokens"]:
                # Generate realistic token counts
                base_value = {"input_tokens": 1500, "output_tokens": 800, "total_tokens": 2300,
                             "cache_read_tokens": 500, "cache_write_tokens": 200, "non_cache_tokens": 1600,
                             "payable_tokens": 1600}.get(field_name, 100)
                schema_field_values[field_name] = base_value
            elif field_name == "credit_balance":
                schema_field_values[field_name] = 25.50
            elif field_name == "cost_usd":
                schema_field_values[field_name] = 0.012345
            elif field_name == "message_id":
                schema_field_values[field_name] = f"test_msg_{uuid.uuid4()}"
            elif field_name == "model":
                schema_field_values[field_name] = AIModel.CLAUDE_3_5_SONNET
            elif field_name == "thread_id":
                schema_field_values[field_name] = f"test_thread_{uuid.uuid4()}"
            elif field_name == "user_id":
                schema_field_values[field_name] = f"test_user_{uuid.uuid4()}"
            elif field_name == "chargeable":
                schema_field_values[field_name] = True
            else:
                # Handle any new fields that might be added to the schema
                if field_type == "INTEGER":
                    schema_field_values[field_name] = 0
                elif field_type == "STRING":
                    schema_field_values[field_name] = f"test_{field_name}_{uuid.uuid4()}"
                elif field_type == "BOOLEAN":
                    schema_field_values[field_name] = True
                elif field_type == "NUMERIC":
                    schema_field_values[field_name] = 0.001
                elif field_type == "TIMESTAMP":
                    schema_field_values[field_name] = datetime.now(timezone.utc)
                else:
                    schema_field_values[field_name] = f"unknown_type_{field_name}"
        
        # Validate that we've covered all schema fields (excluding timestamp which is auto-generated)
        expected_field_count = len([f for f in USAGE_DATA_SCHEMA if f.name != "timestamp"])
        actual_field_count = len(schema_field_values)
        
        assert actual_field_count == expected_field_count, (
            f"Schema field count mismatch: expected {expected_field_count}, got {actual_field_count}. "
            f"Schema fields: {[f.name for f in USAGE_DATA_SCHEMA if f.name != 'timestamp']}, "
            f"Generated fields: {list(schema_field_values.keys())}"
        )
        
        usage = CoplayUsage(**schema_field_values)
        run = CoplayRun(
            usage=usage,
            message_id=schema_field_values["message_id"],
            model=schema_field_values["model"],
            thread_id=schema_field_values["thread_id"],
            user_id=schema_field_values["user_id"],
            prompt=schema_field_values["prompt"],
            tool_calls=schema_field_values["tool_calls"],
            tool_outputs=schema_field_values["tool_outputs"],
            chargeable=schema_field_values["chargeable"],
        )
        return run
    
    @pytest.mark.asyncio
    async def test_single_run_insertion(self, sample_run: CoplayRun, monkeypatch):
        """Test inserting a single usage record to BigQuery production tables."""
        # Skip if no Google Cloud credentials are available
        if not self._has_gcp_credentials():
            pytest.skip("Google Cloud credentials not available")
    
        mock_func = Mock(side_effect=insert_rows_to_table)
        
        monkeypatch.setattr(usage_mod, "insert_rows_to_table", mock_func)
    
        result = await insert_run_data(sample_run)
        row = mock_func.call_args[0][2][0]
        
        # Verify successful insertion
        assert result is True, "BigQuery insertion should succeed"

        # Verify that cost_usd is converted to a JSON-serializable type
        assert isinstance(row["cost_usd"], (int, float, str))
        assert row["cost_usd"] == f"{sample_run.usage.cost_usd:.6f}"
        
        # Verify other fields are present and correct
        assert row["input_tokens"] == sample_run.usage.input_tokens
        assert row["output_tokens"] == sample_run.usage.output_tokens
        assert row["total_tokens"] == sample_run.usage.total_tokens
        assert row["model"] == sample_run.model.value
        assert row["user_id"] == sample_run.user_id
        assert row["chargeable"] == sample_run.chargeable
        assert row["credit_balance"] == f"{float(sample_run.usage.credit_balance):.6f}"
        
        # Verify timestamp is present and properly formatted
        assert "timestamp" in row
        assert isinstance(row["timestamp"], str)
    
    def _has_gcp_credentials(self) -> bool:
        """Check if Google Cloud credentials are available."""
        # Check for service account key file
        if os.getenv("GOOGLE_APPLICATION_CREDENTIALS"):
            return True
        
        # Check for gcloud default credentials (common in local development)
        try:
            from google.auth import default
            credentials, project = default()
            return credentials is not None
        except Exception:
            return False
