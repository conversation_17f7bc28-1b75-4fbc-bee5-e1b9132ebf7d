[{"type": "function", "function": {"name": "get_scene_view_screenshot", "strict": true, "description": "Capture a screenshot of the current scene view. This tool allows you to directly see or analyze the visual appearance of objects in the Unity scene view. Use this tool after you've made visual changes to a scene or prefab to double check your work.", "parameters": {"type": "object", "properties": {"gameObjectPath": {"type": ["string", "null"], "description": "Optional path of a game object in the active hierarchy to focus on before taking the screenshot (e.g. '/Root/Parent/Child'). If provided, the scene view will be focused on this object. If empty or null, no focusing will be performed."}, "includeUI": {"type": ["boolean", "null"], "description": "Optional flag to include UI elements (gizmos, handles, overlays) in the screenshot. Defaults to false if not specified. When false, only the camera view is captured without UI elements."}}, "required": ["gameObjectPath", "includeUI"], "additionalProperties": false}}}]