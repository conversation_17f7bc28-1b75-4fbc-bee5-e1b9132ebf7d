import { z } from "zod";

// Shared validation schemas
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters");

export const emailSchema = z
  .string()
  .email("Please enter a valid email address");

// Shared password confirmation validation
export const createPasswordConfirmationRefine = () => ({
  refine: (data: { password: string; confirmPassword: string }) => 
    data.password === data.confirmPassword,
  error: {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  },
});

// Login schema
export const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

// Sign up schema
export const signUpSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine(
    (data) => data.password === data.confirmPassword,
    {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }
  );

// Reset password schema
export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine(
    (data) => data.password === data.confirmPassword,
    {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }
  );

// Forgot password schema
export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

// Export types
export type LoginFormData = z.infer<typeof loginSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
