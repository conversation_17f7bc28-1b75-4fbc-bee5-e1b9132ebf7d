#!/usr/bin/env python3
"""
Script to dump prompts from BigQuery to JSON files organized by keyword.
"""

import json
import os
import argparse
import re
from typing import Dict, List, Set
from google.cloud import bigquery
from pathlib import Path


def setup_bigquery_client():
    """Initialize and return BigQuery client."""
    return bigquery.Client()


def ensure_folder_exists(folder_path: str) -> None:
    """Create folder if it doesn't exist."""
    Path(folder_path).mkdir(parents=True, exist_ok=True)


def load_existing_prompts(file_path: str) -> Dict[str, Dict]:
    """
    Load existing prompts from JSON file.
    Returns a dictionary with prompt text as key and prompt data as value.
    """
    if not os.path.exists(file_path):
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert list format to dict for easier duplicate checking
        if isinstance(data, list):
            return {item.get('prompt', ''): item for item in data}
        elif isinstance(data, dict):
            return data
        else:
            return {}
    except (json.JSONDecodeError, FileNotFoundError):
        return {}


def extract_prompt_from_message(message: str) -> str:
    """
    Extract the actual prompt from a log message.
    
    Examples:
    "[Mode: Orchestrator] User prompt: Task 10: Create a whimsical..." 
    -> "Task 10: Create a whimsical..."
    
    "User prompt: Do something cool" -> "Do something cool"
    """
    if not message:
        return ""
    
    # Try different patterns to extract the prompt
    patterns = [
        r'User prompt:\s*(.*)',
        r'user prompt:\s*(.*)', 
        r'prompt:\s*(.*)',
        r'Prompt:\s*(.*)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
        if match:
            return match.group(1).strip()
    
    # If no pattern matches, return the original message
    return message.strip()


def query_bigquery_prompts(client: bigquery.Client, keyword: str, exact_word: bool = False) -> List[str]:
    """
    Query BigQuery for distinct prompts containing the specified keyword.
    
    Args:
        client: BigQuery client
        keyword: The keyword to search for
        exact_word: If True, searches for keyword with spaces around it (exact word match)
    """
    if exact_word:
        # Search for keyword with spaces around it to match whole words
        search_pattern = f"% {keyword.lower()} %"
        match_type = "exact word"
    else:
        # Search for keyword anywhere in the text (partial match)
        search_pattern = f"%{keyword.lower()}%"
        match_type = "partial"
    
    query = f"""
    SELECT DISTINCT
      prompt
    FROM
      `langsmith_logs.curated_runs`
    WHERE
      LOWER(prompt) LIKE '{search_pattern}'
    """
    
    print(f"Running BigQuery query for keyword: '{keyword}' ({match_type} match) - langsmith_logs table")
    query_job = client.query(query)
    results = query_job.result()
    
    prompts = []
    for row in results:
        if row.prompt and row.prompt.strip():
            prompts.append(row.prompt.strip())
    
    print(f"Found {len(prompts)} distinct prompts containing '{keyword}' ({match_type}) from langsmith_logs")
    return prompts


def query_coplay_logs_prompts(client: bigquery.Client, keyword: str, exact_word: bool = False) -> List[str]:
    """
    Query BigQuery coplay_analytics.coplay_logs_* tables for prompts containing the specified keyword.
    
    Args:
        client: BigQuery client
        keyword: The keyword to search for
        exact_word: If True, searches for keyword with spaces around it (exact word match)
    """
    if exact_word:
        # Search for keyword with spaces around it to match whole words
        search_pattern = f"% {keyword.lower()} %"
        match_type = "exact word"
    else:
        # Search for keyword anywhere in the text (partial match)
        search_pattern = f"%{keyword.lower()}%"
        match_type = "partial"
    
    query = f"""
    SELECT DISTINCT
      jsonPayload.message as message
    FROM
      `coplay_analytics.coplay_logs_*`
    WHERE
      jsonPayload.message IS NOT NULL
      AND LOWER(jsonPayload.message) LIKE '%prompt%'
      AND LOWER(jsonPayload.message) LIKE '{search_pattern}'
    """
    
    print(f"Running BigQuery query for keyword: '{keyword}' ({match_type} match) - coplay_analytics table")
    query_job = client.query(query)
    results = query_job.result()
    
    prompts = []
    for row in results:
        if row.message and row.message.strip():
            # Extract the actual prompt from the log message
            extracted_prompt = extract_prompt_from_message(row.message)
            if extracted_prompt and extracted_prompt.strip():
                prompts.append(extracted_prompt.strip())
    
    print(f"Found {len(prompts)} distinct prompts containing '{keyword}' ({match_type}) from coplay_analytics")
    return prompts


def merge_prompts(existing_prompts: Dict[str, Dict], new_prompts: List[str]) -> Dict[str, Dict]:
    """
    Merge new prompts with existing ones, avoiding duplicates.
    Each prompt gets a 'negative' property set to False if not already present.
    """
    merged = existing_prompts.copy()
    added_count = 0
    
    for prompt in new_prompts:
        if prompt not in merged:
            merged[prompt] = {
                'prompt': prompt,
                'negative': ""
            }
            added_count += 1
        else:
            # Preserve existing 'negative' value if it exists
            if 'negative' not in merged[prompt]:
                merged[prompt]['negative'] = ""
    
    print(f"Added {added_count} new prompts")
    print(f"Skipped {len(new_prompts) - added_count} duplicates")
    return merged


def save_prompts_to_json(file_path: str, prompts_dict: Dict[str, Dict]) -> None:
    """
    Save prompts dictionary to JSON file as a list format.
    """
    # Convert dict back to list format for cleaner JSON structure
    prompts_list = list(prompts_dict.values())
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(prompts_list, f, indent=2, ensure_ascii=False)
    
    print(f"Saved {len(prompts_list)} prompts to {file_path}")


def main():
    parser = argparse.ArgumentParser(
        description='Dump prompts from BigQuery to JSON files by keyword'
    )
    parser.add_argument(
        'keyword',
        help='Keyword to search for in prompts (e.g., "ui")'
    )
    parser.add_argument(
        '--output-folder',
        default='all_prompts_by_keyword',
        help='Output folder for JSON files (default: all_prompts_by_keyword)'
    )
    parser.add_argument(
        '--exact-word',
        action='store_true',
        help='Search for exact word matches with spaces around keyword (e.g., " ui " instead of "ui")'
    )
    
    args = parser.parse_args()
    
    # Setup paths
    keyword = args.keyword.lower()
    output_folder = args.output_folder
    json_filename = f"{keyword}.json"
    json_file_path = os.path.join(output_folder, json_filename)
    
    # Ensure output folder exists
    ensure_folder_exists(output_folder)
    
    # Initialize BigQuery client
    try:
        client = setup_bigquery_client()
    except Exception as e:
        print(f"Error initializing BigQuery client: {e}")
        print("Make sure you have Google Cloud credentials configured.")
        return 1
    
    # Load existing prompts
    print(f"Checking for existing file: {json_file_path}")
    existing_prompts = load_existing_prompts(json_file_path)
    print(f"Found {len(existing_prompts)} existing prompts")
    
    all_new_prompts = []
    
    langsmith_prompts = query_bigquery_prompts(client, keyword, exact_word=args.exact_word)
    all_new_prompts.extend(langsmith_prompts)
    
    coplay_prompts = query_coplay_logs_prompts(client, keyword, exact_word=args.exact_word)
    all_new_prompts.extend(coplay_prompts)
    
    print(f"Total prompts found across all tables: {len(all_new_prompts)}")
    
    # Merge prompts
    merged_prompts = merge_prompts(existing_prompts, all_new_prompts)
    
    # Save to file
    try:
        save_prompts_to_json(json_file_path, merged_prompts)
        print(f"Successfully updated {json_filename}")
    except Exception as e:
        print(f"Error saving to file: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
