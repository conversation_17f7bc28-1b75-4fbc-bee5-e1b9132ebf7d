using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Coplay.Tests.Benchmark
{
    [Serializable]
    public class BenchmarkExercise
    {
        public string Name { get; set; }
        public string Language { get; set; }
        public string ExercisePath { get; set; }
        public List<string> SolutionFiles { get; set; } = new List<string>();
        public List<string> TestFiles { get; set; } = new List<string>();
        public List<string> ExampleFiles { get; set; } = new List<string>();
        public string Instructions { get; set; }
        public string Introduction { get; set; }
        public Dictionary<string, object> Config { get; set; } = new Dictionary<string, object>();
        
        [JsonIgnore]
        public string FullExercisePath => ExercisePath;
        
        public override string ToString()
        {
            return $"{Language}/{Name}";
        }
    }
    
    [Serializable]
    public class BenchmarkResult
    {
        public string ExerciseName { get; set; }
        public string Language { get; set; }
        public string Model { get; set; }
        public bool Success { get; set; }
        public List<bool> TestOutcomes { get; set; } = new List<bool>();
        public double Cost { get; set; }
        public double Duration { get; set; }
        public int TestTimeouts { get; set; }
        public int ErrorOutputs { get; set; }
        public int UserAsks { get; set; }
        public int MalformedResponses { get; set; }
        public int SyntaxErrors { get; set; }
        public int CompilationErrors { get; set; }
        public string CommitHash { get; set; }
        public DateTime Timestamp { get; set; }
        public List<string> FunctionsUsed { get; set; } = new List<string>();
        public string ErrorMessage { get; set; }
        
        public BenchmarkResult()
        {
            Timestamp = DateTime.UtcNow;
        }
    }
    
    [Serializable]
    public class BenchmarkSession
    {
        public string SessionId { get; set; }
        public string Model { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public List<BenchmarkResult> Results { get; set; } = new List<BenchmarkResult>();
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public double TotalCost { get; set; }
        public double TotalDuration { get; set; }
        
        public BenchmarkSession()
        {
            SessionId = Guid.NewGuid().ToString();
            StartTime = DateTime.UtcNow;
        }
        
        public void Complete()
        {
            EndTime = DateTime.UtcNow;
        }
        
        public double SuccessRate => CompletedExercises > 0 ? (double)Results.FindAll(r => r.Success).Count / CompletedExercises : 0.0;
    }
}
