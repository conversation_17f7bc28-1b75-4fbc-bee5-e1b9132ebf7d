using System;
using Newtonsoft.Json;

namespace Coplay.Common.CoplayApi.Models
{
    /// <summary>
    /// Response model for credit top-up
    /// </summary>
    [Serializable]
    public class TopUpResponse
    {
        /// <summary>
        /// Whether the top-up was successful
        /// </summary>
        [JsonProperty("success")]
        public bool Success { get; set; }

        /// <summary>
        /// New credit balance after top-up
        /// </summary>
        [JsonProperty("new_balance")]
        public float? NewBalance { get; set; }

        /// <summary>
        /// Stripe payment intent ID
        /// </summary>
        [JsonProperty("transaction_id")]
        public string TransactionId { get; set; }

        /// <summary>
        /// Error message if top-up failed
        /// </summary>
        [JsonProperty("error")]
        public string Error { get; set; }
    }
}
