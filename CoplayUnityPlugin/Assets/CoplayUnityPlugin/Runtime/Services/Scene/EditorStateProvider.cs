using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.SceneManagement;
using Coplay.Services.Threading;
using Microsoft.Extensions.Logging;
using Coplay.Services.Scene.Models;
using UnityScene = UnityEngine.SceneManagement.Scene;

#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

namespace Coplay.Services.Scene
{
    /// <summary>
    /// Provider for Unity editor state
    /// </summary>
    public class EditorStateProvider : IEditorStateProvider
    {
        private readonly IEditorThreadHelper _editorThreadHelper;
        private readonly IEditorStateService _editorStateService;
        private readonly ILogger<EditorStateProvider> _logger;

        /// <summary>
        /// Creates a new editor state provider
        /// </summary>
        public EditorStateProvider(IEditorThreadHelper editorThreadHelper, IEditorStateService editorStateService, ILogger<EditorStateProvider> logger)
        {
            _editorThreadHelper = editorThreadHelper;
            _editorStateService = editorStateService;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current editor state
        /// </summary>
        [ItemCanBeNull]
        public async Task<EditorState> GetEditorStateAsync()
        {
            try
            {
                // Get the editor state on the editor thread
                var state = await _editorThreadHelper.ExecuteOnEditorThreadAsync(GetEditorState);
                return state;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting editor state. ");
                return null;
            }
        }

        /// <summary>
        /// Gets the current editor state
        /// </summary>
        /// <returns>The editor state as a JSON string</returns>
        private EditorState GetEditorState()
        {
            // Get the base editor state
            var state = new EditorState
            {
                PlayMode = GetPlayMode(),
                SelectedGameObjects = GetSelectedGameObjects(),
                HasCompilationErrors = CheckForCompilationErrors(),
                ActiveAssetPath = GetActiveAssetPath()
            };
            
            return state;
        }
        
        /// <summary>
        /// Gets the path of the currently active asset in the editor (scene, prefab, etc.)
        /// </summary>
        /// <returns>The path of the active asset, or null if none is active</returns>
        private string GetActiveAssetPath()
        {
#if UNITY_EDITOR
            // Check if a prefab is open in the Prefab Mode
            var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
            if (prefabStage != null)
            {
                return prefabStage.assetPath;
            }
            
            // Check if a scene is open
            var activeScene = SceneManager.GetActiveScene();
            if (!string.IsNullOrEmpty(activeScene.path))
            {
                return activeScene.path;
            }
            
            // Check if any asset is selected in the Project window
            var selectedAssets = Selection.GetFiltered<UnityEngine.Object>(SelectionMode.Assets);
            if (selectedAssets != null && selectedAssets.Length > 0)
            {
                var assetPath = AssetDatabase.GetAssetPath(selectedAssets[0]);
                if (!string.IsNullOrEmpty(assetPath))
                {
                    return assetPath;
                }
            }
            
            return null;
#else
            return null;
#endif
        }
        
        /// <summary>
        /// Checks if there are any compilation errors in the Unity project
        /// </summary>
        /// <returns>True if there are compilation errors, false otherwise</returns>
        private bool CheckForCompilationErrors()
        {
#if UNITY_EDITOR
            // This must be called from the main thread
            return EditorUtility.scriptCompilationFailed;
#else
            return false;
#endif
        }

        /// <summary>
        /// Gets the current play mode state
        /// </summary>
        /// <returns>True if in play mode, false otherwise</returns>
        private bool GetPlayMode()
        {
            return _editorStateService.IsPlaying;
        }

        /// <summary>
        /// Gets the currently selected game objects
        /// </summary>
        /// <returns>A list of selected game object information</returns>
        private List<GameObjectInfo> GetSelectedGameObjects()
        {
#if UNITY_EDITOR
            // This must be called from the main thread
            return Selection.gameObjects.Select(go => new GameObjectInfo
            {
                Path = GetGameObjectPath(go),
            }).ToList();
#else
            return new List<GameObjectInfo>();
#endif
        }

        /// <summary>
        /// Gets the current scene hierarchy with optional filtering
        /// </summary>
        /// <param name="request">Request with filtering parameters</param>
        /// <returns>A result containing scenes with their game objects and optional message</returns>
        public async Task<HierarchyListResult> QuerySceneAsync(QuerySceneRequest request = null)
        {
            try
            {
                return await _editorThreadHelper.ExecuteOnEditorThreadAsync(() => QuerySceneState(request));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting scene hierarchy state.");
                return new HierarchyListResult();
            }
        }

        /// <summary>
        /// Gets the current scene hierarchy with optional filtering
        /// </summary>
        /// <param name="request">Request with filtering parameters</param>
        /// <returns>A result containing scenes with their game objects and optional message</returns>
        private HierarchyListResult QuerySceneState(QuerySceneRequest request)
        {
            // TODO: We should consider extending this function to be able to do this for any prefab/scene in the project and not only the active ones.
#if UNITY_EDITOR
            var result = new HierarchyListResult();
            var scenes = new List<SceneInfo>();
            bool limitReached = false;
            
            // Check if a prefab is open in any mode
            var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
            if (prefabStage != null)
            {
                // Get the root GameObject of the prefab
                GameObject prefabRoot = prefabStage.prefabContentsRoot;
                
                if (prefabRoot != null)
                {
                    _logger.LogInformation($"Using prefab hierarchy: {prefabRoot.name} at path {prefabStage.assetPath}");
                    
                    // Get the hierarchy starting from the prefab root
                    var hierarchyResult = GetFilteredHierarchy(prefabRoot, request);
                    limitReached = hierarchyResult.LimitReached;
                    
                    // Create a pseudo-scene for the prefab
                    var prefabScene = new SceneInfo
                    {
                        Name = System.IO.Path.GetFileNameWithoutExtension(prefabStage.assetPath),
                        Path = prefabStage.assetPath,
                        IsActive = true,
                        GameObjects = hierarchyResult.GameObjects
                    };
                    scenes.Add(prefabScene);
                    
                    // Add a message indicating this is a prefab hierarchy
                    result.Message = "This is the hierarchy for a prefab. ENSURE that you perform actions relative to a prefab as needed";
                    result.Message += $"\nPrefab path: {prefabStage.assetPath}";
                }
            }
            // If not in a prefab, proceed with normal scene hierarchy
            else if (!string.IsNullOrEmpty(request?.ReferenceObjectPath))
            {
                // Find the reference object by path (now supports scene prefixes)
                GameObject referenceObject = FindGameObjectByPath(request.ReferenceObjectPath);
                
                if (referenceObject != null)
                {
                    // If we found the reference object, start the search from there
                    _logger.LogInformation($"Using reference object: {referenceObject.name} at path {request.ReferenceObjectPath}");
                    
                    // Get the hierarchy starting from the reference object
                    var hierarchyResult = GetFilteredHierarchy(referenceObject, request);
                    limitReached = hierarchyResult.LimitReached;
                    
                    // Create a scene info for the scene containing the reference object
                    var scene = referenceObject.scene;
                    var sceneInfo = new SceneInfo
                    {
                        Name = scene.name,
                        Path = scene.path,
                        IsActive = scene == SceneManager.GetActiveScene(),
                        GameObjects = hierarchyResult.GameObjects
                    };
                    scenes.Add(sceneInfo);
                }
                else
                {
                    _logger.LogWarning($"Reference object not found at path: {request.ReferenceObjectPath}");
                }
            }
            else
            {
                // Get all scenes including regular scenes and DontDestroyOnLoad
                var allScenes = GetAllLoadedScenes();
                var activeScene = SceneManager.GetActiveScene();
                
                foreach (var scene in allScenes)
                {
                    var sceneGameObjects = new List<GameObjectInfo>();
                    var rootObjects = scene.GetRootGameObjects();
                    
                    // Process each root object in the scene
                    foreach (var rootObject in rootObjects)
                    {
                        var hierarchyResult = GetFilteredHierarchy(rootObject, request);
                        sceneGameObjects.AddRange(hierarchyResult.GameObjects);
                        
                        if (hierarchyResult.LimitReached)
                        {
                            limitReached = true;
                            break;
                        }
                    }
                    
                    // Create scene info
                    var sceneInfo = new SceneInfo
                    {
                        Name = string.IsNullOrEmpty(scene.name) ? "DontDestroyOnLoad" : scene.name,
                        Path = scene.path ?? "", // DontDestroyOnLoad doesn't have a file path
                        IsActive = scene == activeScene,
                        GameObjects = sceneGameObjects
                    };
                    
                    scenes.Add(sceneInfo);
                    
                    if (limitReached)
                        break;
                }
            }
            
            // Apply pagination if needed
            if (request?.GetSkip() > 0 || request?.GetLimit() < int.MaxValue)
            {
                int skip = request?.GetSkip() ?? 0;
                int limit = request?.GetLimit() ?? 200;
                int currentCount = 0;
                
                foreach (var scene in scenes)
                {
                    if (currentCount >= skip + limit)
                    {
                        scene.GameObjects.Clear();
                        continue;
                    }
                    
                    if (currentCount < skip)
                    {
                        int toSkip = Math.Min(skip - currentCount, scene.GameObjects.Count);
                        scene.GameObjects.RemoveRange(0, toSkip);
                        currentCount += toSkip;
                    }
                    
                    if (currentCount + scene.GameObjects.Count > skip + limit)
                    {
                        int toTake = skip + limit - currentCount;
                        scene.GameObjects = scene.GameObjects.Take(toTake).ToList();
                    }
                    
                    currentCount += scene.GameObjects.Count;
                }
            }
            
            result.Scenes = scenes;
            
            // Add message if limit was reached
            if (limitReached)
            {
                // Append the truncation message to any existing message
                if (!string.IsNullOrEmpty(result.Message))
                {
                    result.Message += $" (Result truncated to first {request?.GetLimit() ?? 200} items.)";
                }
                else
                {
                    result.Message = $"(Result truncated to first {request?.GetLimit() ?? 200} items.)";
                }
            }
            
            return result;
#else
            return new HierarchyListResult();
#endif
        }
        
        /// <summary>
        /// Finds a GameObject by its path in the hierarchy
        /// </summary>
        /// <param name="path">The path of the GameObject (e.g. "/Root/Parent/Child" or "/scene0/Root/Parent/Child")</param>
        /// <returns>The GameObject if found, null otherwise</returns>
        public GameObject FindGameObjectByPath(string path)
        {
#if UNITY_EDITOR
            try
            {
                // Normalize the path
                if (!path.StartsWith("/"))
                {
                    path = "/" + path;
                }
                
                // Split the path into parts
                var parts = path.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 0)
                {
                    return null;
                }
                
                // Check if path starts with sceneX
                UnityScene targetScene;
                int startIndex = 0;
                
                if (parts[0].StartsWith("scene") && int.TryParse(parts[0].Substring(5), out int sceneIndex))
                {
                    // Use specified scene
                    if (sceneIndex < 0 || sceneIndex >= SceneManager.sceneCount)
                    {
                        _logger.LogWarning($"Scene index out of range: {sceneIndex}");
                        return null;
                    }
                    
                    targetScene = SceneManager.GetSceneAt(sceneIndex);
                    if (!targetScene.isLoaded)
                    {
                        _logger.LogWarning($"Scene at index {sceneIndex} is not loaded");
                        return null;
                    }
                    
                    startIndex = 1; // Skip the scene part
                }
                else
                {
                    // Use active scene
                    targetScene = SceneManager.GetActiveScene();
                }
                
                if (startIndex >= parts.Length)
                {
                    _logger.LogWarning("No GameObject path specified after scene");
                    return null;
                }
                
                // Find the root object in the target scene
                var rootObjects = targetScene.GetRootGameObjects();
                var current = rootObjects.FirstOrDefault(go => go.name == parts[startIndex]);
                
                if (current == null)
                {
                    _logger.LogWarning($"Root object not found: {parts[startIndex]} in scene {targetScene.name}");
                    return null;
                }
                
                // Navigate through the hierarchy
                for (int i = startIndex + 1; i < parts.Length; i++)
                {
                    var childName = parts[i];
                    Transform child = null;
                    
                    // Find the child by name
                    for (int j = 0; j < current.transform.childCount; j++)
                    {
                        var childTransform = current.transform.GetChild(j);
                        if (childTransform.name == childName)
                        {
                            child = childTransform;
                            break;
                        }
                    }
                    
                    if (child == null)
                    {
                        _logger.LogWarning($"Child object not found: {childName} in {current.name}");
                        return null;
                    }
                    
                    current = child.gameObject;
                }
                
                return current;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding GameObject by path: {ex.Message}");
                return null;
            }
#else
            return null;
#endif
        }

        /// <summary>
        /// Gets a filtered hierarchy starting from the given game object using breadth-first search
        /// </summary>
        /// <param name="gameObject">The root game object</param>
        /// <param name="request">Request with filtering parameters</param>
        /// <returns>A result containing game objects and whether the limit was reached</returns>
        private (List<GameObjectInfo> GameObjects, bool LimitReached) GetFilteredHierarchy(GameObject gameObject, QuerySceneRequest request)
        {
            var result = new List<GameObjectInfo>();
            bool limitReached = false;
            
            // Use breadth-first search to process the hierarchy
            var queue = new Queue<(GameObject, int)>(); // GameObject and its depth
            queue.Enqueue((gameObject, 0));
            
            // Process objects level by level until we reach the limit or exhaust the queue
            while (queue.Count > 0)
            {
                var (currentObject, currentDepth) = queue.Dequeue();
                
                // Process the current game object
                ProcessGameObject(currentObject, request, result);
                
                // Check if we've reached the limit
                if (result.Count >= request.GetLimit())
                {
                    limitReached = true;
                    break;
                }
                
                // Enqueue all children for the next level
                for (int i = 0; i < currentObject.transform.childCount; i++)
                {
                    Transform childTransform = currentObject.transform.GetChild(i);
                    queue.Enqueue((childTransform.gameObject, currentDepth + 1));
                }
            }
            
            return (result, limitReached);
        }
        
        /// <summary>
        /// Processes a single game object, adding it to the result list if it matches the filters
        /// </summary>
        /// <param name="gameObject">The game object to process</param>
        /// <param name="request">Request with filtering parameters</param>
        /// <param name="result">List to add matching game objects to</param>
        private void ProcessGameObject(GameObject gameObject, QuerySceneRequest request, List<GameObjectInfo> result)
        {
            // Check active state
            if (!request.GetIncludeInactive() && !gameObject.activeInHierarchy)
            {
                return;
            }
            
            // Create the game object info
            var info = new GameObjectInfo
            {
                Path = GetGameObjectPath(gameObject)
            };
            
            // Only include components and children if onlyPaths is false
            if (!request.GetOnlyPaths())
            {
                info.Components = gameObject.GetComponents<Component>()
                    .Where(c => c != null)
                    .Select(c => c.GetType().Name)
                    .ToList();
                info.Children = new List<GameObjectInfo>();
            }
            else
            {
                // Set to null to exclude from serialization
                info.Components = null;
                info.Children = null;
            }
            
            // Check if this object matches the filters
            bool matchesFilters = true;
            
            // Check name filter
            if (!string.IsNullOrEmpty(request.NameFilter) && 
                !gameObject.name.Contains(request.NameFilter, StringComparison.OrdinalIgnoreCase))
            {
                matchesFilters = false;
            }
            
            // Check tag filter
            if (matchesFilters && !string.IsNullOrEmpty(request.TagFilter) && 
                !gameObject.CompareTag(request.TagFilter))
            {
                matchesFilters = false;
            }
            
            // Check component filter
            if (matchesFilters && !string.IsNullOrEmpty(request.ComponentFilter))
            {
                var components = gameObject.GetComponents<Component>();
                bool hasMatchingComponent = components.Any(c => 
                    c != null && c.GetType().Name.Contains(request.ComponentFilter, StringComparison.OrdinalIgnoreCase));
                
                if (!hasMatchingComponent)
                {
                    matchesFilters = false;
                }
            }
            
            // If this object matches the filters, add it to the result
            if (matchesFilters)
            {
                result.Add(info);
            }
        }

        /// <summary>
        /// Gets the path of a game object in the hierarchy
        /// </summary>
        /// <param name="gameObject">The game object</param>
        /// <returns>The path of the game object</returns>
        private string GetGameObjectPath(GameObject gameObject)
        {
            string path = "/" + gameObject.name;
            Transform parent = gameObject.transform.parent;

            while (parent != null)
            {
                path = "/" + parent.name + path;
                parent = parent.parent;
            }

            return path;
        }

        /// <summary>
        /// Gets the DontDestroyOnLoad scene using a temporary GameObject trick
        /// </summary>
        /// <returns>The DontDestroyOnLoad scene, or null if unable to get it</returns>
        private UnityScene? GetDontDestroyOnLoadScene()
        {
            if (!_editorStateService.IsPlaying)
                return null;            

            // This gem was generously gifted by this post: https://discussions.unity.com/t/editor-script-how-to-access-objects-under-dontdestroyonload-while-in-play-mode/646469/8
            GameObject temp = null;
            try
            {
                temp = new GameObject();
                UnityEngine.Object.DontDestroyOnLoad(temp);
                var dontDestroyOnLoad = temp.scene;
                UnityEngine.Object.DestroyImmediate(temp);
                temp = null;

                return dontDestroyOnLoad;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting DontDestroyOnLoad scene");
                return null;
            }
            finally
            {
                if (temp != null)
                    UnityEngine.Object.DestroyImmediate(temp);
            }
        }

        /// <summary>
        /// Gets all scenes including regular loaded scenes and DontDestroyOnLoad scene
        /// </summary>
        /// <returns>List of all available scenes</returns>
        private List<UnityScene> GetAllLoadedScenes()
        {
            var allScenes = new List<UnityScene>();
            
            // Add all regular loaded scenes
            int sceneCount = SceneManager.sceneCount;
            for (int i = 0; i < sceneCount; i++)
            {
                var scene = SceneManager.GetSceneAt(i);
                if (scene.isLoaded)
                {
                    allScenes.Add(scene);
                }
            }
            
            // Add DontDestroyOnLoad scene if it exists and has objects
            var dontDestroyScene = GetDontDestroyOnLoadScene();
            if (dontDestroyScene.HasValue && dontDestroyScene.Value.rootCount > 0)
            {
                allScenes.Add(dontDestroyScene.Value);
            }
            
            return allScenes;
        }
    }
}
