import { NextRequest, NextResponse } from "next/server";

// Helper function to create error redirects
export function createErrorRedirect(request: NextRequest, message: string): NextResponse {
  const errorUrl = new URL("/auth/error", request.nextUrl.origin);
  errorUrl.searchParams.set("message", message);
  return NextResponse.redirect(errorUrl);
}

// Helper function to create success redirects
export function createSuccessRedirect(
  request: NextRequest,
  path: string,
  params?: Record<string, string>
) {
  const successUrl = new URL(path, request.nextUrl.origin);
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      successUrl.searchParams.set(key, value);
    });
  }
  return NextResponse.redirect(successUrl);
}
