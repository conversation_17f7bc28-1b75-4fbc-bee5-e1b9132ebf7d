-- Add thread_id column to thread_reviews table to track which thread was reviewed
ALTER TABLE thread_reviews 
ADD COLUMN IF NOT EXISTS thread_id TEXT;

-- Add foreign key constraint to reference the threads table
ALTER TABLE thread_reviews 
DROP CONSTRAINT IF EXISTS fk_reviewed_thread;

ALTER TABLE thread_reviews 
ADD CONSTRAINT fk_reviewed_thread 
FOREIGN KEY (thread_id) REFERENCES threads(id) ON DELETE CASCADE;

-- Create index for efficient queries by thread_id
CREATE INDEX IF NOT EXISTS idx_thread_reviews_thread_id ON thread_reviews(thread_id);

-- Add comment to explain the column
COMMENT ON COLUMN thread_reviews.thread_id IS 'ID of the thread that was reviewed to generate this suggestion';
