<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="style.uss" />
    <ui:VisualElement name="context_item" class="image-context-item-square" style="flex-grow: 0; position: relative; background-color: rgba(48, 47, 47, 0.84); border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; width: 40px; height: 40px; margin-right: 5px; margin-top: 3px;">
        <ui:VisualElement name="thumbnail" class="image-thumbnail-square" style="width: 100%; height: 100%; border-radius: 3px; background-color: rgba(100, 100, 100, 0.5);" />
        <ui:Button text="✕" name="remove" class="image-remove-button" style="position: absolute; top: -5px; right: -5px; width: 16px; height: 16px; background-color: rgba(255, 255, 255, 0.9); border-top-width: 1px; border-right-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-color: rgba(100, 100, 100, 0.5); border-radius: 8px; padding: 0; margin: 0; font-size: 8px; color: rgb(100, 100, 100);" />
    </ui:VisualElement>
</ui:UXML>
