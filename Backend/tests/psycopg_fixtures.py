import pytest
from contextlib import asynccontextmanager
from collections import deque


@pytest.fixture(autouse=True)
def mock_psycopg_connections(monkeypatch: pytest.MonkeyPatch):
    """Mock psycopg async connection pool to avoid real DB access in tests.

    Replaces `connection_pool.connection()` with an async context manager that yields
    a fake connection whose `execute()` returns an object with async `fetchone`/`fetchall`.
    Also stubs `open()`/`close()` to no-ops for app lifespan hooks.
    """

    class _ScriptedResult:
        def __init__(self, one=None, all=None):
            self._one = one
            # If caller provided only `one`, default `all` to a single-item list
            if all is None and one is not None:
                self._all = [one]
            else:
                self._all = all if all is not None else []

        async def fetchone(self):
            return self._one

        async def fetchall(self):
            return self._all

    class _PsycopgController:
        def __init__(self):
            self._queue = deque()

        def push(self, *, one=None, all=None):
            """Queue the next `execute()` result.

            Example: controller.push(one={"id": 1}) then controller.push(all=[{...}, {...}])
            """
            self._queue.append(_ScriptedResult(one=one, all=all))

        def clear(self):
            self._queue.clear()

        def _next(self):
            if self._queue:
                return self._queue.popleft()
            # Default no-op result
            return _ScriptedResult()

    controller = _PsycopgController()

    class _FakeConnection:
        async def execute(self, *_args, **_kwargs):
            return controller._next()

    class _FakePool:
        async def open(self):
            return None

        async def close(self):
            return None

        @asynccontextmanager
        async def connection(self):
            yield _FakeConnection()

    fake_pool = _FakePool()

    # Primary source of truth
    monkeypatch.setattr("coplay.supabase_db.connection_pool", fake_pool, raising=True)

    # Best-effort: some modules import the symbol directly; patch if present
    optional_targets = [
        "main.connection_pool",
        "coplay.routers.auth.connection_pool",
        "coplay.assistants.chat.threads.connection_pool",
        "coplay.thread_review_service.connection_pool",
    ]
    for target in optional_targets:
        try:
            monkeypatch.setattr(target, fake_pool, raising=False)
        except Exception:
            # Module may not be imported; safe to skip
            pass

    # Expose the controller on the fixture function for sibling fixtures
    mock_psycopg_connections.controller = controller  # type: ignore[attr-defined]

    yield


@pytest.fixture
def psycopg_results(mock_psycopg_connections):
    """Provide access to the psycopg controller to script `execute()` results.

    Usage:
        psycopg_results.clear()
        psycopg_results.push(one={"subscription_credits": 10.0, "topup_credits": 2.0})
        psycopg_results.push(one=None)  # for the second execute
    """
    controller = getattr(mock_psycopg_connections, "controller")
    controller.clear()
    return controller
