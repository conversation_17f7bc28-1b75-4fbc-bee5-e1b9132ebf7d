#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reproduce the message ID consistency race condition.

The race condition occurs when:
1. Multiple chunks are generated rapidly by the AI model
2. The background task updates BACKGROUND_TASK_RESULTS[task_id].chunk rapidly
3. The test polls the endpoint at high frequency (100ms intervals)
4. Due to timing, the test can see different message IDs from different chunks

This is more likely to happen with:
- Complex prompts that generate longer responses
- Models that stream quickly (like O3 with reasoning)
- High network latency or processing delays
- CI environments with variable timing
"""

import asyncio
import sys
from pathlib import Path

# Add the Backend directory to the Python path so we can import coplay modules
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

import httpx
import time
from typing import List
import random

from coplay.models import AIModel, AssistantMode, PromptResponse


async def reproduce_race_condition():
    """
    Attempt to reproduce the message ID race condition by:
    1. Using a complex prompt that generates a long response
    2. Polling very frequently to catch timing issues
    3. Running multiple concurrent requests
    4. Adding artificial delays to simulate CI timing
    """
    
    # Use the auth client from your test setup
    async with httpx.AsyncClient(base_url="http://localhost:8080") as client:
        # You'll need to add proper auth headers here
        headers = {
            "Authorization": "Bearer YOUR_TEST_TOKEN",
            "Device-ID": "test-device-123"
        }
        
        # Complex prompt that should generate a long response with multiple chunks
        complex_prompt = """
        Please write a detailed technical explanation of how distributed systems handle consensus algorithms, 
        covering Raft, PBFT, and blockchain consensus mechanisms. Include code examples, diagrams in text form, 
        and explain the trade-offs between consistency, availability, and partition tolerance. Make sure to 
        discuss Byzantine fault tolerance, leader election, log replication, and how these systems handle 
        network partitions. This should be a comprehensive response that will generate many streaming chunks.
        """
        
        model = AIModel.O3
        
        print(f"Testing race condition reproduction for {model.value}")
        
        # Create thread
        create_response = await client.post(
            "/assistant",
            json={"assistant_mode": AssistantMode.NORMAL.value, "name": f"race_test_{model.value}"},
            headers=headers
        )
        
        if create_response.status_code != 200:
            print(f"Failed to create thread: {create_response.status_code} - {create_response.text}")
            return
            
        thread_id = create_response.json()["id"]
        print(f"Created thread: {thread_id}")
        
        # Start streaming
        stream_response = await client.post(
            "/assistant/stream",
            json={
                "thread_id": thread_id,
                "prompt": complex_prompt,
                "model": model.value,
                "assistant_mode": AssistantMode.NORMAL.value,
                "context": {}
            },
            headers=headers
        )
        
        if stream_response.status_code != 202:
            print(f"Failed to start stream: {stream_response.status_code} - {stream_response.text}")
            return
            
        chunk = PromptResponse(**stream_response.json())
        task_id = chunk.task_id
        print(f"Started streaming with task_id: {task_id}")
        
        # Poll very aggressively to catch race conditions
        chunks = []
        message_ids_seen = set()
        chunk_count = 0
        
        while chunk.finished is False:
            # Add random small delays to simulate CI timing variations
            await asyncio.sleep(random.uniform(0.01, 0.05))
            
            poll_response = await client.get(f"/assistant/stream/{task_id}", headers=headers)
            
            if poll_response.status_code != 200:
                print(f"Polling failed: {poll_response.status_code} - {poll_response.text}")
                break
                
            chunk = PromptResponse(**poll_response.json())
            chunks.append(chunk)
            chunk_count += 1
            
            if chunk.message_id:
                message_ids_seen.add(chunk.message_id)
                print(f"Chunk {chunk_count}: message_id={chunk.message_id}, type={chunk.type}, finished={chunk.finished}")
                
                # If we see multiple message IDs, we've reproduced the race condition!
                if len(message_ids_seen) > 1:
                    print(f"🎯 RACE CONDITION REPRODUCED!")
                    print(f"Multiple message IDs found: {message_ids_seen}")
                    print(f"Total chunks: {chunk_count}")
                    return True
        
        print(f"Test completed. Message IDs seen: {message_ids_seen}")
        print(f"Total chunks: {chunk_count}")
        
        if len(message_ids_seen) > 1:
            print(f"🎯 RACE CONDITION REPRODUCED!")
            return True
        else:
            print("❌ Race condition not reproduced in this run")
            return False


async def run_multiple_attempts():
    """Run multiple attempts to increase chances of reproducing the race condition"""
    print("Attempting to reproduce message ID race condition...")
    print("This may take several attempts as it depends on timing.")
    
    for attempt in range(10):
        print(f"\n--- Attempt {attempt + 1}/10 ---")
        try:
            if await reproduce_race_condition():
                print(f"\n✅ Successfully reproduced race condition on attempt {attempt + 1}")
                return
        except Exception as e:
            print(f"Error in attempt {attempt + 1}: {e}")
        
        # Wait between attempts
        await asyncio.sleep(1)
    
    print("\n❌ Could not reproduce race condition in 10 attempts")
    print("This suggests the race condition may be environment-specific (CI timing, network latency, etc.)")


if __name__ == "__main__":
    asyncio.run(run_multiple_attempts())
