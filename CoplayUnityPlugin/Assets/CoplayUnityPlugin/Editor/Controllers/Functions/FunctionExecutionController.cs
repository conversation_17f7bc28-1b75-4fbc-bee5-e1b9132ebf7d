using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using Coplay.Common;
using Coplay.Controllers.Systems;
using Coplay.Models;
using Coplay.Models.Configuration;
using Coplay.Services;
using Coplay.Services.Chat;
using Coplay.Controllers.Functions.Models;
using Coplay.Models.Assistants;
using System.Reactive.Subjects;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Disposables;
using Microsoft.Extensions.Logging;
using Coplay.Models.Context;
using System.IO;

namespace Coplay.Controllers.Functions
{
    public class FunctionExecutionController : IDisposable
    {
        ILogger<FunctionExecutionController> _logger;
        private readonly StateController _stateManager;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly IFunctionInvokerController _functionInvokerController;
        private readonly ChatHistoryRepository _chatHistory;
        private readonly IChatThreads _chatThreads;
        private readonly FunctionQueueController _functionQueueController;
        private readonly Subject<Unit> _functionExecutionChangeSubject = new Subject<Unit>();
        private readonly CompositeDisposable _disposables = new CompositeDisposable();
        private readonly SceneAutoSaveService _sceneAutoSaveService;
        private readonly ICompilationService _compilationService;
        private readonly ICoplayWindowState _coplayWindowState;
        private readonly FullContext _fullContext;

        public IEnumerable<FunctionCall> FunctionQueue => _functionQueueController;
        public IObservable<Unit> FunctionExecutionChange => _functionExecutionChangeSubject.Merge(_functionQueueController.FunctionQueueChanged);
        public bool IsFunctionExecuting => _functionQueueController.IsFunctionExecuting();
        private static readonly List<string> FunctionsToShowResultsFor = new List<string> {
            "get_unity_logs",
            "get_unity_editor_state",
            "list_game_objects_in_hierarchy",
            "execute_script", // Might not need to print this.
            "get_game_object_info",
            "list_files",
            "read_file",
            "search_files",
            "execute_command",
            "generate_script",
            "generate_package",
            "write_to_file",
            "replace_in_file",
            "list_code_definition_names",
            "execute_snippet"
        };
        private static readonly List<string> DontCheckCompileErrorsFunctions = new List<string>
        {
            "write_to_file",
            "replace_in_file",
            "execute_command",
            "create_material",
            "assign_material",
            "list_files",
            "read_file",
            "search_files",
            "list_code_definition_names",
            "list_game_objects_in_hierarchy",
            "get_game_object_info",
            "get_unity_editor_state",
        };

        public FunctionExecutionController(
            ILogger<FunctionExecutionController> logger,
            ChatHistoryRepository chatHistory,
            IChatThreads chatThreads,
            StateController stateManager,
            AssistantConfiguration assistantConfiguration,
            IFunctionInvokerController functionInvokerController,
            FunctionQueueController functionQueueController,
            CoplayStateModel stateModel,
            SceneAutoSaveService sceneAutoSaveService,
            ICompilationService compilationService,
            ICoplayWindowState coplayWindowState,
            FullContext fullContext)
        {
            _logger = logger;
            _chatHistory = chatHistory;
            _chatThreads = chatThreads;
            _stateManager = stateManager;
            _assistantConfiguration = assistantConfiguration;
            _functionInvokerController = functionInvokerController;
            _functionQueueController = functionQueueController;
            _compilationService = compilationService;
            _coplayWindowState = coplayWindowState;
            _fullContext = fullContext;

            var threadIsLoaded = _chatThreads.ThreadLoadedObservable
                .Do(_ => HandleThreadLoaded());

            stateModel.CurrentStateObservable
                .CombineLatest(threadIsLoaded, (state, _) => state)
                .DistinctUntilChanged()
                .Select(state => Observable.FromAsync(() => HandleStateChanged(state)))
                .Switch()
                .Retry()
                .Subscribe(_ => { }, ex => _logger.LogError(ex, "Error handling state change"))
                .AddTo(_disposables);

            _sceneAutoSaveService = sceneAutoSaveService;
        }

        public void Dispose()
        {
            _disposables.Dispose();
            _logger.LogInformation("FunctionExecutionController: Dispose called");
        }

        private void HandleThreadLoaded()
        {
            _functionQueueController.ClearFunctionQueue();
            // From the end of the chat history, take all the messages that are not from the user and extract their function calls.
            var lastMessage = _chatHistory.Messages.LastOrDefault();
            var toolCallMessages = lastMessage?.FunctionCalls;
            if (toolCallMessages != null)
            {
                // Add the function calls to the queue.
                foreach (var functionCall in toolCallMessages)
                {
                    functionCall.IsExecuting = false;
                    AddFunctionToQueue(functionCall);
                }
                _logger.LogInformation($"FunctionExecutionController: Loaded thread with {toolCallMessages.Count} function calls from chat history.");
            }

            if (lastMessage == null)
            {
                _stateManager.ClearState();
                return;
            }

            if (lastMessage.ChatMessageRole != ChatMessageRole.You)
            {
                _stateManager.ClearState();
                if (HasUnExecutedFunctions())
                {
                    _stateManager.SetState(CoplayState.WaitingForAI);
                    _stateManager.SetState(CoplayState.WaitingForUserToExecuteFunction);
                }
            }
        }

        private async Task HandleStateChanged(CoplayState newState)
        {
            if (!_coplayWindowState.HasOpenInstances)
            {
                return;
            }
            switch (newState)
            {
                case CoplayState.ExecutingAllFunctions:
                    await ExecuteAllFunctions();
                    break;
                case CoplayState.WaitingForUserToExecuteFunction:
                    await WaitForUserToInitiateExecuteFunction();
                    break;
            }
        }

        /// <summary>
        /// After executing all functions, we return to the state of waiting for the user to initiate the execution of a function.
        /// Then we need to check if there are any functions left to execute, otherwise, go back one more step in the state history.
        /// </summary>
        private async Task WaitForUserToInitiateExecuteFunction()
        {
            var lastExecutedFunction = _functionQueueController.LastOrDefault(f => f.HasExecuted);
            var wasLastFunctionCreateTask = lastExecutedFunction?.FunctionName == "create_task";

            if (_functionQueueController.AreAllFunctionsExecutedOrCancelled() && _stateManager.CurrentState == CoplayState.WaitingForUserToExecuteFunction && !wasLastFunctionCreateTask)
            {
                _stateManager.ReturnToPreviousState("User has executed all functions");
            }
            else if (_assistantConfiguration.AutoApproveFunctions && !_functionQueueController.NextFunctionCallRequiresApproval())
            {
                _stateManager.SetState(CoplayState.ExecutingAllFunctions);
            }
            else
            {
                var pendingFunctionToExecute = _functionQueueController.FirstOrDefault(f => f.PendingExecution && !f.HasExecuted);
                if (pendingFunctionToExecute != null)
                {
                    await ExecuteFunction(pendingFunctionToExecute);
                }
                await ExecuteReadOnlyFunctionsUntilApprovalRequired();
            }
        }

        public void AddFunctionToQueue(FunctionCall functionCall)
        {
            _functionQueueController.EnqueueFunctionCall(functionCall);
        }

        public bool IsFunctionInQueue(FunctionCall functionCall)
        {
            return _functionQueueController.Contains(functionCall);
        }

        public static FunctionCall MakeFunctionCallFromToolCall(ToolCall toolCall, string messageId)
        {
            FunctionCall functionCall = new()
            {
                MessageId = messageId,
                ToolCallId = toolCall.id,
                FunctionName = toolCall.name,
                Arguments = toolCall.args,
                Description = toolCall.description,
                HasExecuted = toolCall.output != null,
                State = toolCall.output != null ? FunctionState.Executed : FunctionState.Queued,
                RequiresUserApproval = false,
                Result = toolCall.output,
                OutputProcessed = toolCall.output != null,
            };

            var requiresUserApproval = functionCall.FunctionName == "execute_command"
                                       && functionCall.Arguments.TryGetValue("requires_approval", out var requiresApproval)
                                       && bool.TryParse(requiresApproval, out var requiresApprovalBool)
                                       && requiresApprovalBool;

            // Command safety analysis for execute_command functions (skip if already executed)
            if (functionCall.FunctionName == "execute_command" && !functionCall.HasExecuted && functionCall.Arguments.TryGetValue("command", out var command))
            {
                var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);
                
                if (analysis.Risk == CommandSafetyAnalyzer.CommandRisk.Fatal)
                {
                    // Auto-reject fatal commands
                    functionCall.IsCancelled = true;
                    functionCall.State = FunctionState.Cancelled;
                    functionCall.Result = $"Command blocked for safety: {string.Join(", ", analysis.Reasons)}";
                    functionCall.OutputProcessed = true;
                    CoplayLogger.LogWarning($"Blocked dangerous command: {command} - {string.Join(", ", analysis.Reasons)}");
                }
                // Safe commands proceed as normal - LLM handles approval via requires_approval flag
            }

            functionCall.RequiresUserApproval = requiresUserApproval;

            return functionCall;
        }

        public async Task ExecuteFunction(FunctionCall functionCall)
        {
            if (functionCall.IsExecuting || functionCall.HasExecuted)
            {
                return;
            }

            functionCall.PendingExecution = true;
            functionCall.IsExecuting = true;
            _functionExecutionChangeSubject.OnNext(Unit.Default);
            if (functionCall.MessageId != null)
            {
                _chatHistory.UpdateMessage(functionCall.MessageId, string.Empty, new List<FunctionCall>() { functionCall });
            }
            else
            {
                var message = _chatHistory.AddMessage(string.Empty, functionCalls: new List<FunctionCall>() { functionCall });
                functionCall.MessageId = message.Id;
            }

            string compileErrors = null;
            if (DontCheckCompileErrorsFunctions.All(f => f != functionCall.FunctionName))
            {
                compileErrors = await _compilationService.CheckCompilationErrors(default);
            }

            try
            {
                _functionQueueController.MarkFunctionAsExecuted(functionCall);
                _logger.LogInformation($"Func: {functionCall.FunctionName} Args: {string.Join(", ", functionCall.Arguments.Select(kv => $"{kv.Key}: {kv.Value}"))}");

                var argumentsWithContext = new Dictionary<string, string>(functionCall.Arguments)
                {
                    { "__tool_call_id", functionCall.ToolCallId }
                };

                var functionResult = compileErrors ?? await _functionInvokerController.InvokeUnityFunction(functionCall.FunctionName, argumentsWithContext);
                var resultMessage = functionResult is string strResult ? strResult : JsonConvert.SerializeObject(functionResult);

                // Check if the result is a GetSceneViewScreenshotResponse and extract the image path
                if (functionResult is GetImageResponse screenshotResponse)
                {
                    resultMessage = screenshotResponse.Message;
                }

                // Check if the result is an ImageGenerationResponse and extract the image path
                if (functionResult is ImageGenerationFunctionResponse imageResponse)
                {
                    resultMessage = imageResponse.Message;
                }

                _logger.LogInformation($"Function result: {resultMessage}");
                functionCall.Result = resultMessage;
                functionCall.ResultType = functionResult is not string ? functionResult?.GetType().Name : null;
                functionCall.State = FunctionState.Executed;
                _functionExecutionChangeSubject.OnNext(Unit.Default);

                _chatHistory.UpdateMessage(functionCall.MessageId); // This is to trigger a UI refresh as the function result is updated.

                // Increment the action count for auto-save functionality (only for successfully executed functions)
                if (!functionCall.Result.StartsWith("Error: ") && _functionInvokerController.DoesInvokeSceneEditingFunctions(functionCall.FunctionName))
                {
                    _sceneAutoSaveService.IncrementActionCount();
                }

                if (_compilationService.IsCompilationSkipped(resultMessage))
                {
                    // Do not print this message to the user and don't set the waitingForRecompile flag.
                }
                else if (FunctionsToShowResultsFor.Contains(functionCall.FunctionName))
                {
                    _chatHistory.AddActionResultToMessage(functionCall.MessageId, resultMessage); // NOTE: for multiple function calls, this will only add the result to the last function call.
                }

                if (functionCall.FunctionName == "create_task")
                {
                    return; // Don't proceed to the next steps where we return to the previous state. We handle state transition in the function call itself.
                }

                // This is only needed when we execute one function at a time and this is the last function in the queue.
                // If we execute multiple functions at once, this will be handled in ExecuteAllFunctions.
                if (_functionQueueController.AreAllFunctionsExecutedOrCancelled() && _stateManager.CurrentState == CoplayState.WaitingForUserToExecuteFunction)
                {
                    _stateManager.ReturnToPreviousState("Last function executed");
                }
                else if (functionCall.RequiresUserApproval && _functionQueueController.AreAllFunctionsExecutedOrCancelled())
                {
                    // If we end up here, we were executing all, but needed to stop and wait for user approval. Thus we return to the previous state, which will trigger whatever is needed next.
                    _stateManager.ReturnToPreviousState("Executed a function approved by user");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error executing function {functionCall.FunctionName}");
                functionCall.IsExecuting = false;
                ClearFunctionQueue();
                _stateManager.ReturnToPreviousState("Error executing function");
                _chatHistory.UpdateMessage(functionCall.MessageId, $"Error executing function {functionCall.FunctionName}: {ex.Message}. Please try re-phrasing your prompt.");
            }
            finally
            {
                functionCall.IsExecuting = false;
                _chatHistory.UpdateMessage(functionCall.MessageId);
                _functionExecutionChangeSubject.OnNext(Unit.Default);
            }
        }

        public async Task ExecuteReadOnlyFunctionsUntilApprovalRequired()
        {
            var functionQueue = _functionQueueController.ToList();
            foreach (var functionCall in functionQueue)
            {
                if (functionCall.HasExecuted || functionCall.IsCancelled)
                {
                    continue;
                }
                    
                if (functionCall.RequiresUserApproval || 
                    functionCall.AutoApproveLimitReached || 
                    !_functionInvokerController.IsReadOnlyFunction(functionCall.FunctionName))
                {
                    return;
                }

                await ExecuteFunction(functionCall);
                _functionExecutionChangeSubject.OnNext(Unit.Default);
            }
        }

        public async Task ExecuteAllFunctions()
        {
            for (var attempt = 0; attempt <= 1; attempt++)
            {
                var functionQueue = _functionQueueController.ToList();
                for (var i = 0; i < functionQueue.Count; i++)
                {
                    var functionCall = functionQueue[i];
                    if (functionCall.HasExecuted || functionCall.IsCancelled)
                    {
                        continue;
                    }

                    if (functionCall.RequiresUserApproval || functionCall.AutoApproveLimitReached)
                    {
                        _stateManager.SetState(CoplayState.WaitingForUserToExecuteFunction);
                        return;
                    }

                    var isParallelizable =
                        functionCall.FunctionName.StartsWith("generate") ||
                        functionCall.FunctionName == "image_functions";

                    if (isParallelizable)
                    {
                        var parallelBatch = new List<FunctionCall> { functionCall };
                        for (var j = i + 1; j < functionQueue.Count; j++)
                        {
                            var nextFunctionCall = functionQueue[j];
                            if (nextFunctionCall.FunctionName == functionCall.FunctionName && !nextFunctionCall.HasExecuted)
                            {
                                parallelBatch.Add(nextFunctionCall);
                            }
                            else
                            {
                                break;
                            }
                        }

                        if (parallelBatch.Count > 1)
                        {
                            var executionTasks = parallelBatch.Select(ExecuteFunction).ToList();
                            await Task.WhenAll(executionTasks);

                            i += parallelBatch.Count - 1;

                            _functionExecutionChangeSubject.OnNext(Unit.Default);
                            continue;
                        }
                    }


                    await ExecuteFunction(functionCall); // We need to await here so that we don't change state before the function has finished executing.

                    if (functionCall.FunctionName == "create_task")
                    {
                        return;
                    }

                    _functionExecutionChangeSubject.OnNext(Unit.Default);
                }

                // We need this so that after compilation, as the state is set we correctly revert to the waiting for user to execute function state.
                if (_stateManager.CurrentState == CoplayState.ExecutingAllFunctions)
                {
                    if (_functionQueueController.AreAllFunctionsExecutedOrCancelled())
                    {
                        _stateManager.ReturnToPreviousState("All functions executed");
                    }
                    else
                    {
                        // If the queue is not empty, and we reach this point, it means a new function was added while executing all. This is mainly a hack for the 3D model generation and refinement flow, but might be useful otherwise.
                        continue;
                    }
                }

                // If we reach this point, it means we have executed all functions in the queue and can exit the loop.
                break;
            }
        }

        public void ClearFunctionQueue()
        {
            _functionQueueController.ClearFunctionQueue();
        }

        public void CancelFunction(FunctionCall functionCall, string userFeedback = null)
        {
            functionCall.IsCancelled = true;

            string cancellationMessage = $"Cancelled: '{functionCall.FunctionName}'. Cancelled by user. Ignore this action."; // NOTE: this string is checked on the backend. take care when changing.
            if (userFeedback != null)
            {
                cancellationMessage = $"User rejected the function: <b>{functionCall.FunctionName}</b>. User feedback: {userFeedback}";
            }

            // Provide image when rejecting tool call
            var imageAttachment = _fullContext.AttachedFiles.LastOrDefault(ContextHelpers.IsImage);
            if (imageAttachment is not null)
            {
                var previewImagePath = FunctionCall.GetPreviewPath(functionCall.ToolCallId);
                try
                {
                    File.Copy(imageAttachment, previewImagePath);
                    functionCall.ResultType = nameof(GetImageResponse);
                }
                catch (IOException)
                {
                    // Ignore
                }
            }

            functionCall.Result = userFeedback == null ? $"Function <b>\"{functionCall.Description}\"</b> cancelled by user." : cancellationMessage;
            functionCall.State = FunctionState.Cancelled;

            if (functionCall.MessageId != null)
            {
                _chatHistory.UpdateMessage(functionCall.MessageId, functionCalls: new List<FunctionCall>() { functionCall });
            }
            else
            {
                _chatHistory.AddMessage(functionCall.Result, functionCalls: new List<FunctionCall>() { functionCall });
            }

            if (_functionQueueController.AreAllFunctionsExecutedOrCancelled())
            {
                _stateManager.ReturnToPreviousState("Cancelled all functions");
            }

            // TODO: implement functions auto execution in case Auto-Approve is enabled. Cannot just use ReturnToPreviousState because previous state could be waiting for AI.

            _functionExecutionChangeSubject.OnNext(Unit.Default);
        }

        public void CancelAllFunctions(string userFeedback = null)
        {
            foreach (var functionCall in _functionQueueController)
            {
                if (!functionCall.HasExecuted && !functionCall.IsCancelled)
                {
                    CancelFunction(functionCall, userFeedback);
                }
            }

            _stateManager.ClearState();
        }

        public bool HasUnExecutedFunctions()
        {
            return FunctionQueue.Any(f => !f.HasExecuted && !f.IsCancelled);
        }

        private static bool ContainsErrorMessage(string message)
        {
            if (string.IsNullOrEmpty(message))
                return false;

            string lowerMessage = message.ToLower();
            return lowerMessage.StartsWith("error") ||
                   Regex.IsMatch(lowerMessage, @"(?<![\w])error(?![\w])");
        }

        public static string FormatFunctionString(FunctionCall functionCall)
        {
            // TODO: fix this, if the get_unity_logs function contains an error log, it shows up as if the function failed even though it succeeded.
            bool hasError = !string.IsNullOrEmpty(functionCall.Result) &&
                            (ContainsErrorMessage(functionCall.Result) ||
                             functionCall.IsCancelled);
            if (hasError)
            {
                return $"<color=red>✕</color> {functionCall.Result}";
            }

            if (!functionCall.HasExecuted || functionCall.IsExecuting)
            {
                if (functionCall.FunctionName.Equals("generate_scene", StringComparison.OrdinalIgnoreCase))
                {
                    return $"<color=#808080>✓</color> {functionCall.Arguments["description"]}";
                }
                else
                {
                    return $"<color=#808080>✓</color> {functionCall.Description}";
                }
            }
            else
            {
                if (functionCall.FunctionName.Equals("generate_scene", StringComparison.OrdinalIgnoreCase))
                {
                    return $"<color=green>✓</color> {functionCall.Arguments["description"]}";
                }
                else
                {
                    return $"<color=green>✓</color> {functionCall.Description}";
                }
            }
        }
    }
}
