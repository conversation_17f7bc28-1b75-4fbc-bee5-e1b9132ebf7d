"use client";
import React, { useState, useEffect, createContext, useContext, useRef } from "react";
import { X, CheckCircle, Info, AlertTriangle, XCircle } from "lucide-react";

// Toast Context
const ToastContext = createContext<{
  success: (message: string, duration?: number) => string;
  error: (message: string, duration?: number) => string;
  warning: (message: string, duration?: number) => string;
  info: (message: string, duration?: number) => string;
  remove: (id: string) => void;
} | null>(null);

// Toast types
const TOAST_TYPES = {
  success: {
    icon: CheckCircle,
    className:
      "bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300",
  },
  error: {
    icon: XCircle,
    className:
      "bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300",
  },
  warning: {
    icon: AlertTriangle,
    className:
      "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300",
  },
  info: {
    icon: Info,
    className:
      "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300",
  },
};

interface Toast {
  id: string;
  message: string;
  type: keyof typeof TOAST_TYPES;
  duration: number;
}

// Toast Provider Component
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const addToast = (
    message: string,
    type: keyof typeof TOAST_TYPES = "info",
    duration = 5000
  ): string => {
    const id = Date.now() + Math.random().toString();
    const newToast: Toast = { id, message, type, duration };

    setToasts((prev) => [...prev, newToast]);

    return id;
  };

  const removeToast = (id: string) => {
    // Clear timeout if it exists
    const timeout = timeoutsRef.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutsRef.current.delete(id);
    }

    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  // Handle auto-removal with proper cleanup
  useEffect(() => {
    const timeouts = timeoutsRef.current;

    toasts.forEach((toast) => {
      // Skip if timeout already exists for this toast
      if (timeouts.has(toast.id)) {
        return;
      }

      const timeoutId = setTimeout(() => {
        removeToast(toast.id);
      }, toast.duration);

      timeouts.set(toast.id, timeoutId);
    });

    // Cleanup function to clear all timeouts on unmount
    return () => {
      timeouts.forEach((timeout) => {
        clearTimeout(timeout);
      });
      timeouts.clear();
    };
  }, [toasts]);

  const toast = {
    success: (message: string, duration?: number) =>
      addToast(message, "success", duration),
    error: (message: string, duration?: number) =>
      addToast(message, "error", duration),
    warning: (message: string, duration?: number) =>
      addToast(message, "warning", duration),
    info: (message: string, duration?: number) =>
      addToast(message, "info", duration),
    remove: removeToast,
  };

  return (
    <ToastContext.Provider value={toast}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
};

// Toast Container Component
const ToastContainer: React.FC<{
  toasts: Toast[];
  removeToast: (id: string) => void;
}> = ({ toasts, removeToast }) => {
  if (toasts.length === 0) return null;

  return (
    <div
      className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-sm w-full sm:max-w-md"
      aria-live="polite"
    >
      {toasts.map((toast) => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  );
};

// Individual Toast Item Component
const ToastItem: React.FC<{ toast: Toast; onClose: () => void }> = ({
  toast,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const exitTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const toastConfig = TOAST_TYPES[toast.type] || TOAST_TYPES.info;
  const Icon = toastConfig.icon;

  useEffect(() => {
    // Trigger entrance animation
    requestAnimationFrame(() => {
      setIsVisible(true);
    });
  }, []);

  useEffect(() => {
    // Cleanup timeout on unmount
    return () => {
      if (exitTimeoutRef.current) {
        clearTimeout(exitTimeoutRef.current);
      }
    };
  }, []);

  const handleClose = () => {
    // Clear any existing timeout
    if (exitTimeoutRef.current) {
      clearTimeout(exitTimeoutRef.current);
    }

    setIsExiting(true);
    exitTimeoutRef.current = setTimeout(() => {
      onClose();
    }, 200);
  };

  return (
    <div
      className={`
        relative border rounded-lg shadow-lg p-4 pr-10 transition-all duration-200 ease-in-out
        ${toastConfig.className}
        ${isVisible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
        }
        ${isExiting ? "translate-x-full opacity-0" : ""}
      `}
    >
      <div className="flex items-start gap-3">
        <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
        <p className="text-sm font-medium leading-relaxed pr-2">
          {toast.message}
        </p>
      </div>

      <button
        onClick={handleClose}
        className="absolute top-2 right-2 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
        aria-label="Close toast"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};

// Hook to use toast
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};
