using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Coplay.Common;
using Coplay.MCP;
using Coplay.Services.Scene;
using Coplay.Services.Scene.Models;
using Coplay.Services.UnityLogs;
using Newtonsoft.Json;

namespace Coplay.Controllers.Functions.Implementations
{
    [ToolFunctionProvider]
    public class MCPToolFunctions // TODO: should probably move this class and rename it to something under the Functions folder.
    {
        private readonly IUnityLogsRepository _unityLogsRepository;
        private readonly ScriptExecutor _scriptExecutor;
        private readonly IEditorStateProvider _editorStateProvider;

        public MCPToolFunctions(IUnityLogsRepository unityLogsRepository, ScriptExecutor scriptExecutor, IEditorStateProvider editorStateProvider)
        {
            _scriptExecutor = scriptExecutor;
            _editorStateProvider = editorStateProvider;
            _unityLogsRepository = unityLogsRepository;
        }

        public async Task<string> ExecuteScript(Dictionary<string, string> parameters)
        {
            string filePath = parameters.GetValueOrDefault("filePath", string.Empty);
            string methodName = parameters.GetValueOrDefault("methodName", "Execute");

            // Check if arguments are provided as a JSON string
            string argumentsJson = parameters.GetValueOrDefault("arguments", string.Empty);
            Newtonsoft.Json.Linq.JObject arguments = null;

            if (!string.IsNullOrEmpty(argumentsJson))
            {
                try
                {
                    arguments = Newtonsoft.Json.Linq.JObject.Parse(argumentsJson);
                }
                catch (Exception ex)
                {
                    CoplayLogger.LogError($"Failed to parse arguments JSON: {ex.Message}", ex);
                }
            }

            return JsonConvert.SerializeObject(await _scriptExecutor.ExecuteScriptFromFileAsync(filePath, methodName, arguments));
        }

        [ReadOnlyFunction]
        public string GetUnityLogs(Dictionary<string, string> parameters)
        {
            // Parse offset and limit, providing defaults
            int skipNewestNLogs = int.TryParse(parameters.GetValueOrDefault("skip_newest_n_logs", "0"), out var parsedSkipNewestNLogs) ? parsedSkipNewestNLogs : 0;
            int limit = int.TryParse(parameters.GetValueOrDefault("limit", "100"), out var parsedLimit) ? parsedLimit : 100;

            // Parse boolean flags, defaulting to true if missing or invalid
            bool showLogs = bool.TryParse(parameters.GetValueOrDefault("show_logs", "true"), out var parsedShowLogs) ? parsedShowLogs : true;
            bool showWarnings = bool.TryParse(parameters.GetValueOrDefault("show_warnings", "true"), out var parsedShowWarnings) ? parsedShowWarnings : true;
            bool showErrors = bool.TryParse(parameters.GetValueOrDefault("show_errors", "true"), out var parsedShowErrors) ? parsedShowErrors : true;
            
            // Get search term, defaulting to empty string if missing
            string searchTerm = parameters.GetValueOrDefault("search_term", string.Empty);

            // Call the async logger method with all parameters
            var logList = _unityLogsRepository.GetLogs(skipNewestNLogs, limit, showLogs, showWarnings, showErrors, searchTerm);

            using var sw = new StringWriter();
            foreach (var logEntry in logList)
            {
                sw.WriteLine(JsonConvert.SerializeObject(logEntry));
            }
            return sw.ToString();
        }

        [ReadOnlyFunction]
        public async Task<string> GetUnityEditorState(Dictionary<string, string> parameters)
        {
            return JsonConvert.SerializeObject(await _editorStateProvider.GetEditorStateAsync());
        }

        [ReadOnlyFunction]
        public async Task<string> ListGameObjectsInHierarchy(QuerySceneRequest request)
        {
            return JsonConvert.SerializeObject(await _editorStateProvider.QuerySceneAsync(request));
        }
    }
}
