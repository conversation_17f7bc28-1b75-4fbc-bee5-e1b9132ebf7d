from langchain_core.messages import AIMessageChunk, AIMessage
from langchain_core.messages.ai import UsageMetadata

from .models import AIModel, CoplayUsage
from .costs import calculate_cost
from .assistants.model_config import is_claude_model


def calculate_usage(chunks: list[AIMessageChunk | AIMessage], request_model: AIModel) -> CoplayUsage | None:
    """Calculate the usage for a list of AIMessageChunks"""
    usages: list[UsageMetadata] = []
    for chunk in chunks:
        if chunk.usage_metadata is not None:
            usages.append(chunk.usage_metadata)
    if len(usages) > 0:
        total_input_tokens = max(usage.get("input_tokens", 0)
                                 for usage in usages)
        output_tokens = max(usage.get("output_tokens", 0) for usage in usages) if is_claude_model(
            request_model) else sum(usage.get("output_tokens", 0) for usage in usages)
        cache_read_tokens = max(usage.get("input_token_details", {}).get(
            "cache_read", 0) for usage in usages)
        cache_write_tokens = max(usage.get("input_token_details", {}).get(
            "cache_creation", 0) for usage in usages)
        # TODO: all of these numbers are correct for <PERSON>, but they might be wrong for other providers, we should write tests/check this.
        input_tokens = total_input_tokens - cache_read_tokens - cache_write_tokens

        cost = calculate_cost(
            input_tokens,
            output_tokens,
            cache_read_tokens,
            cache_write_tokens,
            request_model,
        )

        # TODO: we should also send credit_balance (remaining credits) to bigquery to make it easier in analytics to track

        return CoplayUsage(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_input_tokens + output_tokens, # We use total_input because input_tokens can be zero if it's all cache. TODO: I think we can remove this field.
            cache_read_tokens=cache_read_tokens,
            cache_write_tokens=cache_write_tokens,
            non_cache_tokens=total_input_tokens - cache_read_tokens - cache_write_tokens, # TODO: I think we can remove this field.
            payable_tokens=calculate_payable_tokens(
                input_tokens, cache_read_tokens, output_tokens),
            cost_usd=cost,
        )
    
    return None


def calculate_payable_tokens(input_tokens: int, cache_read_tokens: int, output_tokens: int) -> int:
    """
    Payable tokens are used downstream to consume tokens from the user's balance.
    We use the most expensive prices for all token requests for now.
    We want to charge all tokens at the price of an input_cache_write -- just an arbitrary assumption of how we want to consumed tokens.
    Thus we scale other tokens in relation to their price of an input_cache_write.

    At the time of writing:
    Claude input tokens (cache write): $3.75/M
    OpenAI cache-read: 0.25*Input_token_cost
    Claude and Gemini output tokens: $15/M
    """
    assumed_input_token_cost = 3.75
    assumed_output_token_cost = 15
    assumed_cache_read_token_cost = 0.25*assumed_input_token_cost

    scaled_cache_read_tokens = cache_read_tokens * \
        assumed_cache_read_token_cost / assumed_input_token_cost
    scaled_output_tokens = output_tokens * \
        assumed_output_token_cost / assumed_input_token_cost

    payable_tokens = int(
        round(input_tokens + scaled_cache_read_tokens + scaled_output_tokens))

    return payable_tokens
