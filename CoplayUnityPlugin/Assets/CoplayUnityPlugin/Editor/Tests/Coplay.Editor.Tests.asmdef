{"name": "Coplay.Editor.Tests", "rootNamespace": "Coplay.Tests", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "Coplay.Editor", "TestData", "Coplay"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll", "Newtonsoft.Json.dll", "Microsoft.Extensions.DependencyInjection.dll", "Microsoft.Extensions.DependencyInjection.Abstractions.dll", "Moq.dll", "Testably.Abstractions.FileSystem.Interface.dll", "TestableIO.System.IO.Abstractions.Wrappers.dll", "TestableIO.System.IO.Abstractions.TestingHelpers.dll", "System.Collections.Immutable.dll", "System.Reactive.dll", "Microsoft.Extensions.Logging.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}