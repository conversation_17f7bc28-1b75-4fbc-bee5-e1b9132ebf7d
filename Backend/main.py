import hashlib
import logging
import json
import os
from tempfile import NamedTemporaryF<PERSON>
from uuid import uuid4
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path
from datetime import datetime, timezone, timedelta

from fastapi import Depends, FastAPI, Request, HTTPException, UploadFile, File, Form, BackgroundTasks, Response, Query
from fastapi.middleware.gzip import GZipMiddleware
from supabase_auth.types import User
from supabase import AsyncClient
from fastapi_events.middleware import EventHandlerASGIMiddleware
from fastapi_events.handlers.local import local_handler
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from psycopg.rows import class_row

from utils.gcp_utils import setup_gcp_credentials_if_key_file_exists
# Copy your gcp credentials json file to the Backend folder and name it coplay-backend-key.json
# Saves you from having to use google cloud auth every day.
setup_gcp_credentials_if_key_file_exists() # We need to do this before importing logger, otherwise you get daily gcp cloud authentication errors as a dev

if 'LANGSMITH_TRACING' not in os.environ:
    os.environ['LANGSMITH_TRACING'] = 'true'
if 'LANGCHAIN_OTEL_ENABLED' not in os.environ:
    os.environ['LANGCHAIN_OTEL_ENABLED'] = 'true'
if 'LANGSMITH_OTEL_SPAN_TTL_SECONDS' not in os.environ:
    os.environ['LANGSMITH_OTEL_SPAN_TTL_SECONDS'] = '300'
if 'LANGSMITH_ENDPOINT' not in os.environ:
    os.environ['LANGSMITH_ENDPOINT'] = 'https://api.smith.langchain.com'

if 'LANGSMITH_PROJECT' not in os.environ:
    os.environ['LANGSMITH_PROJECT'] = 'pr-coplay-default'

if 'LANGSMITH_API_KEY' not in os.environ:
    os.environ['LANGSMITH_API_KEY'] = '***************************************************'

from coplay.dalle_generate_image import dalle_generate_image
from coplay.imagen_generate_image import imagen_generate_image
from coplay.logger import logger, is_test_environment, SEVERITY_MAP
from coplay.supabase_db import connection_pool, top_up_low_balance_subscribers
from coplay.models import UserAttachedModels, VersionModel, LogEntry, SceneGenerationRequest, SceneGenerationStatus, AutocompleteRequest, ImageGenerationRequest, FeedbackRequest, LogArchiveUploadRequest, LogArchiveUploadResponse
from coplay.assistants import autocomplete_assistant, scene_assistant
from coplay.notifications import DiscordNotifier
from coplay.routers.auth import get_current_user_optional, router as auth_router, get_current_user, get_validated_device_id
from coplay.routers.stripe import router as stripe_router
from coplay.routers.dependencies import router as dependencies_router
from coplay.routers.image_functions import router as image_functions_router
from coplay.routers.assets import router as assets_router
from coplay.routers.assistant import router as assistant_router
from coplay.supabase_db import get_admin_supabase_client
from coplay.update_service import check_version
from coplay.request_utils import extract_coplay_version
from coplay.context.update import update_context_async, context_lock
from coplay.constants import GCP_PROJECT_ID, MAX_LOG_CHUNK_SIZE, CONTEXT_SYNC_MAX_LOCK_WAIT, SUPABASE_BUCKET
import coplay.feedback_service as feedback_service
from coplay.opentelemetry.setup import initialize_otel
from coplay.opentelemetry.helpers import CoplayAttributes, set_trace_attribute
from coplay.storage import download_bytes_from_supabase


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Preload autocomplete models when the app starts
    if not os.environ.get('TESTING') and not os.environ.get('DEVELOPMENT'):
        try:
            await autocomplete_assistant.preload_models()
        except Exception as e:
            logger.error(f"Failed to preload autocomplete models: {e}")
    
    await connection_pool.open()

    scheduler = AsyncIOScheduler(timezone="UTC")
    scheduler.add_job(top_up_low_balance_subscribers, "cron", hour=0)
    if os.environ.get('DEBUG_MEMORY') == 'true':
        from coplay.utils.memory import log_memory_usage, take_base_snapshot
        scheduler.add_job(take_base_snapshot, "date", run_date=datetime.now(timezone.utc) + timedelta(minutes=1))
        scheduler.add_job(log_memory_usage, "cron", minute='*')

    scheduler.start()

    yield

    scheduler.shutdown(wait=True)
    await connection_pool.close()

class EndpointFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        try:
            message = record.getMessage()
            return ("/log" not in message and 
                    "/health" not in message and 
                    "/assistant/stream" not in message and
                    "/image/functions/stream" not in message and
                    "/assets/status" not in message and
                    "/scene/status" not in message)
        except Exception:
            # Return True to allow the message through if there's an error
            # This prevents crashes during shutdown
            # We need this for CI tests to not end with a segfault
            return True

app = FastAPI(lifespan=lifespan)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(EventHandlerASGIMiddleware, handlers=[local_handler])

# FastAPI and HTTP client instrumentation for OpenTelemetry
initialize_otel(GCP_PROJECT_ID)
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
FastAPIInstrumentor.instrument_app(app, excluded_urls="health,log")

# -----------------------------------------------------------------------------
# Ensure BackgroundTasks do not block the response cycle (especially in tests)
# -----------------------------------------------------------------------------
# In Starlette's default behaviour BackgroundTasks are awaited before the request
# handler returns.  This is fine in production but it prevents our integration
# tests (which use httpx.AsyncClient + ASGITransport) from observing incremental
# streaming chunks – the test client waits for every background task to finish
# before the response is returned.  To mirror production behaviour inside the
# test-runner we detach background tasks from the request lifecycle whenever the
# TESTING environment variable is set (and always if the optional
# `asgi_background` package is available).
try:
    # Prefer the robust implementation from the third-party package if present.
    from asgi_background import BackgroundTaskMiddleware  # type: ignore
    app.add_middleware(BackgroundTaskMiddleware)
except ModuleNotFoundError:  # pragma: no cover – fallback if dependency absent
    import asyncio
    from starlette.background import BackgroundTasks  # type: ignore

    if is_test_environment():
        async def _detached_call(self: BackgroundTasks) -> None:  # noqa: D401
            """Execute each background task via `asyncio.create_task` and return immediately."""
            loop = asyncio.get_event_loop()
            for task in self.tasks:  # type: ignore[attr-defined]
                loop.create_task(task())  # schedule but *do not* await
        # Monkey-patch the BackgroundTasks.__call__ at runtime.
        BackgroundTasks.__call__ = _detached_call  # type: ignore[assignment]
# -----------------------------------------------------------------------------

if not is_test_environment(): # We need this for CI tests to not end with a segfault
    logging.getLogger("uvicorn.access").addFilter(EndpointFilter())

app.include_router(auth_router)
app.include_router(stripe_router)
app.include_router(image_functions_router)
app.include_router(dependencies_router)
app.include_router(assets_router)
app.include_router(assistant_router)

from coplay.uploaded_model_processor import process_uploaded_model # putting it down here such that creds are initialized for local docker tests

# Add this near other constants
SCENE_GENERATION_JOBS = {}  # In-memory storage for jobs (consider using Redis for production)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/version")
async def version(version_model: VersionModel, supabase_client: AsyncClient = Depends(get_admin_supabase_client)):
    return await check_version(version_model, supabase_client)

async def process_log(payload: LogEntry, user: User, request: Request):
    message_chunks = [payload.message[i:i+MAX_LOG_CHUNK_SIZE] for i in range(0, len(payload.message), MAX_LOG_CHUNK_SIZE)]
    total_chunks = len(message_chunks)
    coplay_version = extract_coplay_version(request) or "unknown"

    for chunk_index, message in enumerate(message_chunks):
        logger.log(
            message,
            severity=SEVERITY_MAP.get(payload.severity, logging.INFO),
            timestamp=payload.timestamp,
            labels={
                CoplayAttributes.USER_ID: user.id if user else "unknown",
                CoplayAttributes.USER_EMAIL: user.email if user else "unknown",
                CoplayAttributes.USER_AGENT: request.headers.get("User-Agent", "unknown"),
                CoplayAttributes.VERSION: coplay_version,
                CoplayAttributes.UNITY_VERSION: payload.unity_version or "unknown",
                CoplayAttributes.THREAD_ID: payload.thread_id or "unknown"
            },
            chunk_id=chunk_index,
            total_chunks=total_chunks,
            skip_console=True
        )

    await DiscordNotifier.handle_log_entry(
        payload.message, 
        user.email if user else "unknown", 
        payload.timestamp, 
        unity_version=payload.unity_version, 
        coplay_version=coplay_version,
        thread_id=payload.thread_id
    )

@app.post("/log")
async def log(payload: LogEntry, request: Request, background_tasks: BackgroundTasks, current_user: User = Depends(get_current_user_optional)):
    background_tasks.add_task(process_log, payload, current_user, request)

    return Response(status_code=202)


@app.post("/logs/upload-archive")
async def upload_log_archive(
    archive_file_name: str = Form(...),
    archive_size: str = Form(...),
    timestamp: str = Form(...),
    unity_version: str = Form(...),
    platform: str = Form(...),
    archive: UploadFile = File(...),
    current_user: User = Depends(get_current_user_optional),
    device_id: str = Depends(get_validated_device_id),
    supabase_client: AsyncClient = Depends(get_admin_supabase_client)
):
    """
    Upload a log archive to Supabase storage for debugging purposes.
    Device ID and user email are extracted from headers.
    Sends Discord notification with signed download link.
    """
    try:                
        # Read archive content
        archive_content = await archive.read()
        
        # Generate unique file path organized by device ID
        now = datetime.now(timezone.utc)
        date_path = now.strftime("%Y/%m/%d")
        unique_filename = f"{uuid4()}_{archive_file_name}"
        file_path = f"log-archives/{device_id}/{date_path}/{unique_filename}"
        
        logger.info(f"Uploading log archive {archive_file_name} to {file_path} for device {device_id}")
        
        # Upload to Supabase storage
        try:
            result = await supabase_client.storage.from_(SUPABASE_BUCKET).upload(
                path=file_path,
                file=archive_content,
                file_options={"content-type": "application/zip"}
            )
            
            # Handle potential errors from old/new SDK versions
            upload_error = getattr(result, "error", None)
            if upload_error:
                logger.error(f"Failed to upload log archive to Supabase: {upload_error}")
                raise HTTPException(status_code=500, detail=f"Upload failed: {upload_error}")
                
        except Exception as e:
            logger.error(f"Supabase storage upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")
        
        # Create a signed URL that expires in 7 days (604800 seconds)
        try:
            signed_url_result = await supabase_client.storage.from_(SUPABASE_BUCKET).create_signed_url(
                path=file_path,
                expires_in=604800
            )
            
            signed_url_error = getattr(signed_url_result, "error", None)
            if signed_url_error:
                logger.error(f"Failed to create signed URL: {signed_url_error}")
                raise HTTPException(status_code=500, detail=f"Signed URL creation failed: {signed_url_error}")
            
            # Extract signed URL from different possible response shapes
            if isinstance(signed_url_result, dict):
                signed_url = signed_url_result.get("signedURL") or signed_url_result.get("signed_url")
            else:
                signed_url = getattr(signed_url_result, "signed_url", None)
            
            if not signed_url:
                logger.error("Signed URL missing in Supabase response")
                raise HTTPException(status_code=500, detail="Signed URL missing in Supabase response")
                
        except Exception as e:
            logger.error(f"Supabase signed URL error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Signed URL creation failed: {str(e)}")
        
        # Send Discord notification
        try:
            notifier = DiscordNotifier(DiscordNotifier.WEBHOOK_URL_FEEDBACK)
            message = f"**Log Archive Uploaded**\n"
            message += f"User: {current_user.email if current_user else 'unknown'}\n"
            message += f"Device ID: {device_id}\n"
            message += f"Unity Version: {unity_version}\n"
            message += f"Platform: {platform}\n"
            message += f"Archive Size: {archive_size} bytes\n"
            message += f"[Download Archive](<{signed_url}>)"
            
            await notifier.notify(message)
            logger.info(f"Sent Discord notification for log archive upload from {current_user.email if current_user else 'unknown'}")
        except Exception as e:
            # Don't fail the upload if Discord notification fails
            logger.error(f"Failed to send Discord notification: {str(e)}")
        
        logger.info(f"Successfully uploaded log archive {archive_file_name} to {file_path}, signed URL created")
        
        return LogArchiveUploadResponse(
            success=True,
            archive_url=signed_url,
            archive_path=file_path
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error uploading log archive: {str(e)}")
        return LogArchiveUploadResponse(
            success=False,
            error=f"Failed to upload log archive: {str(e)}"
        )

@app.post("/autocomplete")
async def autocomplete(request: AutocompleteRequest):
    """
    Endpoint for fast FIM (Fill-In-Middle) autocompletion using a small, fast model.
    This is optimized for low-latency code completion.
    """
    try:
        response = await autocomplete_assistant.generate_completion(
            prefix=request.prefix,
            suffix=request.suffix,
            language=request.language,
            model=request.model
        )
        return response
    except Exception as e:
        print(f"[Autocomplete] Error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Autocomplete failed: {str(e)}")

@app.post("/scene/generate")
async def create_scene_generation(request: SceneGenerationRequest, current_user: User = Depends(get_current_user), device_id: str = Depends(get_validated_device_id)):
    job_id = str(uuid4())
    SCENE_GENERATION_JOBS[job_id] = {
        "status": "pending",
        "result": None,
        "error": None,
        "current_iteration": 0,
        "total_iterations": request.depth,
        "iteration_status": "Starting"
    }
    
    # Start the generation in a background task
    asyncio.create_task(run_scene_generation(job_id, request, device_id, current_user))
    
    return SceneGenerationStatus(
        job_id=job_id,
        status="pending",
        current_iteration=0,
        total_iterations=request.depth,
        iteration_status="Starting"
    )

async def run_scene_generation(job_id: str, request: SceneGenerationRequest, device_id: str, current_user: User):
    set_trace_attribute(CoplayAttributes.USER_ID, current_user.id)
    set_trace_attribute(CoplayAttributes.USER_EMAIL, current_user.email)
    set_trace_attribute(CoplayAttributes.DEVICE_ID, device_id)
    set_trace_attribute(CoplayAttributes.TASK_ID, job_id)

    try:
        def update_status(current_iteration, total_iterations, status_message):
            SCENE_GENERATION_JOBS[job_id].update({
                "current_iteration": current_iteration,
                "total_iterations": total_iterations,
                "iteration_status": status_message
            })
        
        SCENE_GENERATION_JOBS[job_id]["status"] = "processing"

        logger.debug(f"fbx files: {request.attached_fbx_files}")

        async with connection_pool.connection() as conn:
            cur = conn.cursor(row_factory=class_row(UserAttachedModels))
            attached_fbx_files = await (await cur.execute(
                "SELECT * FROM user_attached_models WHERE path = ANY(%s) AND device_id = %s",
                (request.attached_fbx_files, device_id)
            )).fetchall()
        
        supabase_client = await get_admin_supabase_client()
        
        for file in attached_fbx_files:
            if file.local_ply_path is None:
                tempfile = NamedTemporaryFile(delete=False, suffix=".ply")
                tempfile.write(await download_bytes_from_supabase(file.ply_path, supabase_client))
                tempfile.close()
                file.local_ply_path = tempfile.name
        
        # Now we can directly await generate_scene since it's async
        json_file_path = await scene_assistant.generate_scene(
            request.description,
            request.depth,
            update_status,
            attached_fbx_files,  # Pass the FBX files to the generation function
            request.model
        )

        with open(json_file_path, "r") as file:
            json_content = json.load(file)

        SCENE_GENERATION_JOBS[job_id].update({
            "status": "completed",
            "result": json_content,
            "iteration_status": "Complete"
        })

    except Exception as e:
        SCENE_GENERATION_JOBS[job_id].update({
            "status": "failed",
            "error": str(e),
            "iteration_status": "Failed"
        })
        raise

@app.get("/scene/status/{job_id}")
async def get_scene_status(job_id: str, current_user: User = Depends(get_current_user)):
    if job_id not in SCENE_GENERATION_JOBS:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = SCENE_GENERATION_JOBS[job_id]
    return SceneGenerationStatus(
        job_id=job_id,
        status=job["status"],
        current_iteration=job["current_iteration"],
        total_iterations=job["total_iterations"],
        iteration_status=job["iteration_status"],
        result=job["result"],
        error=job["error"],
    )

@app.get("/context/{context_id}")
async def get_context_hash(context_id: str, device_id: str = Depends(get_validated_device_id), supabase_client: AsyncClient=Depends(get_admin_supabase_client)):
    lock = context_lock[f"{device_id}_{context_id}"]
    try: 
        await lock.acquire(timeout=CONTEXT_SYNC_MAX_LOCK_WAIT, wait_until_synced=True)
        response = await supabase_client.table('contexts').select("hash").eq('device_id', device_id).eq('context_id', context_id).execute()

        if response.data is None or len(response.data) == 0:
            raise HTTPException(status_code=404, detail="Context not found")
        return response.data[0]['hash']
    except TimeoutError:
        raise HTTPException(status_code=408, detail="Context sync timeout")
    finally:
        lock.release()

@app.post("/context/{context_id}")
async def update_context(context_id: str, request: Request, background_tasks: BackgroundTasks, device_id: str = Depends(get_validated_device_id)):    
    body = await request.json()
    lock = context_lock[f"{device_id}_{context_id}"]

    try:
        # Set the is_synced event to False to indicate that the context will be updated
        await lock.clear_is_synced(timeout=CONTEXT_SYNC_MAX_LOCK_WAIT)
        background_tasks.add_task(update_context_async, context_id, device_id, body)
        return Response(status_code=202)
    except TimeoutError:
        raise HTTPException(status_code=408, detail="Context sync timeout")

# New FBX upload endpoint
@app.post("/upload-fbx")
async def upload_fbx(
    relative_path: str = Form(...),
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: dict = Depends(get_current_user),
    device_id: str = Depends(get_validated_device_id)
):
    with NamedTemporaryFile(delete=False, suffix=".fbx") as f:
        content = await file.read()
        f.write(content)
    
    content_hash = hashlib.sha256(content).hexdigest()
    
    # Add the background task to process the fbx file
    background_tasks.add_task(process_uploaded_model, f.name, relative_path, current_user.id, content_hash, device_id)

    return Response(status_code=202)

@app.post("/image/generate")
async def generate_image_generic(
    request: ImageGenerationRequest,
    provider: str = Query("dall-e", description="Image provider: 'dall-e' or 'imagen'"),
    current_user: User = Depends(get_current_user)
):
    """
    Generate images via DALL·E or Imagen based on provider param.
    """
    try:
        if provider.lower() == "imagen":
            image_url = await imagen_generate_image(request)
        else:
            image_url = await dalle_generate_image(request)
        return {"status": "success", "image_url": image_url}
    except Exception as e:
        logger.error(f"Error generating image ({provider}): {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate image: {str(e)}"
        )

@app.post("/feedback")
async def submit_feedback(feedback: FeedbackRequest, user: User = Depends(get_current_user_optional)):
    """
    Submit user feedback for an agent run.
    
    Args:
        feedback: The feedback details including run_id, key, score, and optional comment
        user_email: The authenticated user email
        
    Returns:
        A dictionary with status and message
    """
    return await feedback_service.submit_feedback(feedback, user.email if user else "unknown")

if __name__ == "__main__":
    import uvicorn
    # Configure Uvicorn logging
    log_config = uvicorn.config.LOGGING_CONFIG
    log_config["formatters"]["access"]["fmt"] = "%(levelname)s    %(asctime)s    %(message)s"
    log_config["formatters"]["default"]["fmt"] = "%(levelname)s    %(asctime)s    %(message)s"
    uvicorn.run(app, host="0.0.0.0", port=8080, log_config=log_config)
