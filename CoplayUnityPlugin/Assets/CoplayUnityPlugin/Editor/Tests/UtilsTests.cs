using System.Reflection;
using Coplay.Common;
using NUnit.Framework;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Coplay.Tests
{
    public class UtilsTests
    {
        private bool originalPlayingState;
        private Scene testScene;

        [SetUp]
        public void SetUp()
        {
            // Store original playing state to restore later
            originalPlayingState = EditorApplication.isPlaying;
        }

        [TearDown]
        public void TearDown()
        {
            // Restore original playing state
            // Note: We can't actually change EditorApplication.isPlaying in tests,
            // but this demonstrates the pattern
        }

        [Test]
        public void SafeMarkSceneDirty_ShouldNotThrowException_WhenInPlayMode()
        {
            // Arrange & Act & Assert
            // This test verifies that calling SafeMarkSceneDirty doesn't throw an exception
            // The actual behavior verification would require mocking Unity's EditorApplication.isPlaying
            Assert.DoesNotThrow(() => Utils.SafeMarkSceneDirty());
        }

        [Test]
        public void SafeMarkSceneDirty_ShouldNotThrowException_WhenNotInPlayMode()
        {
            // Arrange & Act & Assert
            // This test verifies that calling SafeMarkSceneDirty doesn't throw an exception
            // when not in play mode (which should be the normal case during unit testing)
            Assert.DoesNotThrow(() => Utils.SafeMarkSceneDirty());
        }

        [Test]
        public void SafeMarkSceneDirty_ShouldNotThrowException_WithSpecificScene()
        {
            // Arrange
            Scene activeScene = SceneManager.GetActiveScene();

            // Act & Assert
            // This test verifies that calling SafeMarkSceneDirty with a specific scene doesn't throw
            Assert.DoesNotThrow(() => Utils.SafeMarkSceneDirty(activeScene));
        }

        [Test]
        public void SafeMarkSceneDirty_ShouldHandleNullScene_Gracefully()
        {
            // Arrange & Act & Assert
            // This test verifies that calling SafeMarkSceneDirty with null scene works
            // (should default to active scene)
            Assert.DoesNotThrow(() => Utils.SafeMarkSceneDirty(null));
        }

        /// <summary>
        /// This test documents the expected behavior but cannot fully verify it due to Unity Editor dependencies.
        /// In a more advanced testing setup, we would use dependency injection or wrapper classes
        /// to properly mock EditorApplication.isPlaying and EditorSceneManager.MarkSceneDirty.
        /// </summary>
        [Test]
        public void SafeMarkSceneDirty_BehaviorDocumentation()
        {
            // This test serves as documentation for the expected behavior:
            // 1. When EditorApplication.isPlaying is true, MarkSceneDirty should NOT be called
            // 2. When EditorApplication.isPlaying is false, MarkSceneDirty SHOULD be called
            // 3. If no scene is provided, it should use SceneManager.GetActiveScene()
            // 4. If a scene is provided, it should use that specific scene

            // For now, we just verify the method exists and can be called
            var method = typeof(Utils).GetMethod("SafeMarkSceneDirty", BindingFlags.Public | BindingFlags.Static);
            Assert.IsNotNull(method, "SafeMarkSceneDirty method should exist");

            var parameters = method.GetParameters();
            Assert.AreEqual(1, parameters.Length, "Method should have one parameter");
            Assert.AreEqual(typeof(Scene?), parameters[0].ParameterType, "Parameter should be nullable Scene");
            Assert.IsTrue(parameters[0].IsOptional, "Parameter should be optional");
        }
    }
}
