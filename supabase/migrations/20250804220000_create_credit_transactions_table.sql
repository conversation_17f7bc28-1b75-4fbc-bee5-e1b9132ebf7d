-- Create table for credit transaction audit trail
create table if not exists public.credit_transactions (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid not null references auth.users(id) on delete cascade,
    amount decimal(10,4) not null,
    transaction_type varchar(50) not null check (transaction_type in ('top_up', 'consumption', 'refund', 'adjustment', 'subscription_renewal')),
    transaction_id varchar(255), -- Stripe payment intent ID or other external transaction ID
    balance_after decimal(10,4) not null,
    description text,
    metadata jsonb,
    created_at timestamptz not null default now()
);

-- Enable RLS
alter table public.credit_transactions enable row level security;

-- Create policy for users to view their own transaction history (only if it doesn't exist)
do $$
begin
    if not exists (
        select 1 from pg_policies 
        where schemaname = 'public' 
        and tablename = 'credit_transactions' 
        and policyname = 'Users can view their own transaction history'
    ) then
        create policy "Users can view their own transaction history"
            on public.credit_transactions
            for select
            to authenticated
            using (auth.uid() = user_id);
    end if;
end $$;

-- Create policy for service role to insert transactions (only if it doesn't exist)
do $$
begin
    if not exists (
        select 1 from pg_policies 
        where schemaname = 'public' 
        and tablename = 'credit_transactions' 
        and policyname = 'Service role can insert transactions'
    ) then
        create policy "Service role can insert transactions"
            on public.credit_transactions
            for insert
            to service_role
            with check (true);
    end if;
end $$;

-- Create indexes for faster lookups
create index if not exists credit_transactions_user_id_idx on public.credit_transactions(user_id);
create index if not exists credit_transactions_created_at_idx on public.credit_transactions(created_at desc);
create index if not exists credit_transactions_transaction_type_idx on public.credit_transactions(transaction_type);
create index if not exists credit_transactions_transaction_id_idx on public.credit_transactions(transaction_id) where transaction_id is not null;

-- Add comment to table
comment on table public.credit_transactions is 'Audit trail for all credit transactions including top-ups, consumption, refunds, and adjustments';
comment on column public.credit_transactions.transaction_type is 'Type of transaction: top_up, consumption, refund, adjustment, subscription_renewal';
comment on column public.credit_transactions.transaction_id is 'External transaction ID (e.g., Stripe payment intent ID)';
comment on column public.credit_transactions.balance_after is 'User credit balance after this transaction';
comment on column public.credit_transactions.metadata is 'Additional transaction metadata as JSON';
