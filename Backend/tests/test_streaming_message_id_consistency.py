#!/usr/bin/env python3
"""
The original issue that prompted these tests were that openAI models would result in duplicate messages shown to the user in the UI.
We learned that message_ids were not consistent and thus we check that we only get one out per message turn.

Integration test for message ID consistency in streaming responses across multiple AI models.
Tests that the message_id remains the same throughout the streaming response.
I.e. when the model returns a block of text for a message, the message_id should stay the same until completion, such that we end up with a single message/block to show for the user.
"""

import pytest
import asyncio
import httpx
from typing import List

from coplay.models import AIModel, AssistantMode, PromptResponse


class TestStreamingMessageIdConsistency:
    """Integration test to verify message ID consistency across streaming responses."""

    @pytest.fixture
    def haiku_prompt(self):
        """
        The original issue that prompted these tests were that openAI models would result in duplicate messages shown to the user in the UI.
        We learned that message_ids were not consistent and thus we check that we only get one out per message turn.
        Test prompt fixture that explicitly asks not to use tools.
        """
        return """
        Without using any tools, write a haiku.
        """


    @pytest.mark.integration
    @pytest.mark.parametrize("model", [
        AIModel.GPT41,
        AIModel.O3,
        AIModel.GPT5,
        AIModel.CLAUDE_4_SONNET,
        AIModel.CLAUDE_4_THINKING,
        AIModel.GEMINI_2_5_PRO # TODO: maybe test all models here automatically? and elsewhere in our tests?
    ])
    async def test_streaming_message_id_consistency(
        self, 
        async_client_with_auth: httpx.AsyncClient, 
        haiku_prompt: str,
        model: AIModel
    ):
        """
        The original issue that prompted these tests were that openAI models would result in duplicate messages shown to the user in the UI.
        We learned that message_ids were not consistent and thus we check that we only get one out per message turn.
        Integration test: Verify that message_id remains consistent throughout streaming response.
        
        This test:
        1. Creates a new thread
        2. Sends a haiku prompt (no tools should be used)
        3. Streams the response
        4. Verifies message_id consistency across all chunks
        5. Verifies no tools are called
        6. Verifies a message is returned
        """
        # 1. Create a thread
        create_thread_response = await async_client_with_auth.post(
            "/assistant", 
            json={"assistant_mode": AssistantMode.NORMAL.value, "name": f"test_streaming_{model.value}"}
        )
        assert create_thread_response.status_code == 200
        thread_id = create_thread_response.json()["id"]

        # 2. Start streaming request
        prompt_data = {
            "thread_id": thread_id,
            "prompt": haiku_prompt,
            "model": model.value,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }

        stream_start_response = await async_client_with_auth.post(
            "/assistant/stream", 
            json=prompt_data
        )
        assert stream_start_response.status_code == 202, f"Stream start failed for {model.value}: {stream_start_response.text}"
        # Extract task_id from the initial response
        chunk = PromptResponse(**stream_start_response.json())
        task_id = chunk.task_id
        assert task_id is not None, f"No task_id in initial response for {model.value}"

        # 3. Poll the streaming endpoint
        chunks: List[PromptResponse] = []
        message_ids_seen: set[str] = set()

        chunk_count = 0
        
        while chunk.finished is False:
            poll_response = await async_client_with_auth.get(f"/assistant/stream/{task_id}")
            assert poll_response.status_code == 200, f"Polling failed for {model.value}: {poll_response.text}"
            
            chunk = PromptResponse(**poll_response.json())
            chunks.append(chunk)
            chunk_count += 1
            # Track message IDs (only for non-empty message_id)
            if chunk.message_id:
                message_ids_seen.add(chunk.message_id)

            # NOTE: We need to wait at least 200ms to avoid race conditions. See the test_race_condition_simply.py file for more details.
            await asyncio.sleep(0.2)
        
        # 4. Verify we got a complete response
        assert len(chunks) > 0, f"No chunks received for {model.value}"
        if model != AIModel.GEMINI_2_5_PRO: 
            assert chunk_count > 2, f"We should receive at least 2 chunks otherwise streaming is not working for this test."
        
        final_chunk = chunks[-1]
        assert final_chunk.finished, f"Final chunk not marked as finished for {model.value}"
        assert final_chunk.type != "error", f"Received error response for {model.value}: {final_chunk.message}"

        # 5. Verify message ID consistency
        # All chunks with a message_id should have the same message_id
        assert len(message_ids_seen) <= 1, f"Multiple message IDs found for {model.value}: {message_ids_seen}"
        
        # At least one chunk should have a message_id (the final response)
        assert len(message_ids_seen) == 1, f"No message_id found in any chunk for {model.value}"
        
        # 6. Verify no tools were called (since we explicitly asked for no tools)
        for chunk in chunks:
            if chunk.tool_calls:
                assert len(chunk.tool_calls) == 0, f"Tools were called despite asking not to use tools for {model.value}: {chunk.tool_calls}"

        # 7. Verify we got a message response
        final_message = final_chunk.message
        assert final_message is not None, f"No message in final response for {model.value}"
        assert len(final_message.strip()) > 0, f"Empty message in final response for {model.value}"
        
        # 8. Verify it looks like a haiku (basic check - should have some content)
        # We won't be too strict about haiku format, just verify we got reasonable content
        assert len(final_message.split()) >= 3, f"Response too short to be a haiku for {model.value}: '{final_message}'"

        print(f"✅ Test passed for {model.value}:")
        print(f"   Message ID: {list(message_ids_seen)[0]}")
        print(f"   Chunks received: {len(chunks)}")
        print(f"   Final message: {final_message[:100]}...")

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )


if __name__ == "__main__":
    pytest.main(['-v', __file__, '-m', 'integration']) 