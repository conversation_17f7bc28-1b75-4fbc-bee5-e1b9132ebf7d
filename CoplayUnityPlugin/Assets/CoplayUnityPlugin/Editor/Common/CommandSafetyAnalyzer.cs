using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Coplay.Common;

namespace Coplay.Common
{
    /// <summary>
    /// Analyzes shell commands to determine their safety level and prevent dangerous operations.
    /// </summary>
    public static class CommandSafetyAnalyzer
    {
        /// <summary>
        /// Represents the risk level of a command
        /// </summary>
        public enum CommandRisk
        {
            Safe,
            Fatal
        }

        /// <summary>
        /// Contains the analysis result of a command
        /// </summary>
        public class CommandAnalysis
        {
            public CommandRisk Risk { get; set; }
            public List<string> Reasons { get; set; } = new List<string>();
            public string Command { get; set; }
        }

        // Only truly system-critical commands that should never be executed
        private static readonly HashSet<string> FatalCommands = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // System destruction
            "format", "fdisk", "mkfs", "mkfs.ext4", "mkfs.ext3", "mkfs.ext2",
            "mkfs.fat", "mkfs.ntfs", "mkfs.xfs", "mkfs.btrfs",
            // System control
            "shutdown", "reboot", "halt", "poweroff", "restart",
            // System files destruction
            "dd", "shred", "wipe",
            // User management
            "passwd", "useradd", "userdel", "usermod", "adduser", "deluser"
        };

        // Fatal command patterns (regex-based detection for dangerous combinations)
        private static readonly List<Regex> FatalPatterns = new List<Regex>
        {
            // Root-level deletions (any rm targeting root)
            new Regex(@"\brm\s+.*\s+/(?:\*|\s*$)", RegexOptions.IgnoreCase), // rm ... / or rm ... /*
            
            // System directory deletions (critical system paths starting with /)
            new Regex(@"\brm\s+.*-[rf]+.*\s+/(home|usr|etc|var|boot|opt|bin|sbin|lib|lib64|sys|proc|dev|mnt|media)\b", RegexOptions.IgnoreCase), // rm -rf /system-dir
            new Regex(@"\brm\s+.*--(?:recursive|force).*\s+/(home|usr|etc|var|boot|opt|bin|sbin|lib|lib64|sys|proc|dev|mnt|media)\b", RegexOptions.IgnoreCase), // rm --recursive/--force /system-dir
            
            // Windows destructive deletions
            new Regex(@"\b(del|rd)\s+/[sq].*\\\*", RegexOptions.IgnoreCase), // del/rd /s or /q with wildcards
            
            // Sudo with system commands
            new Regex(@"\bsudo\s+(rm\s+-[rf]+|format|shutdown|reboot|halt|dd\s+.*of=/dev/)", RegexOptions.IgnoreCase),
            
            // dd to system devices
            new Regex(@"\bdd\s+.*of=/dev/(sd|hd|nvme)", RegexOptions.IgnoreCase),
            new Regex(@"\bdd\s+.*of=\\\\\.\\\\PhysicalDrive", RegexOptions.IgnoreCase), // Windows physical drive
            
            // Pipe to critical system locations
            new Regex(@">\s*/etc/(passwd|shadow|hosts)", RegexOptions.IgnoreCase),
            new Regex(@">\s*/boot/", RegexOptions.IgnoreCase)
        };

        /// <summary>
        /// Analyzes a command string and returns its risk level with reasons
        /// </summary>
        /// <param name="command">The command string to analyze</param>
        /// <returns>CommandAnalysis containing risk level and reasons</returns>
        public static CommandAnalysis AnalyzeCommand(string command)
        {
            var analysis = new CommandAnalysis { Command = command };

            if (string.IsNullOrWhiteSpace(command))
            {
                analysis.Risk = CommandRisk.Safe;
                analysis.Reasons.Add("Empty command");
                return analysis;
            }

            try
            {
                // Tokenize the command
                var tokens = TokenizeCommand(command.Trim());
                if (tokens.Length == 0)
                {
                    analysis.Risk = CommandRisk.Safe;
                    analysis.Reasons.Add("No command tokens found");
                    return analysis;
                }

                string baseCommand = tokens[0].ToLowerInvariant();

                // Check command lists - only truly dangerous system commands
                if (FatalCommands.Contains(baseCommand))
                {
                    analysis.Risk = CommandRisk.Fatal;
                    analysis.Reasons.Add($"'{baseCommand}' is a system-level command that could damage the system");

                    // Add specific reasoning for dangerous system commands
                    switch (baseCommand)
                    {
                        case "format":
                        case "mkfs":
                            analysis.Reasons.Add("Can format drives and destroy all data");
                            break;
                        case "shutdown":
                        case "reboot":
                        case "halt":
                        case "poweroff":
                        case "restart":
                            analysis.Reasons.Add("Can shut down or restart the system");
                            break;
                        case "dd":
                        case "shred":
                        case "wipe":
                            analysis.Reasons.Add("Can overwrite drives and destroy data");
                            break;
                        case "passwd":
                        case "useradd":
                        case "userdel":
                        case "usermod":
                        case "adduser":
                        case "deluser":
                            analysis.Reasons.Add("Can modify system users and security");
                            break;
                    }
                    return analysis;
                }

                // Check for fatal patterns first
                foreach (var pattern in FatalPatterns)
                {
                    if (pattern.IsMatch(command))
                    {
                        analysis.Risk = CommandRisk.Fatal;
                        analysis.Reasons.Add($"Matches dangerous pattern: {pattern}");
                        return analysis;
                    }
                }

                // All other commands are considered safe for LLM approval
                analysis.Risk = CommandRisk.Safe;
                analysis.Reasons.Add($"'{baseCommand}' is classified as safe - approval handled by LLM");
                return analysis;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Command safety analysis failed for '{command}': {ex.Message}");

                // Default to caution on parsing failure
                analysis.Risk = CommandRisk.Fatal;
                analysis.Reasons.Add("Failed to parse command - blocked for safety");
                return analysis;
            }
        }

        /// <summary>
        /// Simple command tokenizer that handles basic quoting
        /// </summary>
        /// <param name="command">Command string to tokenize</param>
        /// <returns>Array of command tokens</returns>
        public static string[] TokenizeCommand(string command)
        {
            if (string.IsNullOrWhiteSpace(command))
                return new string[0];

            var tokens = new List<string>();
            var currentToken = "";
            var inQuotes = false;
            var quoteChar = '\0';

            for (int i = 0; i < command.Length; i++)
            {
                char c = command[i];

                if (!inQuotes && (c == '"' || c == '\''))
                {
                    // Start of quoted section
                    inQuotes = true;
                    quoteChar = c;
                }
                else if (inQuotes && c == quoteChar)
                {
                    // End of quoted section
                    inQuotes = false;
                    quoteChar = '\0';
                }
                else if (!inQuotes && char.IsWhiteSpace(c))
                {
                    // Token separator
                    if (!string.IsNullOrEmpty(currentToken))
                    {
                        tokens.Add(currentToken);
                        currentToken = "";
                    }
                }
                else
                {
                    // Regular character
                    currentToken += c;
                }
            }

            // Add final token
            if (!string.IsNullOrEmpty(currentToken))
            {
                tokens.Add(currentToken);
            }

            return tokens.ToArray();
        }

        /// <summary>
        /// Gets a human-readable description of the risk level
        /// </summary>
        /// <param name="risk">The risk level</param>
        /// <returns>Description string</returns>
        public static string GetRiskDescription(CommandRisk risk)
        {
            return risk switch
            {
                CommandRisk.Safe => "Command is safe - approval handled by LLM",
                CommandRisk.Fatal => "Command is dangerous and blocked for safety",
                _ => "Unknown risk level"
            };
        }
    }
}
