using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using UnityEngine.SceneManagement;
using Coplay.Services.Threading;
using Microsoft.Extensions.Logging;
using UnityObject = UnityEngine.Object;
using Coplay.Services.Scene;
using Coplay.Services.Scene.Models;
using Moq;
using Coplay.Services;

namespace Coplay.Tests
{
    public class EditorStateProviderTests
    {
        private EditorStateProvider _editorStateProvider;
        private MockEditorThreadHelper _mockThreadHelper;
        private Mock<IEditorStateService> _mockEditorState;
        private MockLogger<EditorStateProvider> _mockLogger;
        private List<GameObject> _createdObjects;
        private List<Scene> _createdScenes;

        [SetUp]
        public void SetUp()
        {
            _mockThreadHelper = new MockEditorThreadHelper();
            _mockLogger = new MockLogger<EditorStateProvider>();
            _mockEditorState = new Mock<IEditorStateService>();
            _mockEditorState.SetupGet(x => x.IsPlaying).Returns(true);
            _editorStateProvider = new EditorStateProvider(_mockThreadHelper, _mockEditorState.Object, _mockLogger);
            _createdObjects = new List<GameObject>();
            _createdScenes = new List<Scene>();
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up created objects
            foreach (var obj in _createdObjects)
            {
                if (obj != null)
                {
                    UnityObject.DestroyImmediate(obj);
                }
            }
            _createdObjects.Clear();

            // Clean up created scenes
            foreach (var scene in _createdScenes)
            {
                if (scene.IsValid())
                {
                    SceneManager.UnloadSceneAsync(scene);
                }
            }
            _createdScenes.Clear();

            _mockThreadHelper?.Dispose();
        }

        [UnityTest]
        public IEnumerator TestSceneQueryWithMultipleScenesAndDontDestroyOnLoad()
        {
            // Create first scene
            var scene1 = SceneManager.CreateScene("TestScene1");
            _createdScenes.Add(scene1);
            
            // Create second scene
            var scene2 = SceneManager.CreateScene("TestScene2");
            _createdScenes.Add(scene2);

            // Load scenes additively
            SceneManager.SetActiveScene(scene1);
            yield return null;

            // Create objects in first scene
            var scene1Parent = new GameObject("Scene1Parent");
            _createdObjects.Add(scene1Parent);
            SceneManager.MoveGameObjectToScene(scene1Parent, scene1);

            var scene1Child1 = new GameObject("Scene1Child1");
            _createdObjects.Add(scene1Child1);
            scene1Child1.transform.SetParent(scene1Parent.transform);
            scene1Child1.AddComponent<MeshRenderer>();

            var scene1Child2 = new GameObject("Scene1Child2");
            _createdObjects.Add(scene1Child2);
            scene1Child2.transform.SetParent(scene1Parent.transform);
            scene1Child2.AddComponent<BoxCollider>();

            // Create objects in second scene
            var scene2Parent = new GameObject("Scene2Parent");
            _createdObjects.Add(scene2Parent);
            SceneManager.MoveGameObjectToScene(scene2Parent, scene2);

            var scene2Child1 = new GameObject("Scene2Child1");
            _createdObjects.Add(scene2Child1);
            scene2Child1.transform.SetParent(scene2Parent.transform);
            scene2Child1.AddComponent<Rigidbody>();

            var scene2Child2 = new GameObject("Scene2Child2");
            _createdObjects.Add(scene2Child2);
            scene2Child2.transform.SetParent(scene2Parent.transform);
            scene2Child2.AddComponent<AudioSource>();

            // Create objects under DontDestroyOnLoad
            var dontDestroyParent = new GameObject("DontDestroyParent");
            _createdObjects.Add(dontDestroyParent);
            UnityObject.DontDestroyOnLoad(dontDestroyParent);

            var dontDestroyChild1 = new GameObject("DontDestroyChild1");
            _createdObjects.Add(dontDestroyChild1);
            dontDestroyChild1.transform.SetParent(dontDestroyParent.transform);
            dontDestroyChild1.AddComponent<Camera>();

            var dontDestroyChild2 = new GameObject("DontDestroyChild2");
            _createdObjects.Add(dontDestroyChild2);
            dontDestroyChild2.transform.SetParent(dontDestroyParent.transform);
            dontDestroyChild2.AddComponent<Light>();

            yield return null; // Wait a frame for scene operations to complete

            // Test querying scene with default parameters (should get objects from active scene)
            var request = new QuerySceneRequest
            {
                OnlyPaths = false,
                IncludeInactive = true,
                Limit = 100
            };

            var queryTask = _editorStateProvider.QuerySceneAsync(request);
            while (!queryTask.IsCompleted)
            {
                yield return null;
            }
            var result = queryTask.Result;

            // Verify the result contains expected objects
            Assert.IsNotNull(result, "Query result should not be null");
            Assert.IsNotNull(result.Scenes, "Scenes list should not be null");
            Assert.Greater(result.Scenes.Count, 0, "Should find some scenes");

            // Get all game objects from all scenes
            var allGameObjects = result.Scenes.SelectMany(s => s.GameObjects).ToList();
            Assert.Greater(allGameObjects.Count, 0, "Should find some game objects");

            // Check that we can find objects from the active scene (scene1)
            var scene1Objects = allGameObjects.Where(go => go.Path.Contains("Scene1")).ToList();
            Assert.Greater(scene1Objects.Count, 0, "Should find objects from Scene1");

            // Verify specific objects exist from Scene1
            var scene1ParentInfo = allGameObjects.FirstOrDefault(go => go.Path == "/Scene1Parent");
            Assert.IsNotNull(scene1ParentInfo, "Should find Scene1Parent object");

            var scene1Child1Info = allGameObjects.FirstOrDefault(go => go.Path == "/Scene1Parent/Scene1Child1");
            Assert.IsNotNull(scene1Child1Info, "Should find Scene1Child1 object");
            Assert.IsTrue(scene1Child1Info.Components.Contains("MeshRenderer"), "Scene1Child1 should have MeshRenderer component");

            var scene1Child2Info = allGameObjects.FirstOrDefault(go => go.Path == "/Scene1Parent/Scene1Child2");
            Assert.IsNotNull(scene1Child2Info, "Should find Scene1Child2 object");
            Assert.IsTrue(scene1Child2Info.Components.Contains("BoxCollider"), "Scene1Child2 should have BoxCollider component");

            // Check that we can find objects from the second scene (scene2)
            var scene2Objects = allGameObjects.Where(go => go.Path.Contains("Scene2")).ToList();
            Assert.Greater(scene2Objects.Count, 0, "Should find objects from Scene2");

            // Verify specific objects exist from Scene2
            var scene2ParentInfo = allGameObjects.FirstOrDefault(go => go.Path == "/Scene2Parent");
            Assert.IsNotNull(scene2ParentInfo, "Should find Scene2Parent object");

            var scene2Child1Info = allGameObjects.FirstOrDefault(go => go.Path == "/Scene2Parent/Scene2Child1");
            Assert.IsNotNull(scene2Child1Info, "Should find Scene2Child1 object");
            Assert.IsTrue(scene2Child1Info.Components.Contains("Rigidbody"), "Scene2Child1 should have Rigidbody component");

            var scene2Child2Info = allGameObjects.FirstOrDefault(go => go.Path == "/Scene2Parent/Scene2Child2");
            Assert.IsNotNull(scene2Child2Info, "Should find Scene2Child2 object");
            Assert.IsTrue(scene2Child2Info.Components.Contains("AudioSource"), "Scene2Child2 should have AudioSource component");

            // Check that we can find DontDestroyOnLoad objects
            var dontDestroyObjects = allGameObjects.Where(go => go.Path.Contains("DontDestroy")).ToList();
            Assert.Greater(dontDestroyObjects.Count, 0, "Should find DontDestroyOnLoad objects");

            // Verify specific DontDestroyOnLoad objects exist
            var dontDestroyParentInfo = allGameObjects.FirstOrDefault(go => go.Path == "/DontDestroyParent");
            Assert.IsNotNull(dontDestroyParentInfo, "Should find DontDestroyParent object");

            var dontDestroyChild1Info = allGameObjects.FirstOrDefault(go => go.Path == "/DontDestroyParent/DontDestroyChild1");
            Assert.IsNotNull(dontDestroyChild1Info, "Should find DontDestroyChild1 object");
            Assert.IsTrue(dontDestroyChild1Info.Components.Contains("Camera"), "DontDestroyChild1 should have Camera component");

            var dontDestroyChild2Info = allGameObjects.FirstOrDefault(go => go.Path == "/DontDestroyParent/DontDestroyChild2");
            Assert.IsNotNull(dontDestroyChild2Info, "Should find DontDestroyChild2 object");
            Assert.IsTrue(dontDestroyChild2Info.Components.Contains("Light"), "DontDestroyChild2 should have Light component");

            // Verify that we have multiple scenes in the result
            Assert.GreaterOrEqual(result.Scenes.Count, 2, "Should have at least 2 scenes (including DontDestroyOnLoad scene)");

            // Check that scene information is correctly populated
            var activeSceneInfo = result.Scenes.FirstOrDefault(s => s.IsActive);
            Assert.IsNotNull(activeSceneInfo, "Should have an active scene");
            Assert.AreEqual("TestScene1", activeSceneInfo.Name, "Active scene should be TestScene1");

            // Test filtering by component
            var meshRendererRequest = new QuerySceneRequest
            {
                ComponentFilter = "MeshRenderer",
                OnlyPaths = false,
                IncludeInactive = true
            };

            var meshRendererTask = _editorStateProvider.QuerySceneAsync(meshRendererRequest);
            while (!meshRendererTask.IsCompleted)
            {
                yield return null;
            }
            var meshRendererResult = meshRendererTask.Result;
            Assert.IsNotNull(meshRendererResult, "MeshRenderer query result should not be null");
            var meshRendererGameObjects = meshRendererResult.Scenes.SelectMany(s => s.GameObjects).ToList();
            Assert.Greater(meshRendererGameObjects.Count, 0, "Should find objects with MeshRenderer");

            var foundMeshRendererObject = meshRendererGameObjects.FirstOrDefault(go => go.Path.Contains("Scene1Child1"));
            Assert.IsNotNull(foundMeshRendererObject, "Should find Scene1Child1 with MeshRenderer filter");

            // Test filtering by name
            var nameFilterRequest = new QuerySceneRequest
            {
                NameFilter = "Child1",
                OnlyPaths = false,
                IncludeInactive = true
            };

            var nameFilterTask = _editorStateProvider.QuerySceneAsync(nameFilterRequest);
            while (!nameFilterTask.IsCompleted)
            {
                yield return null;
            }
            var nameFilterResult = nameFilterTask.Result;
            Assert.IsNotNull(nameFilterResult, "Name filter query result should not be null");
            var nameFilterGameObjects = nameFilterResult.Scenes.SelectMany(s => s.GameObjects).ToList();
            Assert.Greater(nameFilterGameObjects.Count, 0, "Should find objects with 'Child1' in name");

            var foundNameFilterObject = nameFilterGameObjects.FirstOrDefault(go => go.Path.Contains("Child1"));
            Assert.IsNotNull(foundNameFilterObject, "Should find object with 'Child1' in name");

            // Test OnlyPaths = true
            var pathsOnlyRequest = new QuerySceneRequest
            {
                OnlyPaths = true,
                IncludeInactive = true
            };

            var pathsOnlyTask = _editorStateProvider.QuerySceneAsync(pathsOnlyRequest);
            while (!pathsOnlyTask.IsCompleted)
            {
                yield return null;
            }
            var pathsOnlyResult = pathsOnlyTask.Result;
            Assert.IsNotNull(pathsOnlyResult, "Paths only query result should not be null");
            
            var pathsOnlyGameObjects = pathsOnlyResult.Scenes.SelectMany(s => s.GameObjects).ToList();
            if (pathsOnlyGameObjects.Count > 0)
            {
                var firstObject = pathsOnlyGameObjects.First();
                Assert.IsNull(firstObject.Components, "Components should be null when OnlyPaths is true");
                Assert.IsNull(firstObject.Children, "Children should be null when OnlyPaths is true");
                Assert.IsNotEmpty(firstObject.Path, "Path should not be empty");
            }

            yield return null;
        }

        [UnityTest]
        public IEnumerator TestSceneQueryWithReferenceObject()
        {
            // Create a scene with nested hierarchy
            var scene = SceneManager.CreateScene("TestReferenceScene");
            _createdScenes.Add(scene);
            SceneManager.SetActiveScene(scene);

            // Create nested hierarchy
            var rootObject = new GameObject("RootObject");
            _createdObjects.Add(rootObject);
            SceneManager.MoveGameObjectToScene(rootObject, scene);

            var parentObject = new GameObject("ParentObject");
            _createdObjects.Add(parentObject);
            parentObject.transform.SetParent(rootObject.transform);

            var childObject1 = new GameObject("ChildObject1");
            _createdObjects.Add(childObject1);
            childObject1.transform.SetParent(parentObject.transform);

            var childObject2 = new GameObject("ChildObject2");
            _createdObjects.Add(childObject2);
            childObject2.transform.SetParent(parentObject.transform);

            var grandChildObject = new GameObject("GrandChildObject");
            _createdObjects.Add(grandChildObject);
            grandChildObject.transform.SetParent(childObject1.transform);

            yield return null;

            // Test querying with reference object
            var request = new QuerySceneRequest
            {
                ReferenceObjectPath = "/RootObject/ParentObject",
                OnlyPaths = false,
                IncludeInactive = true
            };

            var queryTask = _editorStateProvider.QuerySceneAsync(request);
            while (!queryTask.IsCompleted)
            {
                yield return null;
            }
            var result = queryTask.Result;

            Assert.IsNotNull(result, "Query result should not be null");
            Assert.IsNotNull(result.Scenes, "Scenes list should not be null");

            // Get all game objects from all scenes
            var referenceGameObjects = result.Scenes.SelectMany(s => s.GameObjects).ToList();

            // Should find the reference object and its children
            var parentInfo = referenceGameObjects.FirstOrDefault(go => go.Path == "/RootObject/ParentObject");
            Assert.IsNotNull(parentInfo, "Should find the reference ParentObject");

            var child1Info = referenceGameObjects.FirstOrDefault(go => go.Path == "/RootObject/ParentObject/ChildObject1");
            Assert.IsNotNull(child1Info, "Should find ChildObject1 under reference object");

            var child2Info = referenceGameObjects.FirstOrDefault(go => go.Path == "/RootObject/ParentObject/ChildObject2");
            Assert.IsNotNull(child2Info, "Should find ChildObject2 under reference object");

            var grandChildInfo = referenceGameObjects.FirstOrDefault(go => go.Path == "/RootObject/ParentObject/ChildObject1/GrandChildObject");
            Assert.IsNotNull(grandChildInfo, "Should find GrandChildObject under reference object hierarchy");

            yield return null;
        }

        // Mock implementations for testing
        private class MockEditorThreadHelper : IEditorThreadHelper
        {
            public Task ExecuteOnEditorThreadAsync(System.Action action)
            {
                action?.Invoke();
                return Task.CompletedTask;
            }

            public Task<T> ExecuteOnEditorThreadAsync<T>(System.Func<T> func)
            {
                var result = func != null ? func() : default(T);
                return Task.FromResult(result);
            }

            public Task<T> ExecuteAsyncOnEditorThreadAsync<T>(System.Func<Task<T>> asyncFunc)
            {
                return asyncFunc != null ? asyncFunc() : Task.FromResult(default(T));
            }

            public void Dispose()
            {
                // Mock implementation - nothing to dispose
            }
        }

        private class MockLogger<T> : ILogger<T>
        {
            public IDisposable BeginScope<TState>(TState state) => null;
            public bool IsEnabled(LogLevel logLevel) => true;
            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, System.Exception exception, System.Func<TState, System.Exception, string> formatter) { }
        }
    }
}
