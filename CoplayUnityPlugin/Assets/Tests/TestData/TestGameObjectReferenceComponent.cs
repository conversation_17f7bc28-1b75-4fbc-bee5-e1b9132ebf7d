using UnityEngine;

namespace Coplay.Tests
{
    /// <summary>
    /// Test component for testing GameObject reference assignment in prefabs.
    /// This component has a GameObject field that should be settable via PropertyFunctions.
    /// </summary>
    public class TestGameObjectReferenceComponent : MonoBehaviour
    {
        [SerializeField]
        public GameObject targetGameObject;
        
        [SerializeField]
        public GameObject anotherReference;
        
        // Test with different field types
        [SerializeField]
        public Transform targetTransform;
        
        [SerializeField]
        public string testString = "default";
        
        [SerializeField]
        public int testInt = 42;
    }
}
