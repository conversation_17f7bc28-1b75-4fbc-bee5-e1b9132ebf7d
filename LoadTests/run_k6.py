#!/usr/bin/env python3
"""
Coplay k6 Load Testing Script (Python)
Cross-platform runner for k6 load tests with environment variable support
Usage: python run-k6.py [script_name] [additional_env_vars...]
Example: python run-k6.py stream_test.js
Example: python run-k6.py stream_test.js VUS=20 DURATION=2m
"""

import os
import sys
import subprocess
import requests
import json
from pathlib import Path

# Color codes for terminal output
class Colors:
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    RED = '\033[0;31m'
    NC = '\033[0m'  # No Color
    
    @classmethod
    def disable_on_windows(cls):
        """Disable colors on Windows if not supported"""
        if os.name == 'nt' and not os.getenv('TERM'):
            cls.GREEN = cls.YELLOW = cls.RED = cls.NC = ''


def load_env_file(env_path: Path) -> dict[str, str]:
    """Load environment variables from .env file"""
    env_vars = {}
    if env_path.exists():
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip().strip('"\'')
    return env_vars


def validate_device(base_url: str, device_id: str, auth_token: str) -> bool:
    """Validate and pair device with user"""
    if not device_id:
        print(f"{Colors.RED}❌ K6_DEVICE_ID not set! Cannot pair device.{Colors.NC}")
        return False
    
    if not auth_token:
        print(f"{Colors.RED}❌ K6_AUTH_TOKEN not set! Cannot pair device.{Colors.NC}")
        return False
    
    print(f"{Colors.GREEN}🔗 Pairing device ID with user: {device_id}{Colors.NC}")
    
    # Pair the device with user by updating it with auth token
    try:
        response = requests.post(
            f"{base_url}/auth/device/{device_id}",
            json={
                "access_token": auth_token,
                "refresh_token": ""
            },
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {auth_token}"
            },
            timeout=30
        )
        
        # Save response for debugging
        with open('device_update.json', 'w') as f:
            f.write(response.text)
        
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ Device paired with user successfully!{Colors.NC}")
            return True
        else:
            print(f"{Colors.RED}❌ Failed to pair device with user (HTTP {response.status_code}){Colors.NC}")
            print(response.text)
            return False
            
    except requests.RequestException as e:
        print(f"{Colors.RED}❌ Error pairing device: {e}{Colors.NC}")
        return False


def run_k6_test(script_name: str, env_vars: dict[str, str]) -> int:
    """Run k6 test with environment variables"""
    script_path = Path("scripts") / script_name
    
    if not script_path.exists():
        print(f"{Colors.RED}❌ Script not found: {script_path}{Colors.NC}")
        print(f"Available scripts in scripts/ directory:")
        scripts_dir = Path("scripts")
        if scripts_dir.exists():
            for script in scripts_dir.glob("*.js"):
                print(f"  - {script.name}")
        return 1
    
    print(f"{Colors.GREEN}🏃 Running k6 test...{Colors.NC}")
    print()
    
    # Set up environment for k6 subprocess
    test_env = os.environ.copy()
    test_env.update(env_vars)
    
    # Run k6 command
    try:
        result = subprocess.run(
            ["k6", "run", str(script_path)],
            env=test_env,
            check=False  # Don't raise exception on non-zero exit
        )
        
        print()
        if result.returncode == 0:
            print(f"{Colors.GREEN}✅ Test completed!{Colors.NC}")
        else:
            print(f"{Colors.YELLOW}⚠️  Test completed with warnings or threshold violations{Colors.NC}")
        
        return result.returncode
        
    except FileNotFoundError:
        print(f"{Colors.RED}❌ k6 not found! Please install k6 first.{Colors.NC}")
        print("Installation instructions:")
        if os.name == 'nt':  # Windows
            print("  winget install k6")
            print("  or download from: https://k6.io/docs/get-started/installation/")
        else:  # macOS/Linux
            print("  brew install k6")
            print("  or visit: https://k6.io/docs/get-started/installation/")
        return 1
    except Exception as e:
        print(f"{Colors.RED}❌ Error running k6: {e}{Colors.NC}")
        return 1


def main():
    """Main function"""
    # Initialize colors (disable on unsupported Windows terminals)
    Colors.disable_on_windows()
    
    print(f"{Colors.GREEN}🚀 Coplay k6 Load Testing Script{Colors.NC}")
    
    # Parse command line arguments
    script_name = sys.argv[1] if len(sys.argv) > 1 else "stream_test.js"
    additional_env_vars = {}
    
    # Parse additional environment variables from command line
    for arg in sys.argv[2:]:
        if '=' in arg:
            key, value = arg.split('=', 1)
            additional_env_vars[key] = value
        else:
            print(f"{Colors.YELLOW}⚠️  Ignoring invalid argument: {arg}{Colors.NC}")
    
    # Load .env file
    env_file = Path(".env")
    if env_file.exists():
        print(f"📁 Loading environment variables from .env file...")
        env_vars = load_env_file(env_file)
    else:
        print(f"{Colors.YELLOW}💡 No .env file found. Using environment variables only.{Colors.NC}")
        print(f"{Colors.YELLOW}💡 Create a .env file from .env.example for easier configuration.{Colors.NC}")
        env_vars = {}
    
    # Override with command line arguments
    env_vars.update(additional_env_vars)
    
    # Apply environment variables to current process
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # Get configuration values
    base_url = env_vars.get('BASE_URL', 'http://localhost:8080')
    vus = env_vars.get('VUS', 'default from script')
    duration = env_vars.get('DURATION', 'default from script')
    auth_token = env_vars.get('K6_AUTH_TOKEN')
    device_id = env_vars.get('K6_DEVICE_ID')
    
    # Display configuration
    print(f"📋 Current Configuration:")
    print(f"   Script: {script_name}")
    print(f"   Base URL: {base_url}")
    print(f"   VUs: {vus}")
    print(f"   Duration: {duration}")
    print(f"   Auth Token: {'✅ Set' if auth_token else '❌ Not set'}")
    print(f"   Device ID: {device_id or '❌ Not set'}")
    
    # Validate device if we have the required info
    if auth_token and device_id:
        if not validate_device(base_url, device_id, auth_token):
            print(f"{Colors.RED}❌ Device validation failed. Exiting.{Colors.NC}")
            return 1
    else:
        print(f"{Colors.YELLOW}⚠️  Skipping device validation (missing auth token or device ID){Colors.NC}")
    
    # Run the k6 test
    return run_k6_test(script_name, env_vars)


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
