from math import floor
from pathlib import Path
from typing import Callable
import json
from functools import partial, lru_cache

from jinja2 import Template
from jsonschema import validate
from langchain_core.tools import StructuredTool
from langchain_core.messages import ToolCall, HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticToolsParser
from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_core.messages import convert_to_openai_messages
from langchain.schema import BaseMessage
from pydantic import BaseModel
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_litellm import ChatLiteLLM
from litellm import token_counter
from langsmith import traceable

from langchain_anthropic import ChatAnthropic
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI

from ..models import AssistantMode, AIModel
from ..constants import GEMINI_API_KEY, ANTHROPIC_API_KEY, OPENAI_KEY, GROK_API_KEY, OPENROUTER_API_KEY, MERCURY_CODER_API_KEY
from ..context.tools import SERVER_SIDE_TOOLS
from ..logger import logger

ANTHROPIC_WEB_SEARCH_TOOL = {
    "type": "web_search_20250305",
    "name": "web_search",
    "max_uses": 5,
}

OPENAI_WEB_SEARCH_TOOL = {
    "type": "web_search_preview"
}

def is_claude_model(model: AIModel) -> bool:
    """Check if the given model is a Claude model
    
    Args:
        model: The model to check
        
    Returns:
        True if the model is a Claude model, False otherwise
    """
    return model in [
        AIModel.CLAUDE_3_5_SONNET, 
        AIModel.CLAUDE_3_7_SONNET, 
        AIModel.CLAUDE_3_7_THINKING,
        AIModel.CLAUDE_4_SONNET,
        AIModel.CLAUDE_4_THINKING
    ]

NUM_THINKING_TOKENS = 8000 # 8k was arbitrarily chosen
TIMEOUT_SECONDS = 150 # Claude-4-thinking needs just under 90s for test nr 18. Claude-4-sonnet needs 150s for creating ui from image in normal mode in this example - https://www.reddit.com/r/aigamedev/comments/1lq4o1h/what_do_you_think_of_the_mobile_ui_of_our_ai_rpg/

class ModelConfig:
    """Class to encapsulate model configuration parameters and operations."""
    def __init__(
        self,
        llm: BaseChatModel,
        max_tokens: int,
        max_context_tokens: int,
        max_tokens_before_rag: int,
        token_counter_func: Callable,
        credit_cost: int = 1,  # Default cost of 1 credit per request
    ):
        self.llm = llm
        self.tools: list[Template] = []
        self.structured_llm = llm
        self.max_tokens = max_tokens
        self.max_context_tokens = max_context_tokens
        self.max_tokens_before_rag = max_tokens_before_rag
        self._token_counter_func = token_counter_func
        self.credit_cost = credit_cost  # Store the credit cost for this model
        self.tool_token_count = self._estimate_tool_token_count()
        self.prune_threshold = floor(max_tokens * 0.5)
    
    @traceable(run_type="chain")
    def count_tokens(self, text: str = None, messages: list[BaseMessage] = None, tools = None) -> int:
        """Count tokens in text or messages using the model's token counter"""
        return self._count_tokens(text, messages, tools)

    def _count_tokens(self, text: str = None, messages: list[BaseMessage] = None, tools = None) -> int:
        """Count tokens in text or messages using the model's token counter"""

        def count_tokens_anthropic():
            kwargs = {}
            if messages:
                # Take a copy to not modify the original messages
                messages_copy = [m.model_copy(deep=True) for m in messages]
                # Strip out image URLs as they are not supported
                for m in messages_copy:
                    if m.additional_kwargs.get("image_urls"):
                        if isinstance(m.content, list):
                            m.content = [c for c in m.content if c.get("type") != "image_url"]
                if isinstance(messages_copy[0], SystemMessage):
                    kwargs["system"] = messages_copy[0].content
                    kwargs["messages"] = messages_copy[1:]
                else:
                    kwargs["messages"] = messages_copy
            
            elif text:
                kwargs["messages"] = [HumanMessage(content=text)]

            elif tools:
                # Add dummy message to count tokens for tools
                kwargs["messages"] = [HumanMessage(content="*")]
                kwargs["tools"] = tools

            if hasattr(self.llm, 'thinking') and self.llm.thinking:
                kwargs["thinking"] = {"type": "enabled", "budget_tokens": NUM_THINKING_TOKENS}
            
            return self.llm.get_num_tokens_from_messages(**kwargs)
        
        def count_tokens_generic():
            converted_messages = convert_to_openai_messages(messages) if messages is not None else []
            # We can only pass text or messages, not both. But we have to pass at least one. Sometimes when we count tools, both can be empty in which case we pass [] for messages.
            if text is not None and converted_messages == []:
                converted_messages = None

            return self._token_counter_func(text=text, messages=converted_messages, tools=[convert_to_openai_tool(tool) for tool in tools] if tools else None, default_token_count=0)

        if isinstance(self.llm, ChatAnthropic):
            try:
                return count_tokens_anthropic()
            except Exception as e:
                # If the Anthropic token counter fails, fall back to generic counting
                logger.exception(f"Error counting tokens with Anthropic: {e}. Falling back to generic counting.")
                error_factor = 1.2 # the generic counter under-counts anthropic tokens, which could cause downstream issues. Thus we overestimate tokens here.
                return int(round(count_tokens_generic() * error_factor))
        else:
            return count_tokens_generic()
    
    def _estimate_tool_token_count(self):
        """Estimate the token count for tool templates"""
        rendered_tools = []
        tools = ModelConfig.load_tool_templates_from_schema(AssistantMode.NORMAL)
        for t in tools:
            rendered_tools.extend(json.loads(t.render()))
        
        normal_mode_token_count = self._count_tokens(tools=rendered_tools)

        rendered_tools = []
        tools = ModelConfig.load_tool_templates_from_schema(AssistantMode.STEP_BY_STEP)
        for t in tools:
            rendered_tools.extend(json.loads(t.render()))

        agent_mode_token_count = self._count_tokens(tools=rendered_tools)

        server_side_tools_token_count = self._count_tokens(tools=SERVER_SIDE_TOOLS)

        return max(normal_mode_token_count, agent_mode_token_count) + server_side_tools_token_count
    
    def bind_structured_output(self, schema: BaseModel) -> 'ModelConfig':
        """Bind structured output to the LLM and update the tool token count"""
        parser = PydanticToolsParser(
            tools=[schema], first_tool_only=True
        )
        self.llm = self.llm.bind_tools([schema], tool_choice="required", structured_output_format={"kwargs": {}, "schema": schema}) | parser
        return self

    def with_tools(self, mode: AssistantMode, tool_context: dict[str, str], server_side_tools: list[StructuredTool] = None, screenshots_enabled: bool = False) -> ChatLiteLLM:
        """Bind tools to the LLM by rendering the tool templates and attaching them to the LLM
        
        Args:
            mode: The mode of the assistant
            tool_context: A dictionary of context variables to render the tool templates with
            server_side_tools: A list of extra tools to attach to the LLM
            
        Returns:
            The LLM with the attached tools
        """
        if isinstance(self.llm, ChatOpenAI) and "mercury" in self.llm.model_name and self.llm.streaming:
            return self.llm
        
        loaded_tools = ModelConfig.load_tool_templates_from_schema(mode, screenshots_enabled)
        escaped_tool_context = {k: json.dumps(v).strip('"') for k,v in tool_context.items()}
        functions = []
        openai_api_base = getattr(self.llm, "openai_api_base", "")
        is_openrouter = openai_api_base and "openrouter.ai" in openai_api_base
        
        for t in loaded_tools:
            functions.extend(json.loads(t.render(escaped_tool_context)))

        if server_side_tools is not None and mode not in (AssistantMode.ONE_SHOT_SCENE_GEN):
            functions.extend([convert_to_openai_tool(tool) for tool in server_side_tools])

        if isinstance(self.llm, ChatAnthropic):
            functions.append(ANTHROPIC_WEB_SEARCH_TOOL)
        elif isinstance(self.llm, ChatGoogleGenerativeAI):
            for function in functions:
                function["function"]["parameters"].pop("additionalProperties", None)
                if function["function"]["parameters"]["type"] == "object" and len(function["function"]["parameters"]["properties"]) == 0:
                    function["function"].pop("parameters")
                else:
                    for k,v in function["function"]["parameters"]["properties"].items():
                        if isinstance(v.get("type"), list):
                            v["type"] = v["type"][0]
                        elif v.get("anyOf"):
                            function["function"]["parameters"]["properties"][k] = v["anyOf"][0]
                        if "default" in v:
                            v.pop("default")

        # WebSearch is not implemented yet due to this:
        # https://discord.com/channels/1305608545290686524/1306314635581849600/1395809804999725067
        # https://github.com/google/adk-python/issues/53#issuecomment-2798869949
        elif isinstance(self.llm, ChatOpenAI) and openai_api_base is None:
            if ("o4-mini" in self.llm.model_name or "o3" in self.llm.model_name):
                for function in functions:
                    if len(function["function"]["parameters"]["properties"]) == 0:
                        function["function"]["strict"] = False

                    for k,v in function["function"]["parameters"]["properties"].items():
                        if isinstance(v.get("type"), list):
                            v["type"] = v["type"][0]
                        elif v.get("anyOf"):
                            function["function"]["parameters"]["properties"][k] = v["anyOf"][0]
                        if "default" in v:
                            v.pop("default")

            # OpenAI models require a "required" field to include all parameters
            for function in functions:
                function["function"]["parameters"]["required"] = list(function["function"]["parameters"]["properties"].keys())

            functions.append(OPENAI_WEB_SEARCH_TOOL)
        elif is_openrouter and 'meta-llama' in self.llm.model_name:
            # Remove strict keyword from functions
            for function in functions:
                function["function"].pop("strict", None)
        elif isinstance(self.llm, ChatOpenAI) and "grok" in self.llm.model_name:
            for function in functions:
                function["function"]["strict"] = False

        return self.llm.bind_tools(functions)

    def validate_tool_call_json_schema(self, tool_call: ToolCall, mode: AssistantMode, screenshots_enabled: bool = True) -> bool:
        """Validate a tool call against the tool schema"""
        tools = ModelConfig.load_tool_templates_from_schema(mode, screenshots_enabled)
        tool_schemas = {tool['function']['name']: tool['function'].get('parameters', {}) for tool in tools}
        validate(tool_call['args'], tool_schemas[tool_call['name']])
        return True

    @staticmethod
    def for_gemini(model: AIModel) -> 'ModelConfig':
        """Create a ModelConfig for Gemini model"""
        if model == AIModel.GEMINI_2_5_FLASH_LITE:
            model_name = "gemini-2.5-flash-lite"
        elif model == AIModel.GEMINI_2_5_FLASH:
            model_name = "gemini-2.5-flash"
        else:  # GEMINI_2_5_PRO
            model_name = "gemini-2.5-pro"

        llm = ChatGoogleGenerativeAI(
            model=model_name,
            google_api_key=GEMINI_API_KEY,
            temperature=0,
            timeout=TIMEOUT_SECONDS
        )

        config = ModelConfig(
            llm=llm,
            max_tokens=1000000,
            max_context_tokens=800000,
            max_tokens_before_rag=500000,
            token_counter_func=partial(token_counter, model="gemini/gemini-2.0-flash")
        )

        config.prune_threshold = floor(config.max_tokens * 0.2)
        return config
    
    @staticmethod
    def for_claude(model: AIModel, user_api_key: str = None) -> 'ModelConfig':
        """Create a ModelConfig for Claude models
        
        Args:
            model: The Claude model to use
            user_api_key: Optional user-provided Anthropic API key
        """
        # Use the user's API key if provided, otherwise use the default
        api_key = user_api_key if user_api_key else ANTHROPIC_API_KEY

        if model == AIModel.CLAUDE_3_5_SONNET:
            llm = ChatAnthropic(
                model="claude-3-5-sonnet-latest",
                anthropic_api_key=api_key,
                temperature=0,
                timeout=TIMEOUT_SECONDS,
                stream_usage=True,
            )
        elif model == AIModel.CLAUDE_3_7_SONNET:
            llm = ChatAnthropic(
                model="claude-3-7-sonnet-latest",
                anthropic_api_key=api_key,
                temperature=0,
                timeout=TIMEOUT_SECONDS,
                stream_usage=True,
                # This is the max tokens that can be generated.
                # Set a reasonable max_tokens that allows for sufficient input context
                # Claude 3.7 enforces a strict limit where input tokens + max_tokens <= context window (200,000)
                max_tokens=64000, # NOTE: if for some reason, the model tries to output more than this, the answer will be empty. Hopefully the rest of our code will catch and return an error to the model.
            )
        elif model == AIModel.CLAUDE_3_7_THINKING:
            llm = ChatAnthropic(
                model="claude-3-7-sonnet-latest",
                anthropic_api_key=api_key,
                temperature=1, # Temperature has to be 1 in thinking mode.
                timeout=TIMEOUT_SECONDS,
                stream_usage=True,
                # This is the max tokens that can be generated.
                # Set a reasonable max_tokens that allows for sufficient input context
                # Claude 3.7 enforces a strict limit where input tokens + max_tokens <= context window (200,000)
                max_tokens=64000,
                thinking={"type": "enabled", "budget_tokens": NUM_THINKING_TOKENS},
            )
        elif model == AIModel.CLAUDE_4_SONNET:
            llm = ChatAnthropic(
                model="claude-sonnet-4-20250514",
                anthropic_api_key=api_key,
                temperature=0,
                timeout=TIMEOUT_SECONDS,
                stream_usage=True,
                max_tokens=64000
            )
        elif model == AIModel.CLAUDE_4_THINKING:
            llm = ChatAnthropic(
                model="claude-sonnet-4-20250514",
                anthropic_api_key=api_key,
                temperature=1, # Temperature has to be 1 in thinking mode.
                timeout=TIMEOUT_SECONDS,
                stream_usage=True,
                # This is the max tokens that can be generated.
                # Set a reasonable max_tokens that allows for sufficient input context
                # Claude 3.7 enforces a strict limit where input tokens + max_tokens <= context window (200,000)
                max_tokens=64000,
                thinking={"type": "enabled", "budget_tokens": NUM_THINKING_TOKENS}
            )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=200000,
            max_context_tokens=180000,
            max_tokens_before_rag=120000,
            token_counter_func=partial(token_counter, model=model)
        )

        config.prune_threshold = floor(config.max_tokens * 0.45)
        return config
    
    @staticmethod
    def for_gpt4o_mini() -> 'ModelConfig': # TODO: replace with 4.1-nano
        """Create a ModelConfig for GPT-4o-mini model"""
        llm = ChatOpenAI(
            model="gpt-4o-mini",
            api_key=OPENAI_KEY,
            temperature=0,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=128000,
            max_context_tokens=90000,
            max_tokens_before_rag=50000,
            token_counter_func=partial(token_counter, model="gpt-4o-mini")
        )
            
        return config
    
    @staticmethod
    def for_grok(model: AIModel) -> 'ModelConfig':
        """Create a ModelConfig for Grok models"""
        if model == AIModel.GROK4:
            model_name = "grok-4"
        elif model == AIModel.GROK_CODE_FAST_1:
            model_name = "grok-code-fast-1"
        else:
            raise ValueError(f"Invalid Grok model: {model}")

        llm = ChatOpenAI(
            model=model_name,
            base_url="https://api.x.ai/v1",
            use_responses_api=False,
            api_key=GROK_API_KEY,
            temperature=0,
            timeout=TIMEOUT_SECONDS,
            stream_options={"include_usage": True}
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=128000,
            max_context_tokens=90000,
            max_tokens_before_rag=50000,
            token_counter_func=partial(token_counter, model="grok-3")
        )
            
        return config
    
    @staticmethod
    def for_gpt4o() -> 'ModelConfig':
        """Create a ModelConfig for GPT-4o model"""
        llm = ChatOpenAI(
            model="gpt-4o",
            api_key=OPENAI_KEY,
            temperature=0,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=128000,
            max_context_tokens=90000,
            max_tokens_before_rag=50000,
            token_counter_func=partial(token_counter, model="gpt-4o")
        )
            
        return config
    
    @staticmethod
    def for_gpt41() -> 'ModelConfig':
        """Create a ModelConfig for GPT-4.1 model"""
        llm = ChatOpenAI(
            model="gpt-4.1",
            api_key=OPENAI_KEY,
            temperature=0,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS,
            output_version="responses/v1"
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=1000000,
            max_context_tokens=800000,
            max_tokens_before_rag=500000,
            token_counter_func=partial(token_counter, model="gpt-4.1")
        )
            
        return config
    
    @staticmethod
    def for_gpt5() -> 'ModelConfig':
        """Create a ModelConfig for GPT-5 model"""
        llm = ChatOpenAI(
            model="gpt-5",
            api_key=OPENAI_KEY,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS,
            reasoning={"effort": "low", "summary": "auto"},
            verbosity="low",
            output_version="responses/v1"
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=3000000,
            max_context_tokens=2000000,
            max_tokens_before_rag=1000000,
            token_counter_func=partial(token_counter, model="gpt-5")
        )
            
        return config

    @staticmethod
    def for_o3() -> 'ModelConfig':
        """Create a ModelConfig for O3 model"""
        llm = ChatOpenAI(
            model="o3",
            api_key=OPENAI_KEY,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS,
            reasoning={"effort": "low", "summary": "auto"},
            verbosity="medium",
            output_version="responses/v1"
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=200000,
            max_context_tokens=180000,
            max_tokens_before_rag=120000,
            token_counter_func=partial(token_counter, model="o3")
        )
            
        return config

    @staticmethod
    def for_o4_mini() -> 'ModelConfig':
        """Create a ModelConfig for O4-mini model"""
        llm = ChatOpenAI(
            model="o4-mini",
            api_key=OPENAI_KEY,
            stream_usage=True,
            timeout=TIMEOUT_SECONDS,
            reasoning={"effort": "low", "summary": "auto"},
            verbosity="medium",
            output_version="responses/v1"
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=200000,
            max_context_tokens=180000,
            max_tokens_before_rag=120000,
            token_counter_func=partial(token_counter, model="o4-mini")
        )
            
        return config
    
    @staticmethod
    def for_openrouter(model: str, stream: bool = True) -> 'ModelConfig':
        """Create a ModelConfig for OpenRouter models"""
        kwargs = {}
        if stream:
            kwargs = {"streaming": True, "stream_usage": True}
        
        if 'qwen3-coder' in model:
            kwargs['extra_body'] = {
                "provider": {
                    "order": ["deepinfra/fp4", "alibaba/plus"],  # Prioritize DeepInfra, then Alibaba
                    "allow_fallbacks": False,
                    "data_collection": "deny",
                    "require_parameters": True,
                }
            }

        llm = ChatOpenAI(
            model=model,
            openai_api_key=OPENROUTER_API_KEY,
            openai_api_base="https://openrouter.ai/api/v1",
            timeout=TIMEOUT_SECONDS,
            default_headers={"HTTP-Referer": "https://coplay.dev", "X-Title": "Coplay"},
            temperature=0,
            **kwargs,
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=200000,
            max_context_tokens=180000,
            max_tokens_before_rag=120000,
            token_counter_func=partial(token_counter, model="gpt-4o")
        )
            
        return config

    @staticmethod
    def for_inception(stream: bool = False) -> 'ModelConfig':
        """Create a ModelConfig for OpenRouter models"""
        if stream:
            kwargs = {"streaming": True, "stream_usage": True, "extra_body": {"diffusing": True}}
        else:
            kwargs = {}

        llm = ChatOpenAI(
            base_url="https://api.inceptionlabs.ai/v1",
            model="mercury-coder-small",
            api_key=MERCURY_CODER_API_KEY,
            timeout=TIMEOUT_SECONDS,
            **kwargs
        )
        
        config = ModelConfig(
            llm=llm,
            max_tokens=32000,
            max_context_tokens=28000,
            max_tokens_before_rag=20000,
            token_counter_func=partial(token_counter, model="")
        )

        return config
    
    @staticmethod
    def create_for_model(model: AIModel, user_api_key: str = None) -> 'ModelConfig':
        """Factory method to create the appropriate ModelConfig for a given model
        
        Args:
            model: The model to create a config for
            user_api_key: Optional user-provided API key (currently only used for Anthropic models)
        """
        if model in [AIModel.GEMINI_2_5_FLASH_LITE, AIModel.GEMINI_2_5_PRO, AIModel.GEMINI_2_5_FLASH]:
            return ModelConfig.for_gemini(model)
        elif model in [AIModel.CLAUDE_3_5_SONNET, AIModel.CLAUDE_3_7_SONNET, AIModel.CLAUDE_3_7_THINKING, AIModel.CLAUDE_4_SONNET, AIModel.CLAUDE_4_THINKING]:
            return ModelConfig.for_claude(model, user_api_key)
        elif model == AIModel.GPT4O_MINI:
            return ModelConfig.for_gpt4o_mini()
        elif model == AIModel.GROK4:
            return ModelConfig.for_grok(model)
        elif model == AIModel.GROK_CODE_FAST_1:
            return ModelConfig.for_grok(model)
        elif model == AIModel.GPT4O:
            return ModelConfig.for_gpt4o()
        elif model == AIModel.GPT41:
            return ModelConfig.for_gpt41()
        elif model == AIModel.GPT5:
            return ModelConfig.for_gpt5()
        elif model == AIModel.O3:
            return ModelConfig.for_o3()
        elif model == AIModel.O4_MINI:
            return ModelConfig.for_o4_mini()
        elif model == AIModel.MERCURY_CODER_SMALL_STREAM:
            return ModelConfig.for_inception(stream=True)
        elif model == AIModel.MERCURY_CODER_SMALL: # Streaming and toolcalls are not yet supported simultaneously. Thus we add both versions to show users the magic of diffusion with streaming.
            return ModelConfig.for_inception(stream=False)
        elif model == AIModel.KIMI_K2:
            return ModelConfig.for_openrouter("moonshotai/kimi-k2")
        elif model == AIModel.QWEN3_CODER:
            return ModelConfig.for_openrouter("qwen/qwen3-coder")
        elif model == AIModel.META_LLAMA_3_3_70B:
            return ModelConfig.for_openrouter("meta-llama/llama-3.3-70b-instruct:free")
        elif model == AIModel.Z_AI_GLM_4_5:
            return ModelConfig.for_openrouter("z-ai/glm-4.5")
        elif model == AIModel.GPT_OSS_120B:
            return ModelConfig.for_openrouter("openai/gpt-oss-120b")
        else:  # raise error for unsupported models
             raise ValueError(f"Model {model} not supported")
    
    @staticmethod
    @lru_cache
    def load_tool_templates_from_schema(assistant_mode: AssistantMode | None = None, screenshots_enabled: bool = True) -> list[Template]:
        """Load tool templates from schema files
        Other functions added automatically via server-side tools:
        - query_unity_docs
        - get_project_settings
        - get_git_history
        - get_packages
        - query_scene_or_prefab_xml_with_xpath
        """
        if assistant_mode == AssistantMode.NORMAL or assistant_mode == AssistantMode.EXPERIMENTAL:
            with open(Path(__file__).parent.parent / 'tool_schemas/unity_functions_schema.json', 'r') as file:
                functions = [Template(file.read())]
            with open(Path(__file__).parent.parent / 'tool_schemas/image_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/coplay_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/general_computer_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/replace_in_file_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/agent_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            if screenshots_enabled:
                with open(Path(__file__).parent.parent / 'tool_schemas/screenshot_tool_schema.json', 'r') as file:
                    functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/package_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/input_action_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/ui_functions_schema.json', 'r') as file:
                functions.append(Template(file.read())) # not having this in agent mode doesn't seem to make a big difference. so we could add it back to agent mode.
            with open(Path(__file__).parent.parent / 'tool_schemas/snapping_functions_schema.json', 'r') as file:
                functions.append(Template(file.read()))

            if assistant_mode == AssistantMode.EXPERIMENTAL:
                with open(Path(__file__).parent.parent / 'tool_schemas/batch_tool_schema.json', 'r') as file:
                    functions.append(Template(file.read()))
        
        elif assistant_mode == AssistantMode.STEP_BY_STEP: # We exclude input actions and packages because the model could get there with script execution and it's used infrequently.
            with open(Path(__file__).parent.parent / 'tool_schemas/unity_functions_schema.json', 'r') as file:
                functions = [Template(file.read())]
            with open(Path(__file__).parent.parent / 'tool_schemas/image_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/agent_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            if screenshots_enabled:
                with open(Path(__file__).parent.parent / 'tool_schemas/screenshot_tool_schema.json', 'r') as file:
                    functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/general_computer_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/replace_in_file_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/snapping_functions_schema.json', 'r') as file:
                functions.append(Template(file.read()))

        elif assistant_mode == AssistantMode.PIPELINE_RECORDING:
            with open(Path(__file__).parent.parent / 'tool_schemas/unity_functions_schema.json', 'r') as file:
                functions = [Template(file.read())]
            with open(Path(__file__).parent.parent / 'tool_schemas/agent_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            if screenshots_enabled:
                with open(Path(__file__).parent.parent / 'tool_schemas/screenshot_tool_schema.json', 'r') as file:
                    functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/input_action_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/ui_functions_schema.json', 'r') as file:
                functions.append(Template(file.read()))
            with open(Path(__file__).parent.parent / 'tool_schemas/batch_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))

        elif assistant_mode == AssistantMode.ORCHESTRATOR:
            with open(Path(__file__).parent.parent / 'tool_schemas/create_task_tool_schema.json', 'r') as file:
                functions = [Template(file.read())]
            with open(Path(__file__).parent.parent / 'tool_schemas/replace_in_file_tool_schema.json', 'r') as file:
                functions.append(Template(file.read()))
        
        elif assistant_mode == AssistantMode.THREAD_REVIEW:
            with open(Path(__file__).parent.parent / 'tool_schemas/replace_in_file_tool_schema.json', 'r') as file:
                functions = [Template(file.read())]
        
        return functions
    
    # List of redudant functions in schema:
    # - rename_asset, duplicate_asset -- can be done with execute_command, but wonder if we want to run an asset database refresh after it
    # - set_transform and set_property -- you can do all of it with set_property maybe. For now the model gets confused when to use which.
