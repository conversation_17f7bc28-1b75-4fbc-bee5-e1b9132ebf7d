import json
import sys
from pathlib import Path
from typing import Optional
import asyncio

# Add all necessary paths to Python path
SCENE_LANGUAGE_PATH = Path(__file__).parent.parent.parent / "third_party" / "scene-language"
SCRIPTS_PATH = SCENE_LANGUAGE_PATH / "scripts"
PROMPTS_PATH = SCRIPTS_PATH / "prompts"

# Add all required paths to Python path
paths_to_add = [
    str(SCENE_LANGUAGE_PATH),
    str(SCRIPTS_PATH),
    str(PROMPTS_PATH),
]
for path in paths_to_add:
    if path not in sys.path:
        sys.path.append(path)

# Import the necessary functions from the other scripts
from run_unconstrained import run_unconstrained
from serialize_scene import serialize_scene
from dsl_utils import library

from ..models import UserAttachedModels


async def generate_scene(
    scene_description: str, 
    depth: int = 5, 
    status_callback = None, 
    attached_fbx_files: list[UserAttachedModels] = None,
    model: str = "claude-3-7-sonnet"
) -> str:
    """
    Generate and serialize a scene based on the scene description.
    
    Args:
        description: The scene description
        depth: The depth of scene generation
        status_callback: Callback function for status updates
        attached_fbx_files: List of relative paths to FBX files in the context
        
    Returns:
        Path to the generated scene JSON file
    """
    # TODO: implement a better system for managing generated content on a per user basis
    # Clear the library before each generation for improved results
    library.clear()
    # Ensure the output directory exists
    output_dir = Path("generated_scenes")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Step 1: Generate the scene using run_unconstrained.py in thread pool
    loop = asyncio.get_event_loop()
    last_depth_dir = await loop.run_in_executor(
        None,  # Use default executor
        lambda: run_unconstrained(
            scene_description, 
            output_dir, 
            depth,
            status_callback,
            attached_fbx_files,
            model=model
        )
    )

    print(f"last_depth_dir after run_unconstrained: {last_depth_dir}")

    if status_callback:
        status_callback(depth, depth, "Serializing scene")

    # Step 2: Serialize the generated scene in main async context
    scene_file_program_path = Path(last_depth_dir) / "0" / "program.py"
    with open(scene_file_program_path, "r") as f:
        scene_file_program_content = f.read()
    for file in attached_fbx_files:
        scene_file_program_content = scene_file_program_content.replace(file.path, file.local_ply_path)
    with open(scene_file_program_path, "w") as f:
        f.write(scene_file_program_content)
        
    scene_json_path = serialize_scene(scene_file_program_path)
    
    if not scene_json_path.exists():
        raise FileNotFoundError(f"Scene generation failed - no output file at {scene_json_path}")
    
    with open(scene_json_path, "r") as f:   
        scene_json_content = f.read()
    for file in attached_fbx_files:
        scene_json_content = scene_json_content.replace(file.local_ply_path, f"copy_of_user_assets/{file.path}")
    with open(scene_json_path, "w") as f:
        f.write(scene_json_content)
        
    return str(scene_json_path)

if __name__ == "__main__":
    # Example usage
    test_description = "A living room with a couch and coffee table"
    loop = asyncio.get_event_loop()
    output_path = loop.run_until_complete(generate_scene(test_description))
    print(f"Generated scene saved to: {output_path}")
