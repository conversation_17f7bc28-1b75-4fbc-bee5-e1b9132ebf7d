using Newtonsoft.Json;

namespace Coplay.Common.CoplayApi.Models
{
    public class ImageFunctionsResponse
    {
        [JsonProperty("type")]
        public string Type { get; set; } // "partial_image", "final_image", "error"

        [JsonProperty("task_id")]
        public string TaskId { get; set; }

        [JsonProperty("finished")]
        public bool Finished { get; set; } = true;

        [JsonProperty("count")]
        public int? PartialCount { get; set; }

        [JsonProperty("total")]
        public int? TotalCount { get; set; }

        [JsonProperty("image_data")]
        public string ImageData { get; set; } // Base64 data URL

        [JsonProperty("result")]
        public string Result { get; set; }

        [JsonProperty("error")]
        public string Error { get; set; }

        [JsonProperty("chunk_id")]
        public int ChunkId { get; set; } = 0; // Integer counter that increments with each update

        [JsonProperty("image_url")]
        public string ImageUrl { get; set; }

        [JsonProperty("width")]
        public int? Width { get; set; }

        [JsonProperty("height")]
        public int? Height { get; set; }
    }
}
