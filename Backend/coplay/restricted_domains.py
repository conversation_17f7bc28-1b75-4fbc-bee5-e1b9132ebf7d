"""
Configuration for email domains that should not receive free trial credits.
"""

# List of email domains that should not receive free trial credits
# Users with these domains must pay before using Coplay
RESTRICTED_EMAIL_DOMAINS = [
    "unity3d.com",
    "bytedance.com",
    "miao.company"
]

def is_email_domain_restricted(email: str) -> bool:
    """
    Check if an email domain is restricted from receiving free trial credits.
    
    Args:
        email: The user's email address
        
    Returns:
        True if the email domain is restricted, False otherwise
    """
    if not email or '@' not in email:
        return False
    
    domain = email.split('@')[1].lower()
    return domain in [d.lower() for d in RESTRICTED_EMAIL_DOMAINS]
