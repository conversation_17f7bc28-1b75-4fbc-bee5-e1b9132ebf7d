from datetime import datetime, timezone
import os
from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON>

from .client import ensure_dataset_and_table, insert_rows_to_table
from ..models import <PERSON>play<PERSON>un
from ..logger import get_coplay_env, logger


# Schema for usage data table
# NOTE: if you edit this table, you have to make the same edit in the Bigquery schema in the google console.
# NOTE: when updating this table, update the integration test as needed.
USAGE_DATA_SCHEMA = [
    Schema<PERSON>ield("timestamp", "TIMESTAMP", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("input_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("output_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("total_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON>hem<PERSON><PERSON>ield("cache_read_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("cache_write_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("non_cache_tokens", "INTEGER", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("payable_tokens", "INTEGER", mode="REQUIRED"),
    SchemaField("cost_usd", "NUMERIC", mode="REQUIRED"),
    SchemaField("message_id", "STRING", mode="REQUIRED"),
    SchemaField("model", "STRING", mode="REQUIRED"),
    SchemaField("thread_id", "STRING", mode="REQUIRED"),
    SchemaField("user_id", "STRING", mode="REQUIRED"),
    SchemaField("prompt", "STRING", mode="REQUIRED"),
    SchemaField("tool_calls", "INTEGER", mode="REQUIRED"),
    SchemaField("tool_outputs", "INTEGER", mode="REQUIRED"),
    SchemaField("chargeable", "BOOLEAN", mode="REQUIRED"),
    SchemaField("credit_balance", "NUMERIC", mode="REQUIRED"),
    SchemaField("coplay_version", "STRING", mode="REQUIRED"),
]

# Constants for usage data table
_DATASET_ID = "coplay_analytics"
_TABLE_ID = "usage_data"

async def insert_run_data(run: CoplayRun) -> bool:
    """Insert usage data into BigQuery"""
    try:
        # Ensure dataset and table exist
        await ensure_dataset_and_table(_DATASET_ID, _TABLE_ID, USAGE_DATA_SCHEMA)
        
        # Convert usage data to BigQuery row format
        row = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "input_tokens": run.usage.input_tokens,
            "output_tokens": run.usage.output_tokens,
            "total_tokens": run.usage.total_tokens,
            "cache_read_tokens": run.usage.cache_read_tokens,
            "cache_write_tokens": run.usage.cache_write_tokens,
            "non_cache_tokens": run.usage.non_cache_tokens,
            "payable_tokens": run.usage.payable_tokens,
            "cost_usd": f"{run.usage.cost_usd:.6f}",
            "message_id": run.message_id,
            "model": run.model.value,
            "thread_id": run.thread_id,
            "user_id": run.user_id,
            "prompt": run.prompt or "",
            "tool_calls": run.tool_calls,
            "tool_outputs": run.tool_outputs,
            "chargeable": run.chargeable,
            "credit_balance": f"{float(run.usage.credit_balance):.6f}",
            "coplay_env": get_coplay_env(),
            "coplay_version": run.coplay_version,
        }
        
        # Insert the row using generic function
        return await insert_rows_to_table(_DATASET_ID, _TABLE_ID, [row])
        
    except Exception as e:
        logger.error(f"Error inserting usage data to BigQuery: {e}")
        return False
