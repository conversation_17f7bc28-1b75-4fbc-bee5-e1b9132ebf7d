import { useState, useCallback } from "react";

/**
 * Custom hook for simple email input management
 */
export function useEmailInput(initialValue: string = "") {
  const [email, setEmail] = useState(initialValue);

  const resetEmail = useCallback(() => {
    setEmail(initialValue);
  }, [initialValue]);

  const clearEmail = useCallback(() => {
    setEmail("");
  }, []);

  return {
    email,
    setEmail,
    resetEmail,
    clearEmail,
  };
}

/**
 * Custom hook for managing form submission status
 */
export function useSubmissionStatus() {
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle");

  const setLoading = useCallback(() => setStatus("loading"), []);
  const setSuccess = useCallback(() => setStatus("success"), []);
  const setError = useCallback(() => setStatus("error"), []);
  const resetStatus = useCallback(() => setStatus("idle"), []);

  return {
    status,
    setLoading,
    setSuccess,
    setError,
    resetStatus,
    isLoading: status === "loading",
    isSuccess: status === "success",
    isError: status === "error",
    isIdle: status === "idle",
  };
}

/**
 * Generic async operation handler with status management
 */
export function useAsyncOperation<
  TArgs extends readonly unknown[] = readonly unknown[],
  TResult = unknown
>(
  operation: (...args: TArgs) => Promise<TResult>,
  onSuccess?: (result: TResult) => void,
  onError?: (error: Error) => void
) {
  const { status, setLoading, setSuccess, setError, resetStatus, isLoading } = useSubmissionStatus();

  const execute = useCallback(
    async (...args: TArgs) => {
      setLoading();
      try {
        const result = await operation(...args);
        setSuccess();
        onSuccess?.(result);
        return result;
      } catch (error) {
        setError();
        const errorInstance = error instanceof Error ? error : new Error(String(error));
        onError?.(errorInstance);
        throw errorInstance;
      }
    },
    [operation, setLoading, setSuccess, setError, onSuccess, onError]
  );

  return {
    execute,
    status,
    isLoading,
    resetStatus,
  };
}

/**
 * Generic hook for handling form submissions with toast notifications
 */
export function useFormSubmit<
  TArgs extends readonly unknown[] = readonly unknown[],
  TResult = unknown
>(
  operation: (...args: TArgs) => Promise<TResult>,
  successMessage: string = "Operation completed successfully!",
  errorMessagePrefix: string = "Failed to complete operation"
) {
  const { execute, isLoading } = useAsyncOperation(
    operation
  );

  return {
    handleSubmit: execute,
    isLoading,
  };
}
