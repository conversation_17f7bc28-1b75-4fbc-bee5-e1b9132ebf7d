using System;
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Coplay.Common;
using Coplay.Controllers.Assistants;
using Coplay.Controllers.Functions.Implementations;
using Coplay.Controllers.Systems;
using Coplay.Models.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Coplay.DependencyInjection;
using Coplay.Services.Chat;
using System.Threading;
using System.Threading.Tasks;

namespace Coplay.Tests.Benchmark
{
    [Ignore("For local testing")]
    [TestFixture]
    public class CoplayBenchmarkTests
    {
        public const int MaxExercises = 10;

        private BenchmarkExerciseManager _exerciseManager;
        private CoplayBenchmarkRunner _benchmarkRunner;
        private BenchmarkReporter _reporter;
        private AssistantController _assistantController;
        private FileFunctions _fileFunctions;
        private StateController _stateController;
        
        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            CoplayLogger.Log("Setting up Coplay Benchmark Tests");
            
            // Initialize services - this would typically be done through DI
            _exerciseManager = new BenchmarkExerciseManager();
            _reporter = new BenchmarkReporter();
            
            // Get services from DI container if available
            try
            {
                var serviceProvider = CoplayScopeServices.Services;
                if (serviceProvider != null)
                {
                    _assistantController = serviceProvider.GetService<AssistantController>();
                    _fileFunctions = serviceProvider.GetService<FileFunctions>();
                    _stateController = serviceProvider.GetService<StateController>();
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogWarning($"Could not get services from DI container: {ex.Message}");
            }
            
            // Create mock services if DI is not available
            if (_assistantController == null || _fileFunctions == null)
            {
                CoplayLogger.LogWarning("Using mock services for benchmark tests");
                // In a real scenario, you'd want to set up proper mock services here
            }
            
            // Get IChatThreads, CoplayStateModel, and AssistantConfiguration services from DI container
            IChatThreads chatThreads = null;
            CoplayStateModel stateModel = null;
            AssistantConfiguration assistantConfiguration = null;
            try
            {
                var serviceProvider = CoplayScopeServices.Services;
                if (serviceProvider != null)
                {
                    chatThreads = serviceProvider.GetService<IChatThreads>();
                    stateModel = serviceProvider.GetService<CoplayStateModel>();
                    assistantConfiguration = serviceProvider.GetService<AssistantConfiguration>();
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogWarning($"Could not get services from DI container: {ex.Message}");
            }
            
            _benchmarkRunner = new CoplayBenchmarkRunner(_exerciseManager, _assistantController, _fileFunctions, chatThreads, stateModel, assistantConfiguration);
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("LongRunning")]
        public IEnumerator TestSetupPolyglotBenchmark()
        {
            CoplayLogger.Log("Testing polyglot benchmark setup...");
            
            var setupTask = _exerciseManager.EnsurePolyglotBenchmarkExists();
            
            // Wait for the async operation to complete
            while (!setupTask.IsCompleted)
            {
                yield return null;
            }
            
            if (setupTask.IsFaulted)
            {
                Assert.Fail($"Failed to setup polyglot benchmark: {setupTask.Exception?.GetBaseException().Message}");
            }
            
            Assert.IsTrue(setupTask.Result, "Polyglot benchmark setup should succeed");
            
            // Test loading exercises
            var exercises = _exerciseManager.LoadExercises();
            Assert.IsNotNull(exercises, "Exercises should be loaded");
            Assert.Greater(exercises.Count, 0, "Should have at least some exercises");
            
            CoplayLogger.Log($"Successfully loaded {exercises.Count} exercises");
            
            // Test exercises for each supported language
            var pythonExercises = exercises.FindAll(e => e.Language == "python");
            var jsExercises = exercises.FindAll(e => e.Language == "javascript");
            var rustExercises = exercises.FindAll(e => e.Language == "rust");
            
            CoplayLogger.Log($"Python exercises: {pythonExercises.Count}");
            CoplayLogger.Log($"JavaScript exercises: {jsExercises.Count}");
            CoplayLogger.Log($"Rust exercises: {rustExercises.Count}");
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("LongRunning")]
        public IEnumerator TestSingleExerciseBenchmark()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping single exercise test");
                yield break;
            }
            
            CoplayLogger.Log("Testing single exercise benchmark...");
            
            // Setup polyglot benchmark first
            var setupTask = _exerciseManager.EnsurePolyglotBenchmarkExists();
            while (!setupTask.IsCompleted)
            {
                yield return null;
            }
            
            if (!setupTask.Result)
            {
                Assert.Fail("Failed to setup polyglot benchmark");
            }
            
            // Load exercises and pick the first Python exercise
            var exercises = _exerciseManager.LoadExercises();
            var pythonExercise = exercises.Find(e => e.Language == "python");
            
            if (pythonExercise == null)
            {
                Assert.Ignore("No Python exercises found - skipping test");
                yield break;
            }
            
            CoplayLogger.Log($"Testing with exercise: {pythonExercise}");

            using var cts = new CancellationTokenSource();
            using var streamCancellation = cts.Token.Register(() => _stateController.CancelCurrentRequest());
            
            // Run a limited benchmark with just one exercise
            var benchmarkTask = _benchmarkRunner.RunFullBenchmark(
                model: "claude-4-sonnet", 
                maxExercises: 1, 
                specificLanguages: new List<string> { "python" },
                cancellationToken: cts.Token
            );
            
            // Wait for benchmark to complete (with timeout)
            var timeoutSeconds = 300; // 5 minutes timeout
            var startTime = Time.realtimeSinceStartup;

            while (!benchmarkTask.IsCompleted && (Time.realtimeSinceStartup - startTime) < timeoutSeconds)
            {
                var shouldCancel = true;
                Task.Run(async () =>
                {
                    await Task.Delay(1000);
                    if (shouldCancel)
                    {
                        CoplayLogger.Log("Test has been stopped, cancelling tasks");
                        cts.Cancel();
                    }
                });
                yield return null;
                shouldCancel = false;
            }

            if (!benchmarkTask.IsCompleted)
            {
                Assert.Fail("Benchmark timed out after 5 minutes");
            }

            if (benchmarkTask.IsFaulted)
            {
                Assert.Fail($"Benchmark failed: {benchmarkTask.Exception?.GetBaseException().Message}");
            }
            
            var session = benchmarkTask.Result;
            Assert.IsNotNull(session, "Benchmark session should not be null");
            Assert.Greater(session.CompletedExercises, 0, "Should have completed at least one exercise");
            
            CoplayLogger.Log($"Benchmark completed: {session.CompletedExercises} exercises, Success rate: {session.SuccessRate:P2}");
            
            // Save the session report
            var saveTask = _reporter.SaveSessionReport(session);
            while (!saveTask.IsCompleted)
            {
                yield return null;
            }
        }
        
        [Test]
        [Category("Benchmark")]
        public void TestLanguageTestRunners()
        {
            CoplayLogger.Log("Testing language test runners availability...");
            
            var testRunners = new Dictionary<string, LanguageTestRunners.LanguageTestRunner>
            {
                { "python", new LanguageTestRunners.PythonTestRunner() },
                { "javascript", new LanguageTestRunners.JavaScriptTestRunner() },
                { "rust", new LanguageTestRunners.RustTestRunner() }
            };
            
            foreach (var kvp in testRunners)
            {
                var language = kvp.Key;
                var runner = kvp.Value;
                
                // Test language availability (this is async, but we'll just check the runner exists)
                Assert.IsNotNull(runner, $"{language} test runner should not be null");
                Assert.AreEqual(language, runner.Language, $"Runner language should match {language}");
                
                CoplayLogger.Log($"✓ {language} test runner initialized");
            }
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("FullBenchmark")]
        [Category("LongRunning")]
        public IEnumerator RunFullBenchmarkGPT4()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping full benchmark");
                yield break;
            }
            
            CoplayLogger.Log("Starting full benchmark for GPT-4.1...");
            
            yield return RunFullBenchmarkForModel("gpt-4.1");
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("FullBenchmark")]
        [Category("LongRunning")]
        public IEnumerator RunFullBenchmarkClaude()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping full benchmark");
                yield break;
            }
            
            CoplayLogger.Log("Starting full benchmark for Claude 4 Sonnet...");
            
            yield return RunFullBenchmarkForModel("claude-4-sonnet");
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("FullBenchmark")]
        [Category("LongRunning")]
        public IEnumerator RunFullBenchmarkGPT5()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping full benchmark");
                yield break;
            }
            
            CoplayLogger.Log("Starting full benchmark for GPT-5...");
            
            yield return RunFullBenchmarkForModel("gpt-5");
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("FullBenchmark")]
        [Category("LongRunning")]
        public IEnumerator RunFullBenchmarkGemini25Flash()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping full benchmark");
                yield break;
            }
            
            CoplayLogger.Log("Starting full benchmark for Gemini 2.5 Flash...");
            
            yield return RunFullBenchmarkForModel("gemini-2-5-flash");
        }
        
        [UnityTest]
        [Category("Benchmark")]
        [Category("FullBenchmark")]
        [Category("LongRunning")]
        public IEnumerator RunFullBenchmarkGemini25Pro()
        {
            if (_assistantController == null)
            {
                Assert.Ignore("AssistantController not available - skipping full benchmark");
                yield break;
            }
            
            CoplayLogger.Log("Starting full benchmark for Gemini 2.5 Pro...");
            
            yield return RunFullBenchmarkForModel("gemini-2-5-pro");
        }
        
        private IEnumerator RunFullBenchmarkForModel(string model)
        {
            // Setup polyglot benchmark
            var setupTask = _exerciseManager.EnsurePolyglotBenchmarkExists();
            while (!setupTask.IsCompleted)
            {
                yield return null;
            }
            
            if (!setupTask.Result)
            {
                Assert.Fail("Failed to setup polyglot benchmark");
            }
            
            // Create cancellation token source for benchmark control
            using var cancellationTokenSource = new CancellationTokenSource();
            using var streamCancellation = cancellationTokenSource.Token.Register(() => _stateController.CancelCurrentRequest());
            
            // Run benchmark with a reasonable limit for testing
            var benchmarkTask = _benchmarkRunner.RunFullBenchmark(
                model: model,
                maxExercises: MaxExercises, // Limit number of exercises for testing
                specificLanguages: null,
                cancellationToken: cancellationTokenSource.Token
            );
            
            // Wait for benchmark to complete (with extended timeout for full benchmark)
            var timeoutSeconds = 1800; // 30 minutes timeout
            var startTime = Time.realtimeSinceStartup;
            var lastReportTime = startTime;
            
            while (!benchmarkTask.IsCompleted && (Time.realtimeSinceStartup - startTime) < timeoutSeconds)
            {
                var shouldCancel = true;
                Task.Run(async () =>
                {
                    await Task.Delay(1000);
                    if (shouldCancel)
                    {
                        CoplayLogger.Log("Test has been stopped, cancelling tasks");
                        cancellationTokenSource.Cancel();
                    }
                });
                yield return null;
                shouldCancel = false;

                // Log progress periodically
                if (Time.realtimeSinceStartup - lastReportTime >= 60) // Every minute
                {
                    lastReportTime = Time.realtimeSinceStartup;
                    CoplayLogger.Log($"Benchmark still running... ({Time.realtimeSinceStartup - startTime:F0}s elapsed)");
                }
            }
            
            if (!benchmarkTask.IsCompleted)
            {
                if (cancellationTokenSource.Token.IsCancellationRequested)
                {
                    CoplayLogger.Log("Benchmark was cancelled by user request");
                    yield break; // Exit gracefully on cancellation
                }
                else
                {
                    Assert.Fail($"Benchmark timed out after {timeoutSeconds / 60} minutes");
                }
            }
            
            if (benchmarkTask.IsCanceled)
            {
                CoplayLogger.Log("Benchmark was cancelled");
                yield break; // Exit gracefully on cancellation
            }
            
            if (benchmarkTask.IsFaulted)
            {
                Assert.Fail($"Benchmark failed: {benchmarkTask.Exception?.GetBaseException().Message}");
            }
            
            var session = benchmarkTask.Result;
            Assert.IsNotNull(session, "Benchmark session should not be null");
            
            CoplayLogger.Log($"Full benchmark completed for {model}:");
            CoplayLogger.Log($"  - Exercises: {session.CompletedExercises}/{session.TotalExercises}");
            CoplayLogger.Log($"  - Success rate: {session.SuccessRate:P2}");
            CoplayLogger.Log($"  - Total cost: ${session.TotalCost:F4}");
            CoplayLogger.Log($"  - Duration: {session.TotalDuration:F1} seconds");
            
            // Save the session report
            var saveTask = _reporter.SaveSessionReport(session);
            while (!saveTask.IsCompleted)
            {
                yield return null;
            }
            
            // Generate comparison report if we have multiple sessions
            var allSessions = _reporter.LoadAllSessions();
            if (allSessions.Count > 1)
            {
                var reportTask = _reporter.GenerateComparisonReport(allSessions);
                while (!reportTask.IsCompleted)
                {
                    yield return null;
                }
            }
        }
        
        [Test]
        [Category("Benchmark")]
        public void TestReportGeneration()
        {
            CoplayLogger.Log("Testing report generation...");
            
            // Create a mock session for testing
            var mockSession = new BenchmarkSession
            {
                Model = "test-model",
                TotalExercises = 5,
                CompletedExercises = 3,
                TotalCost = 0.1234,
                TotalDuration = 45.6
            };
            
            // Add some mock results
            mockSession.Results.Add(new BenchmarkResult
            {
                ExerciseName = "test-exercise-1",
                Language = "python",
                Model = "test-model",
                Success = true,
                Duration = 15.2
            });
            
            mockSession.Results.Add(new BenchmarkResult
            {
                ExerciseName = "test-exercise-2",
                Language = "javascript",
                Model = "test-model",
                Success = false,
                Duration = 30.4,
                ErrorMessage = "Test error"
            });
            
            mockSession.Complete();
            
            // Test session properties
            Assert.AreEqual(0.6, mockSession.SuccessRate, 0.01, "Success rate should be 60%");
            Assert.IsTrue(mockSession.EndTime > mockSession.StartTime, "End time should be after start time");
            
            CoplayLogger.Log("✓ Report generation test completed");
        }
    }
}
