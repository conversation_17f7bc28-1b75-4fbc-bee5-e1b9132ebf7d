export function formatDate(dateString?: string): string {
  if (!dateString) return "N/A";

  // Normalize the date string for consistent parsing
  let normalizedDateString = dateString.trim();
  let year: number, month: number, day: number;

  // Handle different date formats explicitly
  const dateFormats = [
    // MM/DD/YYYY format
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // YYYY/MM/DD format  
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
    // MM-DD-YYYY format
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
    // YYYY-MM-DD format
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/
  ];

  let dateParseSuccess = false;

  // Try to parse common slash/dash separated formats
  for (const format of dateFormats) {
    const match = normalizedDateString.match(format);
    if (match) {
      if (format.source.startsWith('^(\\d{4})')) {
        // YYYY/MM/DD or YYYY-MM-DD format
        const [, yearStr, monthStr, dayStr] = match;
        year = parseInt(yearStr, 10);
        month = parseInt(monthStr, 10);
        day = parseInt(dayStr, 10);
      } else {
        // MM/DD/YYYY or MM-DD-YYYY format  
        const [, monthStr, dayStr, yearStr] = match;
        year = parseInt(yearStr, 10);
        month = parseInt(monthStr, 10);
        day = parseInt(dayStr, 10);
      }

      // Validate date components
      if (month < 1 || month > 12 || day < 1 || day > 31 || year < 1000 || year > 9999) {
        return "Invalid Date";
      }

      // Additional validation for days in month
      const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
      if (month === 2 && ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0)) {
        daysInMonth[1] = 29; // Leap year
      }
      if (day > daysInMonth[month - 1]) {
        return "Invalid Date";
      }

      dateParseSuccess = true;
      break;
    }
  }

  let date: Date;

  if (dateParseSuccess) {
    // Create date object using local time to avoid timezone issues
    // Note: month is 0-indexed in Date constructor
    date = new Date(year!, month! - 1, day!);
  } else {
    // For other formats (like ISO strings, already formatted dates), use direct parsing
    date = new Date(normalizedDateString);
  }

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }

  // Format the date consistently
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}
