using Newtonsoft.Json;

namespace Coplay.Services.Scene.Models
{
    /// <summary>
    /// Request model for listing game objects in the hierarchy with optional filtering capabilities
    /// </summary>
    public class QuerySceneRequest
    {
        private bool? _includeInactive = false;
        private int? _limit = 200;
        private int? _skip = 0;
        private bool? _onlyPaths = true;

        /// <summary>
        /// Gets or sets the path of a reference game object to search relative to
        /// If specified, the search will be performed relative to this object instead of the entire hierarchy
        /// </summary>
        [Json<PERSON>roperty("referenceObjectPath")]
        public string ReferenceObjectPath { get; set; }

        /// <summary>
        /// Gets or sets a filter for game object names (case-insensitive substring match)
        /// </summary>
        [JsonProperty("nameFilter")]
        public string NameFilter { get; set; }

        /// <summary>
        /// Gets or sets a filter for game object tags
        /// </summary>
        [JsonProperty("tagFilter")]
        public string TagFilter { get; set; }

        /// <summary>
        /// Gets or sets a filter for component types (e.g. "Transform", "MeshRenderer")
        /// </summary>
        [Json<PERSON>roperty("componentFilter")]
        public string ComponentFilter { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to include inactive game objects
        /// Can be null in the JSON, but will default to false when accessed
        /// </summary>
        [JsonProperty("includeInactive")]
        public bool? IncludeInactive 
        { 
            get => _includeInactive; 
            set => _includeInactive = value; 
        }

        /// <summary>
        /// Gets the actual boolean value for IncludeInactive, defaulting to false if null
        /// </summary>
        public bool GetIncludeInactive() => IncludeInactive ?? false;

        /// <summary>
        /// Gets or sets the maximum number of objects to return
        /// Can be null in the JSON, but will default to 200 when accessed
        /// </summary>
        [JsonProperty("limit")]
        public int? Limit 
        { 
            get => _limit; 
            set => _limit = value; 
        }

        /// <summary>
        /// Gets the actual integer value for Limit, defaulting to 200 if null
        /// </summary>
        public int GetLimit() => Limit ?? 200;

        /// <summary>
        /// Gets or sets the number of objects to skip (for pagination)
        /// Can be null in the JSON, but will default to 0 when accessed
        /// </summary>
        [JsonProperty("skip")]
        public int? Skip 
        { 
            get => _skip; 
            set => _skip = value; 
        }

        /// <summary>
        /// Gets the actual integer value for Skip, defaulting to 0 if null
        /// </summary>
        public int GetSkip() => Skip ?? 0;

        /// <summary>
        /// Gets or sets a flag indicating whether to return only the paths of the game objects
        /// Can be null in the JSON, but will default to true when accessed
        /// </summary>
        [JsonProperty("onlyPaths")]
        public bool? OnlyPaths 
        { 
            get => _onlyPaths; 
            set => _onlyPaths = value; 
        }

        /// <summary>
        /// Gets the actual boolean value for OnlyPaths, defaulting to true if null
        /// </summary>
        public bool GetOnlyPaths() => OnlyPaths ?? true;
    }
}
