using System;
using System.Collections.Generic;
using System.Linq;
using Coplay.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.IO.Abstractions;
using System.Reactive.Subjects;
using System.Reactive;
using System.Reactive.Linq;
using Coplay.Common;

namespace Coplay.Services.Chat
{
    public class ChatHistoryRepository : IDisposable
    {
        private static readonly JsonSerializerSettings JsonSerializerSettings = new()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };
        private readonly IFileSystem _fileSystem;
        private readonly string _filename;
        private readonly List<ChatMessage> _messages;
        public IReadOnlyList<ChatMessage> Messages => _messages.AsReadOnly();
        public int MessageCount => _messages.Count;

        private readonly Subject<ChatMessage> _messageAddedSubject = new();
        public IObservable<ChatMessage> MessageAdded => _messageAddedSubject.AsObservable();

        private readonly Subject<ChatMessage> _messageUpdatedSubject = new();
        public IObservable<ChatMessage> MessageUpdated => _messageUpdatedSubject.AsObservable();

        private readonly Subject<Unit> _clearedSubject = new Subject<Unit>();
        public IObservable<Unit> Cleared => _clearedSubject.AsObservable();

        public ChatHistoryRepository(IApplicationPaths applicationPaths, IFileSystem fileSystem) : this(applicationPaths.MessageHistoryPath, fileSystem)
        {
        }

        private ChatHistoryRepository(string filename, IFileSystem fileSystem)
        {
            _filename = filename;
            _fileSystem = fileSystem;
            var history = _fileSystem.File.Exists(filename) ? LoadFromFile(filename) : new ChatHistory();
            _messages = history.Messages;
        }

        public void Dispose()
        {
            _messageAddedSubject.Dispose();
            _messageUpdatedSubject.Dispose();
            _clearedSubject.Dispose();

            Save();
        }

        public void Save()
        {
            try
            {
                _fileSystem.File.WriteAllText(_filename, JsonConvert.SerializeObject(new ChatHistory(_messages), JsonSerializerSettings));
            }
            catch (Exception e)
            {
                CoplayLogger.LogError("Failed to save chat history: " + e.Message);
            }
        }

        // Add a new message and return its ID
        public ChatMessage AddMessage(string content, ChatMessageRole chatMessageRole = ChatMessageRole.Coplay, List<FunctionCall> functionCalls = null, bool notify = true)
        {
            var message = new ChatMessage(content, chatMessageRole, functionCalls: functionCalls);
            return AddMessage(message, notify);
        }

        public ChatMessage AddMessage(ChatMessage message, bool notify = true) // TODO: should probably refactor this to be private when we can stop the UIassistant from calling it
        {
            _messages.Add(message);
            if (notify)
            {
                _messageAddedSubject.OnNext(message);
            }
            return message;
        }

        public void AddMessages(List<ChatMessage> messages, bool notify = true)
        {
            foreach (var message in messages)
            {
                AddMessage(message, notify);
            }
        }

        public void UpdateMessage(string messageId, string content = null, List<FunctionCall> functionCalls = null, bool replace = false, float? costUsd = null)
        {
            var message = _messages.FirstOrDefault(m => m.Id == messageId);

            if (message != null)
            {
                if (content != null)
                {
                    if (string.IsNullOrEmpty(message.Content) || replace)
                    {
                        message.Content = content;
                    }
                    else
                    {
                        message.Content += $"\n{content}";
                    }
                }
                if (functionCalls != null)
                {
                    foreach (var functionCall in functionCalls)
                    {
                        var index = message.FunctionCalls.FindIndex(f => f.ToolCallId == functionCall.ToolCallId);
                        if (index != -1)
                        {
                            var existingFunction = message.FunctionCalls[index];
                            if (existingFunction.State == FunctionState.Executed)
                            {
                                functionCall.State = existingFunction.State;
                                functionCall.HasExecuted = true;
                            }
                            else if (existingFunction.State == FunctionState.Cancelled)
                            {
                                functionCall.State = FunctionState.Cancelled;
                                functionCall.IsCancelled = true;
                            }

                            if (existingFunction.PendingExecution)
                                functionCall.PendingExecution = true;

                            message.FunctionCalls[index] = functionCall;
                        }
                        else
                        {
                            message.FunctionCalls.Add(functionCall);
                        }
                    }
                }

                if (costUsd.HasValue)
                {
                    message.CostUsd = costUsd;
                }

                _messageUpdatedSubject.OnNext(message);
            }
            else
            {
                AddMessage(new ChatMessage(content, id: messageId, functionCalls: functionCalls, costUsd: costUsd));
            }
        }

        public void AddActionResultToMessage(string messageId, string actionResult)
        {
            var message = _messages.FirstOrDefault(m => m.Id == messageId);
            if (message != null)
            {
                message.ActionResultToShowInFoldout = actionResult;
                _messageUpdatedSubject.OnNext(message);
            }
        }

        // Clear all messages
        public void Clear()
        {
            _messages.Clear();
            _clearedSubject.OnNext(Unit.Default);
        }

        private ChatHistory LoadFromFile(string filename)
        {
            try
            {
                if (_fileSystem.File.Exists(filename))
                {
                    string json = _fileSystem.File.ReadAllText(filename);
                    if (!string.IsNullOrEmpty(json))
                    {
                        return JsonConvert.DeserializeObject<ChatHistory>(json, JsonSerializerSettings);
                    }
                }
            }
            catch (Exception e)
            {
                CoplayLogger.LogError("Failed to load chat history: " + e.Message);
            }

            // Return a new instance if loading fails or file doesn't exist
            return new ChatHistory();
        }

        public void NotifyMessageUpdated(ChatMessage message)
        {
            _messageUpdatedSubject.OnNext(message);
        }
    }
}
