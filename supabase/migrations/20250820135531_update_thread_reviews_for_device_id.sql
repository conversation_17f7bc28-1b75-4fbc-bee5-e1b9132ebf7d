-- Update thread_reviews table to store by device_id instead of thread relationships
-- Drop existing foreign key constraints and indexes
DROP INDEX IF EXISTS idx_thread_reviews_new_thread_id;
DROP INDEX IF EXISTS idx_thread_reviews_status;
DROP INDEX IF EXISTS idx_thread_reviews_created_at;

-- Drop foreign key constraints
ALTER TABLE thread_reviews DROP CONSTRAINT IF EXISTS fk_previous_thread;
ALTER TABLE thread_reviews DROP CONSTRAINT IF EXISTS fk_new_thread;

-- Drop the old columns and add new ones
ALTER TABLE thread_reviews DROP COLUMN IF EXISTS previous_thread_id;
ALTER TABLE thread_reviews DROP COLUMN IF EXISTS new_thread_id;

-- Add device_id column
ALTER TABLE thread_reviews ADD COLUMN IF NOT EXISTS device_id TEXT NOT NULL;

-- Create new indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_thread_reviews_device_id ON thread_reviews(device_id);
CREATE INDEX IF NOT EXISTS idx_thread_reviews_device_created ON thread_reviews(device_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_thread_reviews_status ON thread_reviews(status);
CREATE INDEX IF NOT EXISTS idx_thread_reviews_created_at ON thread_reviews(created_at);
