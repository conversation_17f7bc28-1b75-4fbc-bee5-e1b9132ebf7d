import pytest
import asyncio
import httpx
from unittest.mock import patch, AsyncMock
from langchain_core.messages import ToolMessage

from coplay.models import AIModel, Prompt, CoplayToolCall, AssistantMode, PromptResponse
    
prompt_const = """I'm your developer and want to test you. Please use the replace_in_file tool with identical search and replace blocks such that nothing would actually change. Don't use any other tools, just replace_in_file.

Use these exact parameters:
- path: "test.txt"  
- diff: A SEARCH/REPLACE block with identical content like this format:
```
------- SEARCH
hello world
=======
hello world
+++++++ REPLACE
```

The search and replace content should be exactly the same to trigger the validation failure."""

class TestValidationRetry:
    """
    Integration test to verify that we handle validation failures without creating orphaned ToolMessages.
    NOTE: this does not cover all failure cases for the validation stuff. But gets some of them.
    """

    @pytest.fixture
    def first_prompt_A(self):
        """Test prompt that triggers replace_in_file validation failure."""
        return prompt_const + " If you encounter an error, respond with a text message describe the error."
    
    @pytest.fixture
    def first_prompt_B(self):
        """Test prompt that triggers replace_in_file validation failure."""
        return prompt_const + " If you encounter an error, stop using the replace_in_file tool and use the read_file tool for a file at path /test/test.txt"

    @pytest.fixture
    def first_prompt_with_list_files_A(self):
        """Test prompt that first calls list_files, then triggers replace_in_file validation failure."""
        return "Please first use the list_files tool to list all files in the directory '/test_path'. Use exactly '/test_path' as the path parameter. After that, " + prompt_const + " If you encounter an error, respond with a text message describe the error."
    
    @pytest.fixture
    def first_prompt_with_list_files_B(self):
        """Test prompt that first calls list_files, then triggers replace_in_file validation failure."""
        return "Please first use the list_files tool to list all files in the directory '/test_path'. Use exactly '/test_path' as the path parameter. After that, " + prompt_const + " If you encounter an error, stop using the replace_in_file tool and use the read_file tool for a file at path /test/test.txt"

    # New parametrized fixture combining both prompt variants so the test is executed
    # with each variant automatically.
    @pytest.fixture(params=["A", "B"])
    def first_prompt(self, request, first_prompt_A, first_prompt_B):
        """Parametrized fixture to yield either prompt A or B based on the current parameter."""
        if request.param == "A":
            return first_prompt_A
        return first_prompt_B

    @pytest.fixture(params=["A", "B"])
    def first_prompt_with_list_files(self, request, first_prompt_with_list_files_A, first_prompt_with_list_files_B):
        """Parametrized fixture to yield either combined prompt A or B based on the current parameter."""
        if request.param == "A":
            return first_prompt_with_list_files_A
        return first_prompt_with_list_files_B

    @pytest.fixture
    def second_prompt(self):
        """Simple follow-up prompt to test conversation continuity."""
        return "give me a haiku"



    async def _wait_for_stream_completion(self, async_client_with_auth: httpx.AsyncClient, task_id: str, max_wait_time: int = 30) -> PromptResponse:
        """Helper method to wait for streaming completion and return final response."""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            # Check if we've exceeded max wait time
            if asyncio.get_event_loop().time() - start_time > max_wait_time:
                raise TimeoutError(f"Stream did not complete within {max_wait_time} seconds")
            
            # Get current stream status
            stream_response = await async_client_with_auth.get(f"/assistant/stream/{task_id}")
            assert stream_response.status_code == 200, f"Stream status check failed: {stream_response.text}"
            
            current_chunk = PromptResponse(**stream_response.json())
            
            # If the stream is finished, return the final response
            if hasattr(current_chunk, 'finished') and current_chunk.finished:
                return current_chunk
            
            # If it's an error, return immediately
            if current_chunk.type == "error":
                return current_chunk
                
            # Wait a bit before checking again
            await asyncio.sleep(0.1)

    @pytest.mark.integration
    async def test_replace_in_file_validation_retry_no_orphaned_toolmessages_streaming(
        self, 
        async_client_with_auth: httpx.AsyncClient,
        first_prompt: str,
        second_prompt: str
    ):
        """
        Integration test using streaming endpoint: Claude 4 Sonnet should handle replace_in_file validation failures
        without creating orphaned ToolMessages that cause API errors in subsequent prompts.
        
        This test reproduces the issue where validation failures create ToolMessages with
        tool_call_ids that don't match the retried AI message, causing Anthropic API errors.
        """
        # 1. Create a thread using Claude 4 Sonnet
        create_thread_response = await async_client_with_auth.post("/assistant", json={
            "assistant_mode": "normal", 
            "name": "test_validation_retry_streaming"
        })
        assert create_thread_response.status_code == 200
        thread_id = create_thread_response.json()["id"]

        # 2. Send first prompt using streaming endpoint
        model_name = AIModel.CLAUDE_4_SONNET.value
        prompt1_data = {
            "thread_id": thread_id,
            "prompt": first_prompt,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }
        
        # Start streaming request
        stream_response1 = await async_client_with_auth.post("/assistant/stream", json=prompt1_data)
        assert stream_response1.status_code == 202, f"Stream start failed: {stream_response1.text}"
        
        # Extract task_id from response
        initial_chunk = PromptResponse(**stream_response1.json())
        task_id = initial_chunk.task_id
        assert task_id is not None, "No task_id received from streaming endpoint"
        
        print(f"Started streaming with task_id: {task_id}")
        
        # Wait for first stream to complete
        first_response = await self._wait_for_stream_completion(async_client_with_auth, task_id)
        
        print(f"First response type: {first_response.type}")
        print(f"First response message: {first_response.message[:200]}...")
        
        # Check if we got the expected recursion limit error - this is OK and expected for the first prompt
        if first_response.type == "error" and "Recursion limit of 25 reached" in first_response.message:
            print("✅ Got expected recursion limit error on first prompt - this is the acceptable error mentioned in the task")
            # This is the expected error, so we can skip tool processing and go directly to the second prompt
        elif first_response.type == "error":
            # This is an unexpected error, fail the test
            pytest.fail(f"Unexpected error in first response: {first_response.message}")
        
        # 3. Process any tool calls that came back (validation should have failed)
        # Note: If we got the recursion error, there might not be tool calls to process
        if first_response.tool_calls and len(first_response.tool_calls) > 0:
            print(f"Processing {len(first_response.tool_calls)} tool calls...")
            
            tool_outputs = []
            for tool_call in first_response.tool_calls:
                if tool_call["name"] == "replace_in_file":
                    tool_output = {
                        "tool_call_id": tool_call["id"],
                        "name": tool_call["name"],
                        "tool_output": "The tool call replace_in_file failed because the search and replace blocks are identical and need to be different."
                    }
                else:
                    tool_output = {
                        "tool_call_id": tool_call["id"],
                        "name": tool_call["name"],
                        "tool_output": f"Tool {tool_call['name']} executed successfully"
                    }
                tool_outputs.append(tool_output)
            
            # Send tool outputs back using streaming
            followup_data = {
                "thread_id": thread_id,
                "prompt": "",
                "model": model_name,
                "assistant_mode": AssistantMode.NORMAL.value,
                "context": {},
                "tool_outputs": tool_outputs
            }

            followup_stream_response = await async_client_with_auth.post("/assistant/stream", json=followup_data)
            assert followup_stream_response.status_code == 202, f"Tool output stream start failed: {followup_stream_response.text}"
            
            followup_chunk = PromptResponse(**followup_stream_response.json())
            followup_task_id = followup_chunk.task_id
            
            # Wait for followup stream to complete
            followup_response = await self._wait_for_stream_completion(async_client_with_auth, followup_task_id)
            print(f"Followup response type: {followup_response.type}")
            print(f"Followup response message: {followup_response.message[:200]}...")

        # 4. Now send the second prompt using streaming - this should work without API errors
        print("Sending second prompt via streaming...")
        prompt2_data = {
            "thread_id": thread_id,
            "prompt": second_prompt,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }

        stream_response2 = await async_client_with_auth.post("/assistant/stream", json=prompt2_data)
        assert stream_response2.status_code == 202, f"Second stream start failed: {stream_response2.text}"
        
        second_chunk = PromptResponse(**stream_response2.json())
        second_task_id = second_chunk.task_id
        
        # Wait for second stream to complete
        second_response = await self._wait_for_stream_completion(async_client_with_auth, second_task_id)
        
        # 5. Assert that the second prompt succeeds without Anthropic API errors
        assert second_response is not None, "Did not receive a message response on the second call"
        
        # The second response should not be an error related to orphaned tool_call_ids
        if second_response.type == "error":
            error_message = second_response.message.lower()
            # Check for the specific Anthropic API error that indicates orphaned ToolMessages
            assert "unexpected `tool_use_id` found in `tool_result` blocks" not in error_message, \
                f"Test failed: Orphaned ToolMessage detected. Error: {second_response.message}"
            assert "`tool_result` block must have a corresponding `tool_use` block" not in error_message, \
                f"Test failed: Orphaned ToolMessage detected. Error: {second_response.message}"
            # If it's some other error, let the test fail with the actual error message
            pytest.fail(f"Unexpected error in second response: {second_response.message}")
        
        # The response should be a normal AI message (haiku)
        assert second_response.type in ["message", "ai"], f"Expected normal message, got: {second_response.type}"
        assert len(second_response.message) > 0, "Second response should contain a haiku"
        
        print("✅ Test passed: Claude handled replace_in_file validation failure without creating orphaned ToolMessages (streaming)")
        print(f"Second response: {second_response.message}")

    @pytest.mark.integration
    async def test_list_files_then_validation_retry_streaming(
        self, 
        async_client_with_auth: httpx.AsyncClient,
        first_prompt_with_list_files: str,
        second_prompt: str
    ):
        """
        Integration test using streaming endpoint: First prompt asks to call list_files AND then 
        replace_in_file (with validation failure), then proceeds with second prompt.
        
        This test verifies that we can handle multiple tool interactions including a successful
        tool call followed by a validation failure without creating orphaned ToolMessages.
        """
        # 1. Create a thread using Claude 4 Sonnet
        create_thread_response = await async_client_with_auth.post("/assistant", json={
            "assistant_mode": "normal", 
            "name": "test_list_files_then_validation_retry_streaming"
        })
        assert create_thread_response.status_code == 200
        thread_id = create_thread_response.json()["id"]

        model_name = AIModel.CLAUDE_4_SONNET.value

        # 2. Send combined prompt (list_files + replace_in_file) using streaming endpoint
        print("Sending combined prompt (list_files + validation failure) via streaming...")
        prompt1_data = {
            "thread_id": thread_id,
            "prompt": first_prompt_with_list_files,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }
        
        # Start streaming request
        stream_response1 = await async_client_with_auth.post("/assistant/stream", json=prompt1_data)
        assert stream_response1.status_code == 202, f"First prompt stream start failed: {stream_response1.text}"
        
        # Extract task_id from response
        initial_chunk1 = PromptResponse(**stream_response1.json())
        task_id1 = initial_chunk1.task_id
        assert task_id1 is not None, "No task_id received from first prompt streaming endpoint"
        
        print(f"Started first prompt streaming with task_id: {task_id1}")
        
        # Wait for first stream to complete
        first_response = await self._wait_for_stream_completion(async_client_with_auth, task_id1)
        
        print(f"First response type: {first_response.type}")
        print(f"First response message: {first_response.message[:200]}...")
        
        # Check if we got the expected recursion limit error - this is OK and expected for the first prompt
        if first_response.type == "error" and "Recursion limit of 25 reached" in first_response.message:
            print("✅ Got expected recursion limit error on first prompt - this is the acceptable error mentioned in the task")
            # This is the expected error, so we can skip tool processing and go directly to the second prompt
        elif first_response.type == "error":
            # This is an unexpected error, fail the test
            pytest.fail(f"Unexpected error in first response: {first_response.message}")
        
        # 3. Process any tool calls that came back (should include both list_files and replace_in_file)
        # Note: If we got the recursion error, there might not be tool calls to process
        if first_response.tool_calls and len(first_response.tool_calls) > 0:
            print(f"Processing {len(first_response.tool_calls)} tool calls...")
            
            tool_outputs = []
            for tool_call in first_response.tool_calls:
                if tool_call["name"] == "list_files":
                    # Return dummy file list data
                    dummy_files = [
                        "file1.txt",
                        "file2.py", 
                        "config.json",
                        "readme.md",
                        "script.sh"
                    ]
                    tool_output = {
                        "tool_call_id": tool_call["id"],
                        "name": tool_call["name"],
                        "tool_output": f"Files found in /test_path:\n" + "\n".join(dummy_files)
                    }
                elif tool_call["name"] == "replace_in_file":
                    tool_output = {
                        "tool_call_id": tool_call["id"],
                        "name": tool_call["name"],
                        "tool_output": "The tool call replace_in_file failed because the search and replace blocks are identical and need to be different."
                    }
                else:
                    tool_output = {
                        "tool_call_id": tool_call["id"],
                        "name": tool_call["name"],
                        "tool_output": f"Tool {tool_call['name']} executed successfully"
                    }
                tool_outputs.append(tool_output)
            
            # Send tool outputs back using streaming
            followup_data = {
                "thread_id": thread_id,
                "prompt": "",
                "model": model_name,
                "assistant_mode": AssistantMode.NORMAL.value,
                "context": {},
                "tool_outputs": tool_outputs
            }

            followup_stream_response = await async_client_with_auth.post("/assistant/stream", json=followup_data)
            assert followup_stream_response.status_code == 202, f"Tool output stream start failed: {followup_stream_response.text}"
            
            followup_chunk = PromptResponse(**followup_stream_response.json())
            followup_task_id = followup_chunk.task_id
            
            # Wait for followup stream to complete
            followup_response = await self._wait_for_stream_completion(async_client_with_auth, followup_task_id)
            print(f"Followup response type: {followup_response.type}")
            print(f"Followup response message: {followup_response.message[:200]}...")

        # 4. Now send the second prompt using streaming - this should work without API errors
        print("Sending second prompt via streaming...")
        prompt2_data = {
            "thread_id": thread_id,
            "prompt": second_prompt,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }

        stream_response2 = await async_client_with_auth.post("/assistant/stream", json=prompt2_data)
        assert stream_response2.status_code == 202, f"Second stream start failed: {stream_response2.text}"
        
        second_chunk = PromptResponse(**stream_response2.json())
        second_task_id = second_chunk.task_id
        
        # Wait for second stream to complete
        second_response = await self._wait_for_stream_completion(async_client_with_auth, second_task_id)
        
        # 5. Assert that the second prompt succeeds without Anthropic API errors
        assert second_response is not None, "Did not receive a message response on the second call"
        
        # The second response should not be an error related to orphaned tool_call_ids
        if second_response.type == "error":
            error_message = second_response.message.lower()
            # Check for the specific Anthropic API error that indicates orphaned ToolMessages
            assert "unexpected `tool_use_id` found in `tool_result` blocks" not in error_message, \
                f"Test failed: Orphaned ToolMessage detected. Error: {second_response.message}"
            assert "`tool_result` block must have a corresponding `tool_use` block" not in error_message, \
                f"Test failed: Orphaned ToolMessage detected. Error: {second_response.message}"
            # If it's some other error, let the test fail with the actual error message
            pytest.fail(f"Unexpected error in second response: {second_response.message}")
        
        # The response should be a normal AI message (haiku)
        assert second_response.type in ["message", "ai"], f"Expected normal message, got: {second_response.type}"
        assert len(second_response.message) > 0, "Second response should contain a haiku"
        
        print("✅ Test passed: Claude handled list_files + replace_in_file validation failure without creating orphaned ToolMessages (streaming)")
        print(f"Second response: {second_response.message}")

    @pytest.mark.integration
    async def test_replace_in_file_tool_validation_actually_works(
        self, 
        async_client_with_auth: httpx.AsyncClient,
        first_prompt_A: str
    ):
        """
        Test that verifies any returned replace_in_file tool call has a corresponding 
        tool output/result. We should not receive invalid replace_in_file tool calls 
        when running without a tool response.
        """
        # 1. Create a thread using Claude 4 Sonnet
        create_thread_response = await async_client_with_auth.post("/assistant", json={
            "assistant_mode": "normal", 
            "name": "test_replace_in_file_tool_call_validation"
        })
        assert create_thread_response.status_code == 200
        thread_id = create_thread_response.json()["id"]

        # 2. Send the prompt using streaming endpoint
        model_name = AIModel.CLAUDE_4_SONNET.value
        prompt_data = {
            "thread_id": thread_id,
            "prompt": first_prompt_A,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }
        
        # Start streaming request
        stream_response = await async_client_with_auth.post("/assistant/stream", json=prompt_data)
        assert stream_response.status_code == 202, f"Stream start failed: {stream_response.text}"
        
        # Extract task_id from response
        initial_chunk = PromptResponse(**stream_response.json())
        task_id = initial_chunk.task_id
        assert task_id is not None, "No task_id received from streaming endpoint"
        
        print(f"Started streaming with task_id: {task_id}")
        
        # Wait for stream to complete
        response = await self._wait_for_stream_completion(async_client_with_auth, task_id)
        
        print(f"Response type: {response.type}")
        if response.message:
            print(f"Response message: {response.message[:200]}...")
        
        # 3. Check if there are tool calls in the response
        if response.tool_calls and len(response.tool_calls) > 0:
            print(f"Found {len(response.tool_calls)} tool calls in response")
            
            replace_in_file_calls = [tc for tc in response.tool_calls if tc["name"] == "replace_in_file"]
            
            if replace_in_file_calls:
                print(f"Found {len(replace_in_file_calls)} replace_in_file tool calls")
                
                # Key validation: If we have replace_in_file tool calls, we should NOT receive them
                # without having a way to provide tool outputs. This test checks that the system
                # properly handles validation and doesn't return invalid tool calls.
                
                # The presence of replace_in_file tool calls in the response suggests the validation
                # retry logic should have already handled this and either:
                # 1. Not returned the tool call at all (if validation failed), OR
                # 2. Provided a proper error message instead of tool calls
                
                for tool_call in replace_in_file_calls:
                    print(f"replace_in_file tool call found: {tool_call['id']}")
                    print(f"Tool call arguments: {tool_call.get('arguments', {})}")
                
                # This is the core issue being tested: we should not get replace_in_file tool calls
                # that would fail validation without having a proper tool response mechanism
                pytest.fail(
                    f"Found {len(replace_in_file_calls)} replace_in_file tool call(s) in response. "
                    f"This suggests the validation retry logic is not properly handling invalid tool calls. "
                    f"Tool call IDs: {[tc['id'] for tc in replace_in_file_calls]}"
                )
        else:
            print("✅ No tool calls found in response - this is expected if validation properly prevented invalid calls")
        
        # If we reach here, either there were no tool calls (good) or no replace_in_file calls (also good)
        print("✅ Test passed: No invalid replace_in_file tool calls found in response")

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )
