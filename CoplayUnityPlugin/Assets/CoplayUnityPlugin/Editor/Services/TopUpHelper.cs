using System;
using System.Threading.Tasks;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Common.CoplayApi.Models;
using Coplay.Controllers.Systems;

namespace Coplay.Services
{
    /// <summary>
    /// Shared utility class for handling top-up operations
    /// </summary>
    public static class TopUpHelper
    {
        /// <summary>
        /// Attempts to process top-up directly using the backend API
        /// </summary>
        /// <param name="apiClient">The API client to use for the request</param>
        /// <param name="settings">The settings controller to update balance</param>
        /// <param name="amount">The amount to top up</param>
        /// <returns>True if successful, false otherwise</returns>
        public static async Task<bool> TryDirectTopUpAsync(
            ICoplayApiClient apiClient, 
            SettingsController settings, 
            float amount)
        {
            try
            {
                var topUpRequest = new TopUpRequest
                {
                    AmountUsd = amount
                };

                var response = await apiClient.TopUpCreditsAsync(topUpRequest);
                
                if (response.Success)
                {
                    CoplayLogger.Log($"Direct top-up successful: ${amount:F2}, new balance: ${response.NewBalance:F2}");
                    
                    // Update local balance
                    if (response.NewBalance.HasValue)
                    {
                        settings.TotalCredit = response.NewBalance.Value;
                    }
                    return true;
                }
                else
                {
                    CoplayLogger.LogError($"Direct top-up failed: {response.Error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Exception during direct top-up: {ex.Message}", ex);
                return false;
            }
        }
    }
}
