using System.Collections.Generic;
using System.IO;
using System.Globalization;
using System.Threading;
using NUnit.Framework;
using UnityEditor;
using UnityEngine;
using Coplay.Controllers.Functions.Implementations;
using Coplay.Common;

namespace Coplay.Tests
{
    public class PropertyFunctionsTests
    {
        private const string TEST_PREFAB_PATH = "Assets/Tests/TestData/GameObjectReferenceTestPrefab.prefab";
        private const string TEMP_PREFAB_PATH = "Assets/Tests/TestData/Temp/GameObjectReferenceTestPrefab.prefab";
        
        [SetUp]
        public void Setup()
        {
            // Ensure temp directory exists
            string tempDir = Path.GetDirectoryName(TEMP_PREFAB_PATH);
            if (!AssetDatabase.IsValidFolder(tempDir))
            {
                Directory.CreateDirectory(tempDir);
                AssetDatabase.Refresh();
            }
            
            // Create a copy of the test prefab for each test to avoid interference
            if (File.Exists(AssetDatabase.GetAssetPath(AssetDatabase.LoadAssetAtPath<GameObject>(TEST_PREFAB_PATH))))
            {
                AssetDatabase.CopyAsset(TEST_PREFAB_PATH, TEMP_PREFAB_PATH);
                AssetDatabase.Refresh();
            }
        }
        
        [TearDown]
        public void TearDown()
        {
            // Clean up temp prefab
            if (File.Exists(AssetDatabase.GetAssetPath(AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH))))
            {
                AssetDatabase.DeleteAsset(TEMP_PREFAB_PATH);
                AssetDatabase.Refresh();
            }
        }
        
        [Test]
        public void TestSetGameObjectReference_ShouldPersistInPrefab()
        {
            // Arrange
            Assert.IsTrue(File.Exists(AssetDatabase.GetAssetPath(AssetDatabase.LoadAssetAtPath<GameObject>(TEST_PREFAB_PATH))), 
                $"Test prefab not found at {TEST_PREFAB_PATH}. Please create the test prefab as instructed.");
            Assert.IsTrue(File.Exists(AssetDatabase.GetAssetPath(AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH))), 
                $"Test prefab not found at {TEMP_PREFAB_PATH}. Please create the test prefab as instructed.");
            
            // Instantiate the prefab in the scene so PropertyFunctions can find the GameObjects
            GameObject prefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH);
            GameObject sceneInstance = PrefabUtility.InstantiatePrefab(prefabAsset) as GameObject; // As of this writing, we need to have the prefab in the active scene to set the property. TODO: when we fix this, we should be able to edit prefabs not in the active scene maybe?
            
            try
            {
                var parameters = new Dictionary<string, string>
                {
                    ["gameobject_path"] = "GameObjectReferenceTestPrefab",
                    ["property_name"] = "targetGameObject", 
                    ["value"] = "GameObjectReferenceTestPrefab/TestChild",
                    ["prefab_path"] = TEMP_PREFAB_PATH,
                    ["component_type"] = "TestGameObjectReferenceComponent"
                };
                
                // Act
                string result = PropertyFunctions.SetProperty(parameters);
                
                // Assert - Should not return an error
                Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with error: {result}");
                CoplayLogger.Log($"SetProperty result: {result}");
                
                // Verify the reference was actually set and persisted
                VerifyGameObjectReferenceWasSet();
            }
            finally
            {
                // Clean up the scene instance
                if (sceneInstance != null)
                {
                    UnityEngine.Object.DestroyImmediate(sceneInstance);
                }
            }
        }
        
        private void VerifyGameObjectReferenceWasSet()
        {
            // Load the prefab and verify the reference was set correctly
            GameObject prefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH);
            Assert.IsNotNull(prefabAsset, "Failed to load temp prefab for verification");
            
            // Load prefab contents to inspect
            GameObject prefabInstance = PrefabUtility.LoadPrefabContents(TEMP_PREFAB_PATH);
            try
            {
                // Find the parent object
                GameObject parentObject = GameObjectUtils.FindObjectByPath("GameObjectReferenceTestPrefab", prefabInstance.transform);
                Assert.IsNotNull(parentObject, "GameObjectReferenceTestPrefab object not found in prefab");
                
                // Get the test component
                var testComponent = parentObject.GetComponent<TestGameObjectReferenceComponent>();
                Assert.IsNotNull(testComponent, "TestGameObjectReferenceComponent not found on GameObjectReferenceTestPrefab");
                
                // Verify the reference was set
                Assert.IsNotNull(testComponent.targetGameObject, "targetGameObject field is null - reference was not set!");
                
                // Verify it references the correct object
                Assert.AreEqual("TestChild", testComponent.targetGameObject.name, 
                    $"targetGameObject references wrong object. Expected 'TestChild', got '{testComponent.targetGameObject.name}'");
                
                // Verify it's the correct child object by checking hierarchy
                GameObject expectedChild = GameObjectUtils.FindObjectByPath("GameObjectReferenceTestPrefab/TestChild", prefabInstance.transform);
                Assert.IsNotNull(expectedChild, "TestChild object not found in prefab");
                Assert.AreSame(expectedChild, testComponent.targetGameObject, 
                    "targetGameObject does not reference the expected TestChild object");
                
                CoplayLogger.Log("✓ GameObject reference verification passed - reference was set correctly and persisted!");
            }
            finally
            {
                PrefabUtility.UnloadPrefabContents(prefabInstance);
            }
        }
        
        [Test]
        public void TestGetGameObjectReference_ShouldReturnCorrectValue()
        {
            // Instantiate the prefab in the scene so PropertyFunctions can find the GameObjects
            GameObject prefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH);
            GameObject sceneInstance = PrefabUtility.InstantiatePrefab(prefabAsset) as GameObject;
            
            try
            {
                // First set a reference
                var setParameters = new Dictionary<string, string>
                {
                    ["gameobject_path"] = "GameObjectReferenceTestPrefab",
                    ["property_name"] = "targetGameObject",
                    ["value"] = "GameObjectReferenceTestPrefab/TestChild",
                    ["prefab_path"] = TEMP_PREFAB_PATH,
                    ["component_type"] = "TestGameObjectReferenceComponent"
                };
                
                string setResult = PropertyFunctions.SetProperty(setParameters);
                Assert.IsFalse(setResult.StartsWith("Error:"), $"SetProperty failed: {setResult}");
                
                // Now get the reference
                var getParameters = new Dictionary<string, string>
                {
                    ["gameobject_path"] = "GameObjectReferenceTestPrefab",
                    ["property_name"] = "targetGameObject",
                    ["prefab_path"] = TEMP_PREFAB_PATH,
                    ["component_type"] = "TestGameObjectReferenceComponent"
                };
                
                string getResult = PropertyFunctions.GetProperty(getParameters);
                Assert.IsFalse(getResult.StartsWith("Error:"), $"GetProperty failed: {getResult}");
                Assert.IsTrue(getResult.Contains("TestChild"), $"GetProperty result should contain 'TestChild', got: {getResult}");
            }
            finally
            {
                // Clean up the scene instance
                if (sceneInstance != null)
                {
                    UnityEngine.Object.DestroyImmediate(sceneInstance);
                }
            }
        }

        [Test]
        public void TestGetGameObjectReferenceWithoutPathShouldNotError()
        {
            // Instantiate the prefab in the scene so PropertyFunctions can find the GameObjects
            GameObject prefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(TEMP_PREFAB_PATH);
            GameObject sceneInstance = PrefabUtility.InstantiatePrefab(prefabAsset) as GameObject;
            
            try
            {
                // First set a reference
                var setParameters = new Dictionary<string, string>
                {
                    ["gameobject_path"] = "GameObjectReferenceTestPrefab",
                    ["property_name"] = "targetGameObject",
                    ["value"] = "TestChild",
                    ["prefab_path"] = TEMP_PREFAB_PATH,
                    ["component_type"] = "TestGameObjectReferenceComponent"
                };
                
                string setResult = PropertyFunctions.SetProperty(setParameters);
                Assert.IsFalse(setResult.StartsWith("Error:"), $"SetProperty failed: {setResult}");
                
                // Now get the reference
                var getParameters = new Dictionary<string, string>
                {
                    ["gameobject_path"] = "GameObjectReferenceTestPrefab",
                    ["property_name"] = "targetGameObject",
                    ["prefab_path"] = TEMP_PREFAB_PATH,
                    ["component_type"] = "TestGameObjectReferenceComponent"
                };
                
                string getResult = PropertyFunctions.GetProperty(getParameters);
                Assert.IsFalse(getResult.StartsWith("Error:"), $"GetProperty failed: {getResult}");
                Assert.IsTrue(getResult.Contains("TestChild"), $"GetProperty result should contain 'TestChild', got: {getResult}");
            }
            finally
            {
                // Clean up the scene instance
                if (sceneInstance != null)
                {
                    UnityEngine.Object.DestroyImmediate(sceneInstance);
                }
            }
        }

        // TODO: test that getgameobjectinfo works on a prefab that is not in the current active scene. I.e. using prefab_path.

        #region Invariant Culture Number Parsing Tests

        /// <summary>
        /// Helper method to temporarily change the current culture for testing
        /// </summary>
        private void WithCulture(CultureInfo culture, System.Action action)
        {
            var originalCulture = Thread.CurrentThread.CurrentCulture;
            var originalUICulture = Thread.CurrentThread.CurrentUICulture;
            try
            {
                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;
                action();
            }
            finally
            {
                Thread.CurrentThread.CurrentCulture = originalCulture;
                Thread.CurrentThread.CurrentUICulture = originalUICulture;
            }
        }

        /// <summary>
        /// Create a test GameObject with Transform component for testing numeric properties
        /// </summary>
        private GameObject CreateTestGameObject()
        {
            var go = new GameObject("TestObject");
            return go;
        }

        [Test]
        public void TestFloatParsing_WithGermanLocale_ShouldUseInvariantCulture()
        {
            // German locale uses comma as decimal separator
            var germanCulture = new CultureInfo("de-DE");
            
            WithCulture(germanCulture, () =>
            {
                var testObject = CreateTestGameObject();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "Transform",
                        ["property_name"] = "localScale",
                        ["value"] = "1.5,2.5,3.5" // Using period as decimal separator (invariant culture)
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with German locale: {result}");
                    
                    // Verify the values were set correctly
                    var transform = testObject.GetComponent<Transform>();
                    Assert.AreEqual(1.5f, transform.localScale.x, 0.001f, "X component not parsed correctly");
                    Assert.AreEqual(2.5f, transform.localScale.y, 0.001f, "Y component not parsed correctly");
                    Assert.AreEqual(3.5f, transform.localScale.z, 0.001f, "Z component not parsed correctly");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        [Test]
        public void TestFloatParsing_WithFrenchLocale_ShouldUseInvariantCulture()
        {
            // French locale also uses comma as decimal separator
            var frenchCulture = new CultureInfo("fr-FR");
            
            WithCulture(frenchCulture, () =>
            {
                var testObject = CreateTestGameObject();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "Transform",
                        ["property_name"] = "localPosition",
                        ["value"] = "10.25,20.75,30.125" // Using period as decimal separator
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with French locale: {result}");
                    
                    // Verify the values were set correctly
                    var transform = testObject.GetComponent<Transform>();
                    Assert.AreEqual(10.25f, transform.localPosition.x, 0.001f, "X component not parsed correctly");
                    Assert.AreEqual(20.75f, transform.localPosition.y, 0.001f, "Y component not parsed correctly");
                    Assert.AreEqual(30.125f, transform.localPosition.z, 0.001f, "Z component not parsed correctly");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        [Test]
        public void TestVector2Parsing_WithGermanLocale_ShouldUseInvariantCulture()
        {
            var germanCulture = new CultureInfo("de-DE");
            
            WithCulture(germanCulture, () =>
            {
                var testObject = CreateTestGameObject();
                var rectTransform = testObject.AddComponent<RectTransform>();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "RectTransform",
                        ["property_name"] = "anchoredPosition",
                        ["value"] = "100.5,200.75" // Using period as decimal separator
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with German locale: {result}");
                    
                    // Verify the values were set correctly
                    Assert.AreEqual(100.5f, rectTransform.anchoredPosition.x, 0.001f, "X component not parsed correctly");
                    Assert.AreEqual(200.75f, rectTransform.anchoredPosition.y, 0.001f, "Y component not parsed correctly");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        [Test]
        public void TestIntegerParsing_WithDifferentLocales_ShouldUseInvariantCulture()
        {
            var testCultures = new[]
            {
                new CultureInfo("de-DE"), // German
                new CultureInfo("fr-FR"), // French
                new CultureInfo("ru-RU"), // Russian
                new CultureInfo("ja-JP")  // Japanese
            };

            foreach (var culture in testCultures)
            {
                WithCulture(culture, () =>
                {
                    var testObject = CreateTestGameObject();
                    var light = testObject.AddComponent<Light>();
                    try
                    {
                        var parameters = new Dictionary<string, string>
                        {
                            ["gameobject_path"] = "TestObject",
                            ["component_type"] = "Light",
                            ["property_name"] = "cullingMask",
                            ["value"] = "12345" // Integer value
                        };

                        string result = PropertyFunctions.SetProperty(parameters);
                        
                        Assert.IsFalse(result.StartsWith("Error:"), 
                            $"SetProperty failed with {culture.Name} locale: {result}");
                        
                        // Verify the value was set correctly
                        Assert.AreEqual(12345, light.cullingMask, 
                            $"Integer not parsed correctly with {culture.Name} locale");
                    }
                    finally
                    {
                        UnityEngine.Object.DestroyImmediate(testObject);
                    }
                });
            }
        }

        [Test]
        public void TestColorParsing_WithGermanLocale_ShouldUseInvariantCulture()
        {
            var germanCulture = new CultureInfo("de-DE");
            
            WithCulture(germanCulture, () =>
            {
                var testObject = CreateTestGameObject();
                var light = testObject.AddComponent<Light>();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "Light",
                        ["property_name"] = "color",
                        ["value"] = "0.5,0.75,1.0,0.8" // RGBA with period decimal separators
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with German locale: {result}");
                    
                    // Verify the color values were set correctly
                    Assert.AreEqual(0.5f, light.color.r, 0.001f, "Red component not parsed correctly");
                    Assert.AreEqual(0.75f, light.color.g, 0.001f, "Green component not parsed correctly");
                    Assert.AreEqual(1.0f, light.color.b, 0.001f, "Blue component not parsed correctly");
                    Assert.AreEqual(0.8f, light.color.a, 0.001f, "Alpha component not parsed correctly");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        [Test]
        public void TestVector2JsonParsing_WithGermanLocale_ShouldUseInvariantCulture()
        {
            var germanCulture = new CultureInfo("de-DE");
            
            WithCulture(germanCulture, () =>
            {
                var testObject = CreateTestGameObject();
                var rectTransform = testObject.AddComponent<RectTransform>();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "RectTransform",
                        ["property_name"] = "anchoredPosition",
                        ["value"] = "{x:150.25,y:250.75}" // JSON format with period decimal separators
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    Assert.IsFalse(result.StartsWith("Error:"), $"SetProperty failed with German locale: {result}");
                    
                    // Verify the values were set correctly
                    Assert.AreEqual(150.25f, rectTransform.anchoredPosition.x, 0.001f, "X component not parsed correctly from JSON");
                    Assert.AreEqual(250.75f, rectTransform.anchoredPosition.y, 0.001f, "Y component not parsed correctly from JSON");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        [Test]
        public void TestFloatParsing_ShouldRejectCommaDecimalSeparator()
        {
            var germanCulture = new CultureInfo("de-DE");
            
            WithCulture(germanCulture, () =>
            {
                var testObject = CreateTestGameObject();
                try
                {
                    var parameters = new Dictionary<string, string>
                    {
                        ["gameobject_path"] = "TestObject",
                        ["component_type"] = "Transform",
                        ["property_name"] = "localScale",
                        ["value"] = "1,5,2,5,3,5" // Using comma as decimal separator (German style)
                    };

                    string result = PropertyFunctions.SetProperty(parameters);
                    
                    // This should fail because we're using invariant culture parsing
                    // The comma will be interpreted as a separator between values, not a decimal separator
                    // So we'll get 6 values instead of 3, which should cause an error or unexpected behavior
                    
                    // Verify the values were NOT set as expected (because comma is treated as value separator)
                    var transform = testObject.GetComponent<Transform>();
                    // The parsing should interpret this as "1", "5", "2", "5", "3", "5"
                    // So it should set localScale to (1, 5, 2) and ignore the extra values
                    Assert.AreEqual(1f, transform.localScale.x, 0.001f, "X should be 1 (not 1.5)");
                    Assert.AreEqual(5f, transform.localScale.y, 0.001f, "Y should be 5 (not 2.5)");
                    Assert.AreEqual(2f, transform.localScale.z, 0.001f, "Z should be 2 (not 3.5)");
                }
                finally
                {
                    UnityEngine.Object.DestroyImmediate(testObject);
                }
            });
        }

        #endregion
    }
}
