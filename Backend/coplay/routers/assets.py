import asyncio
from uuid import uuid4
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, UploadFile, File, Form
from supabase import AsyncClient
from supabase_auth.types import User

from ..models import AssetGenerationRequest, AssetGenerationStatus, AssetGenerationTask, AssetGenerationTaskStatus, AssetGenerationProvider, FileUploadResponse
from ..logger import logger
from ..jobs import ASSET_GENERATION_JOBS
from .auth import get_current_user, validate_thread_belongs_to_user, get_validated_device_id
from ..providers.fal_provider import process_fal_generation
from ..providers.meshy_provider import process_meshy_generation
from ..supabase_db import get_admin_supabase_client
from ..constants import SUPABASE_BUCKET

router = APIRouter()

async def process_asset_generation(task_id: str, request: AssetGenerationRequest):
    """Background task to process asset generation using the specified provider"""
    try:
        # Update status to processing
        ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
            task_id=task_id,
            status="processing",
            progress=0,
            message="Processing asset generation...",
            finished=False
        )
        
        logger.info(f"Starting asset generation for task {task_id} using provider: {request.provider}")
        
        # Route to the appropriate provider
        if request.provider == AssetGenerationProvider.HUNYUAN3D21:
            await process_fal_generation(task_id, request)
        elif request.provider == AssetGenerationProvider.MESHY4 or request.provider == AssetGenerationProvider.MESHY5:
            await process_meshy_generation(task_id, request)
        else:
            raise ValueError(f"Unsupported provider: {request.provider}")
            
    except Exception as e:
        logger.error(f"Error processing asset generation for task {task_id}: {str(e)}")
        
        # Update with error
        ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Asset generation failed: {str(e)}",
            finished=True
        )

@router.post("/assets/generate")
async def create_asset_generation(
    request: AssetGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Start a new asset generation job.
    Supports Hunyuan3D21 (image-to-3D), Meshy (text-to-3D, image-to-3D, texture generation).
    Returns immediately with a job ID for tracking progress.
    """
    try:
        # Validate provider-specific requirements
        if request.provider == AssetGenerationProvider.HUNYUAN3D21:
            if not request.input_image_url:
                raise HTTPException(status_code=400, detail="input_image_url is required for FAL generation")
        elif request.provider == AssetGenerationProvider.MESHY4 or request.provider == AssetGenerationProvider.MESHY5:
            # Meshy supports both text-to-3D and image-to-3D
            if request.input_image_url:
                # Image-to-3D mode - input_image_url is required
                logger.info(f"Meshy image-to-3D mode selected for task")
            elif request.model_url:
                # Texture generation mode - model_url is required
                logger.info(f"Meshy texture generation mode selected for task")
            elif request.prompt:
                # Text-to-3D mode - prompt is required
                logger.info(f"Meshy text-to-3D mode selected for task")
            else:
                raise HTTPException(status_code=400, detail="Either input_image_url (for image-to-3D) or prompt (for text-to-3D) is required for Meshy generation")
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported provider: {request.provider}")
        
        # Generate unique task ID
        task_id = "cpl-task-" + str(uuid4())
        
        logger.info(f"Creating {request.provider} asset generation job {task_id} for user {current_user.email}")
        
        # Initialize task in background results
        ASSET_GENERATION_JOBS[task_id] = AssetGenerationTask(
            status=AssetGenerationTaskStatus(
                task_id=task_id,
                status="pending",
                progress=0,
                message="Asset generation job created",
                finished=False
            ),
            cancel_scope=asyncio.Event()
        )
        
        # Start background processing
        background_tasks.add_task(process_asset_generation, task_id, request)
        
        return AssetGenerationStatus(
            job_id=task_id,
            status="pending",
            progress=0
        )
        
    except Exception as e:
        logger.error(f"Error creating asset generation job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create asset generation job: {str(e)}")

@router.get("/assets/status/{job_id}")
async def get_asset_generation_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get the status of an asset generation job.
    """
    if job_id not in ASSET_GENERATION_JOBS:
        raise HTTPException(status_code=404, detail="Job not found")
    
    task = ASSET_GENERATION_JOBS[job_id]
    status = task.status
    
    response = AssetGenerationStatus(
        job_id=job_id,
        status=status.status,
        progress=status.progress,
        message=status.message
    )
    
    # Add result if completed
    if status.status == "completed" and status.result:
        response.result = status.result.model_dump()
    elif status.status == "failed":
        response.error = status.message
    
    return response

@router.post("/assets/cancel/{job_id}")
async def cancel_asset_generation(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Cancel a running asset generation job.
    """
    if job_id not in ASSET_GENERATION_JOBS:
        raise HTTPException(status_code=404, detail="Job not found")
    
    task = ASSET_GENERATION_JOBS[job_id]
    
    if task.status.finished:
        raise HTTPException(status_code=400, detail="Job already finished")
    
    # Set cancel scope
    task.cancel_scope.set()
    
    # Update status
    task.status.status = "cancelled"
    task.status.message = "Job cancelled by user"
    task.status.finished = True
    
    logger.info(f"Asset generation job {job_id} cancelled")
    
    return {"status": "cancelled"}

@router.post("/assets/upload")
async def upload_file(
    thread_id: str = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    device_id: str = Depends(get_validated_device_id),
    supabase_client: AsyncClient = Depends(get_admin_supabase_client)
):
    """
    Upload a file to Supabase storage organized by thread ID.
    Returns a signed download URL for the uploaded file.
    """
    try:
        # Validate that the thread belongs to the user
        await validate_thread_belongs_to_user(thread_id, device_id)
        
        # Read file content
        file_content = await file.read()
        
        # Generate unique file path organized by thread ID
        now = datetime.now(timezone.utc)
        date_path = now.strftime("%Y/%m/%d")
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'bin'
        unique_filename = f"{uuid4()}.{file_extension}"
        file_path = f"uploads/{thread_id}/{date_path}/{unique_filename}"
        
        logger.info(f"Uploading file {file.filename} to {file_path} for thread {thread_id}")
        
        # Upload to Supabase storage
        try:
            result = await supabase_client.storage.from_(SUPABASE_BUCKET).upload(
                path=file_path,
                file=file_content,
                file_options={"content-type": file.content_type or "application/octet-stream"}
            )
            
            # Handle potential errors from old/new SDK versions
            upload_error = getattr(result, "error", None)
            if upload_error:
                logger.error(f"Failed to upload file to Supabase: {upload_error}")
                raise HTTPException(status_code=500, detail=f"Upload failed: {upload_error}")
                
        except Exception as e:
            logger.error(f"Supabase storage upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")
        
        # Create a signed URL that expires in 180 days (15,552,000 seconds)
        try:
            signed_url_result = await supabase_client.storage.from_(SUPABASE_BUCKET).create_signed_url(
                path=file_path,
                expires_in=15552000
            )
            
            signed_url_error = getattr(signed_url_result, "error", None)
            if signed_url_error:
                logger.error(f"Failed to create signed URL: {signed_url_error}")
                raise HTTPException(status_code=500, detail=f"Signed URL creation failed: {signed_url_error}")
            
            # Extract signed URL from different possible response shapes
            if isinstance(signed_url_result, dict):
                signed_url = signed_url_result.get("signedURL") or signed_url_result.get("signed_url")
            else:
                signed_url = getattr(signed_url_result, "signed_url", None)
            
            if not signed_url:
                logger.error("Signed URL missing in Supabase response")
                raise HTTPException(status_code=500, detail="Signed URL missing in Supabase response")
                
        except Exception as e:
            logger.error(f"Supabase signed URL error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Signed URL creation failed: {str(e)}")
        
        logger.info(f"Successfully uploaded file {file.filename} to {file_path}, signed URL created")
        
        return FileUploadResponse(
            success=True,
            file_url=signed_url,
            file_path=file_path
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return FileUploadResponse(
            success=False,
            error=f"Failed to upload file: {str(e)}"
        )
