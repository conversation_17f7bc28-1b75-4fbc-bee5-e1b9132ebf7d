import json
import async<PERSON>
import tiktoken
from tqdm import tqdm

from litellm import aembedding, BadRequestError

from .constants import OPENAI_KEY
from coplay.logger import logger

# Initialize the tokenizer for text-embedding-3-small
tokenizer = tiktoken.get_encoding("cl100k_base")  # This is the encoding used by text-embedding-3-small

async def create_object_embeddings(objects: list[dict]) -> list[list[float]]:
    # Maximum tokens per batch (OpenAI limit is 300,000)
    MAX_TOKENS_PER_BATCH = 250000  # Using a buffer for safety
    
    # Create batches based on token count
    batches = []
    current_batch = []
    current_token_count = 0
    
    for obj in objects:
        obj_str = json.dumps(obj)
        obj_tokens = len(tokenizer.encode(obj_str))
        
        # If adding this object would exceed the token limit, start a new batch
        if current_token_count + obj_tokens > MAX_TOKENS_PER_BATCH:
            if current_batch:  # Only add non-empty batches
                batches.append(current_batch)
            current_batch = [obj]
            current_token_count = obj_tokens
        else:
            current_batch.append(obj)
            current_token_count += obj_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    total_batches = len(batches)
    logger.debug(f"Created {total_batches} batches based on token count")
    
    async def process_batch(batch_idx, batch):
        logger.debug(f"Processing batch {batch_idx + 1} of {total_batches}")
        batch_inputs = [json.dumps(obj) for obj in batch]
        token_count = sum(len(tokenizer.encode(input_str)) for input_str in batch_inputs)
        logger.debug(f"Batch {batch_idx + 1} contains {token_count} tokens")
        
        response = await aembedding(
            model="text-embedding-3-small",
            dimensions=256,
            api_key=OPENAI_KEY,
            input=batch_inputs
        )
        return [j['embedding'] for j in response['data']]

    # Process all batches in parallel
    results = await asyncio.gather(
        *[process_batch(i, batch) for i, batch in enumerate(batches)]
    )

    logger.debug("Done processing batches")
    
    # Flatten results
    return [embedding for batch_result in results for embedding in batch_result]


async def process_batch(batch_idx, batch):
    logger.debug(f"Processing batch {batch_idx + 1} of {len(batch)}")
    token_count = sum(len(tokenizer.encode(input_str)) for input_str in batch)
    logger.debug(f"Batch {batch_idx + 1} contains {token_count} tokens")
    
    try:
        response = await aembedding(
            model="text-embedding-3-small",
            dimensions=256,
            # model="text-embedding-ada-002",
            api_key=OPENAI_KEY,
            input=batch
        )
        return [j['embedding'] for j in response['data']]
    except BadRequestError as e:
        logger.error(f"Error processing batch {batch_idx + 1} due to bad request: {e}")

        # Keeping this commented code here in case we want to dive deeper into the embedding issues in the future.
        # # Test each item individually to find the problematic one
        # for i, item in tqdm(enumerate(batch)):
        #     try:
        #         # logger.debug(f"Testing individual item {i} in failed batch: '{item}'")
        #         await aembedding(
        #             model="text-embedding-3-small",
        #             dimensions=256,
        #             api_key=OPENAI_KEY,
        #             input=[item]
        #         )
        #         # logger.debug(f"Item {i} is valid")
        #     except BadRequestError as item_error:
        #         logger.error(f"Found problematic item at index {i}: '{item}' - Error: {item_error}")
        #     except Exception as other_error:
        #         logger.error(f"Unexpected error testing item {i}: '{item}' - Error: {other_error}")
        logger.warning(f"Skipping batch {batch_idx + 1} and continuing with next batch")
        return None # NOTE: we have issues here with failing embeddings due to some prompt format issues. this returns something, so that we can keep batches alined with the original prompts.

async def create_text_embeddings(texts: list[str]):
    # Maximum tokens per batch (OpenAI limit is 300,000)
    MAX_TOKENS_PER_BATCH = 250000  # Using a buffer for safety
    MAX_TOKENS_PER_INPUT = 8192
    
    # Create batches based on token count
    batches = []
    current_batch = []
    current_token_count = 0
    
    for text in texts:
        encoded_text = tokenizer.encode(text)
        if len(encoded_text) >= MAX_TOKENS_PER_INPUT:
            encoded_text = encoded_text[:MAX_TOKENS_PER_INPUT-5]
            text = tokenizer.decode(encoded_text)

        text_tokens = len(encoded_text)
        
        # If adding this text would exceed the token limit, start a new batch
        if current_token_count + text_tokens > MAX_TOKENS_PER_BATCH:
            if current_batch:  # Only add non-empty batches
                batches.append(current_batch)
            current_batch = [text]
            current_token_count = text_tokens
        else:
            current_batch.append(text)
            current_token_count += text_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    total_batches = len(batches)
    logger.debug(f"Created {total_batches} batches for text embeddings based on token count")
    
    # Process all batches in parallel
    results = await asyncio.gather(
        *[process_batch(i, batch) for i, batch in enumerate(batches)]
    )

    logger.debug("Done processing text batches")
    
    successful_batches = []
    successful_embeddings = []
    for batch, embeddings in zip(batches, results):
        if embeddings is not None:
            successful_batches.append(batch)
            successful_embeddings.append(embeddings)

    return successful_batches, successful_embeddings
