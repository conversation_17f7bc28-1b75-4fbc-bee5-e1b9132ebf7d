import { useToast } from "@/components/ui/toast";
import { useAsyncOperation } from "@/utils/formHelpers";
import type { User } from "@supabase/supabase-js";

interface UseProfileSubmitProps {
  user: User | null;
  submitProfileUpdate: () => Promise<void>;
}

export function useProfileSubmit({
  user,
  submitProfileUpdate,
}: UseProfileSubmitProps) {
  const toast = useToast();

  const { execute: handleSubmit, isLoading } = useAsyncOperation(
    async () => {
      if (!user) {
        throw new Error("User not authenticated");
      }
      await submitProfileUpdate();
    },
    () => {
      toast.success("Profile updated successfully!");
    },
    (error) => {
      const errorMessage = error.message || "Please try again.";
      toast.error(`Failed to save profile: ${errorMessage}`);
    }
  );

  return { handleSubmit, isLoading };
}
