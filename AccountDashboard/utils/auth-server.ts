import { createClient } from "@/lib/supabase/server";
import { Session, SupabaseClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

/**
 * Server-side utility to require authentication - redirects to login if not authenticated
 * @param redirectTo - Where to redirect unauthenticated users (default: "/login")
 * @returns session and supabase client if user is authenticated, otherwise redirects
 */
export async function requireAuth(redirectTo: string = "/login"): Promise<{ session: Session; supabase: SupabaseClient }> {
  const { session, supabase, isAuthenticated } = await getAuthStatus();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    redirect(redirectTo);
  }

  return { session: session!, supabase };
}

/**
 * Server-side utility to get authentication status without redirecting
 * @returns session, supabase client, and authentication status
 */
export async function getAuthStatus() {
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);

  // Use getUser() for secure authentication verification
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  // Get session only if user is authenticated
  let session = null;
  let sessionError = null;
  if (user && !authError) {
    const { data: { session: currentSession }, error: sessionErr } = await supabase.auth.getSession();
    session = currentSession;
    sessionError = sessionErr;
  }

  return {
    session,
    supabase,
    isAuthenticated: !!user && !authError && !sessionError,
  };
}

