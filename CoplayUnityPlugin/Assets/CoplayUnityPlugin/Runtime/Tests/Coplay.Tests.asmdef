{"name": "Coplay.Tests", "rootNamespace": "Coplay.Tests", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "Coplay"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll", "Moq.dll", "Microsoft.Extensions.Logging.Abstractions.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}