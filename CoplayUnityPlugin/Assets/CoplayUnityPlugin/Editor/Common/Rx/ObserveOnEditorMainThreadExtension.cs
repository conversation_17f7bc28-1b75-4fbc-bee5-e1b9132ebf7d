using System;
using System.Collections.Generic;
using System.Reactive.Linq;
using System.Threading;
using UnityEditor;

namespace Coplay.Common.Rx
{
    public static class ObserveOnEditorMainThreadExtension
    {
        /// <summary>
        /// Observes the source on the Unity Editor main thread.
        /// </summary>
        /// <typeparam name="T">The type of items emitted by the source observable.</typeparam>
        /// <param name="source">The source observable to observe on the main thread.</param>
        /// <param name="delayedCall">Execute using delayCall regardless of main thread</param>
        /// <returns>An observable that emits items on the Unity Editor main thread.</returns>
        /// <remarks>
        /// This method is useful for ensuring that UI updates or other operations that must run on the main thread are executed correctly.
        /// </remarks>
        public static IObservable<T> ObserveOnEditorMainThread<T>(this IObservable<T> source, bool delayedCall = false)
        {
            return Observable.Create<T>(observer =>
            {
                var cancelled = false;
                var callbacks = new Queue<Action>();

                void ProcessQueue()
                {
                    lock (callbacks)
                    {
                        while (callbacks.TryDequeue(out var callback))
                        {
                            callback();
                        }
                    }

                    if (cancelled)
                        EditorApplication.update -= ProcessQueue;
                }

                void EnqueueCallback(Action callback)
                {
                    if (cancelled)
                        return;

                    lock (callbacks)
                        callbacks.Enqueue(callback);
                }

                EditorApplication.update += ProcessQueue;

                var subscription = source.Subscribe(
                    item =>
                    {
                        if (!delayedCall && IsMainThread())
                            observer.OnNext(item);
                        else
                            EnqueueCallback(() => observer.OnNext(item));
                    },
                    ex =>
                    {
                        if (!delayedCall && IsMainThread())
                            observer.OnError(ex);
                        else
                            EnqueueCallback(() => observer.OnError(ex));
                    },
                    () =>
                    {
                        if (!delayedCall && IsMainThread())
                            observer.OnCompleted();
                        else
                            EnqueueCallback(() => observer.OnCompleted());
                    }
                );

                return () =>
                {
                    cancelled = true;
                    subscription.Dispose();
                };
            });
        }

        private static bool IsMainThread() => Thread.CurrentThread.ManagedThreadId == 1;
    }
}
