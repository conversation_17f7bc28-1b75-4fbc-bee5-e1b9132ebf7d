using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Coplay.Services;
using Newtonsoft.Json;
using System.IO.Abstractions;
using System.Reactive.Subjects;
using Microsoft.Extensions.Logging;
using System.Reactive.Linq;
using Coplay.Common.Rx;

namespace Coplay.Controllers.Systems
{
    public enum CoplayState
    {
        Initialized,

        WaitingForAI,

        ExecutingFunctions,

        ExecutingAllFunctions,

        WaitingForUserMessage,

        WaitingForUserToExecuteFunction,

        GeneratingScene,
    }

    /// <summary>
    /// Central state management system for Coplay.
    /// Implements a single source of truth for application state.
    /// </summary>
    public class CoplayStateModel
    {
        private readonly ILogger<CoplayStateModel> _logger;
        private readonly IFileSystem _fileSystem;
        private readonly string _stateFilePath;

        public bool IsWaitingForAI => _currentState.Value == CoplayState.WaitingForAI;
        public bool IsInitialized => _currentState.Value == CoplayState.Initialized;
        public bool IsWaitingForUserToExecuteFunction => _currentState.Value == CoplayState.WaitingForUserToExecuteFunction;

        private readonly BehaviorSubject<CoplayState> _currentState = new BehaviorSubject<CoplayState>(CoplayState.Initialized);

        // Using ObserveOnEditorMainThread here because there are cases where state change happens inside of state change handler
        // which causes the problem that some subscribers receive latest state value, then previous state
        public IObservable<CoplayState> CurrentStateObservable => _currentState.AsObservable().ObserveOnEditorMainThread(delayedCall: true);

        // Add stack to track state history
        private readonly Stack<CoplayState> _stateHistory = new Stack<CoplayState>();

        private class StateData
        {
            public CoplayState currentState;
            public CoplayState[] stateHistoryArray;
        }

        public bool IsExecutingAllFunctions => _currentState.Value == CoplayState.ExecutingAllFunctions;

        public CoplayState CurrentState => _currentState.Value;

        public CoplayStateModel(ILogger<CoplayStateModel> logger, IApplicationPaths applicationPaths, IFileSystem fileSystem)
        {
            _logger = logger;
            _stateFilePath = Path.Combine(applicationPaths.DefaultLogsFolderPath, "coplay_system_state.json");
            _fileSystem = fileSystem;
            LoadState();
        }

        public void SetState(CoplayState newState)
        {
            if (_currentState.Value == newState)
            {
                _logger.LogWarning("You are setting to the same state: " + newState);
                return;
            }

            _logger.LogInformation($"Coplay State Change: {_currentState.Value} -> {newState}");

            // Push current state to history before changing
            _stateHistory.Push(_currentState.Value);

            _currentState.OnNext(newState);
            SaveState();
        }

        public void ReturnToPreviousState(string message = null)
        {
            if (_stateHistory.Count == 0)
            {
                _logger.LogWarning("No previous state to return to");
                return;
            }

            var previousState = _stateHistory.Pop();

            if (previousState == _currentState.Value)
            {
                _logger.LogWarning("Returning to the same state: " + previousState);
                return;
            }

            _logger.LogInformation($"Returned to previous state: {_currentState.Value} -> {previousState} " + (message != null ? $"({message})" : ""));
            _currentState.OnNext(previousState);
            SaveState();
        }

        private void SaveState()
        {
            var state = new StateData
            {
                currentState = _currentState.Value,
                stateHistoryArray = _stateHistory.ToArray()
            };

            string json = JsonConvert.SerializeObject(state);
            _fileSystem.File.WriteAllText(_stateFilePath, json);
        }

        private void LoadState()
        {
            if (_fileSystem.File.Exists(_stateFilePath))
            {
                string json = _fileSystem.File.ReadAllText(_stateFilePath);
                var state = JsonConvert.DeserializeObject<StateData>(json);

                if (state != null)
                {
                    _logger.LogInformation("Loading state from file, and setting to: " + state.currentState);
                    // Don't use SetState here to avoid saving during initialization
                    _currentState.OnNext(state.currentState);
                    // Handle weird cases, like the app crashing during an action
                    if (_currentState.Value == CoplayState.GeneratingScene)
                    {
                        _currentState.OnNext(CoplayState.WaitingForUserMessage);
                    }

                    // Restore state history
                    _stateHistory.Clear();
                    if (state.stateHistoryArray != null)
                    {
                        foreach (var historyState in state.stateHistoryArray.Reverse())
                        {
                            _stateHistory.Push(historyState);
                        }
                    }

                    return;
                }
            }
                
            // For fresh installations, maintain the default Initialized state
            _logger.LogInformation("No state file found, maintaining Initialized state for fresh installation");
        }

        public void ClearState()
        {
            _logger.LogInformation("Clearing state model");
            _stateHistory.Clear();  // Clear the state history
            SetState(CoplayState.Initialized);
            SaveState();
        }
    }
}
