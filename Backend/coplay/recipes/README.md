If you're creating new embeddings with different ids, be sure to delete the old embeddings as needed.

TODO in this folder for better future maintenance:
1. we should read all prompts for specific keyword from bigquery and save to a json file. In the file we can mark them as negative or positive prompts. This way we can easily update the list with new bigquery data later without overwriting existing positive/negative prompts.
2. create a calibration script that will find the right threshold depending on negative and positive prompt distances.

TODO: we should move all the code not related to the API to a folder like the UnityDocsUploader outside of Backend.

General flow of how to use this:
1. get all the negative and positive prompts -- see that folder readme.
2. run `embed_recipes.py`
3. calibrate the threshold by running test_positive_prompts.py