using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Coplay.Common;

namespace Coplay.Tests.Benchmark.LanguageTestRunners
{
    public class JavaScriptTestRunner : LanguageTestRunner
    {
        public override string Language => "javascript";
        
        private string GetNpmCommand()
        {
            // On Windows, use npm.cmd instead of npm to avoid PowerShell script issues
            return RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "npm.cmd" : "npm";
        }
        
        public override async Task<bool> IsLanguageAvailable()
        {
            try
            {
                var nodeResult = await ExecuteCommand("node", "--version", Environment.CurrentDirectory, 10, false);
                if (!nodeResult.Success)
                {
                    CoplayLogger.LogWarning("Node.js not available on this system");
                    return false;
                }
                
                var npmCommand = GetNpmCommand();
                var npmResult = await ExecuteCommand(npmCommand, "--version", Environment.CurrentDirectory, 10, false);
                if (!npmResult.Success)
                {
                    CoplayLogger.LogWarning("npm not available on this system");
                    return false;
                }
                
                CoplayLogger.Log($"Node.js available: {nodeResult.Output.Trim()}");
                CoplayLogger.Log($"npm available: {npmResult.Output.Trim()}");
                return true;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogWarning($"Node.js or npm not available on this system: {ex.Message}");
                return false;
            }
        }
        
        public override async Task<TestExecutionResult> RunTests(BenchmarkExercise exercise, string workingDirectory)
        {
            var result = new TestExecutionResult();
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Prepare test environment (no file copying needed since we're running in original folder)
                PrepareTestEnvironment(exercise, workingDirectory);
                
                // Check if package.json exists, if not create a minimal one
                var packageJsonPath = Path.Combine(workingDirectory, "package.json");
                if (!File.Exists(packageJsonPath))
                {
                    await CreateMinimalPackageJson(workingDirectory);
                }
                
                // Install dependencies if package.json exists
                if (File.Exists(packageJsonPath))
                {
                    CoplayLogger.Log("Installing npm dependencies...");
                    var npmCommand = GetNpmCommand();
                    var installResult = await ExecuteCommand(npmCommand, "install", workingDirectory, 120);
                    if (!installResult.Success)
                    {
                        CoplayLogger.LogWarning($"npm install failed: {installResult.Error}");
                        // Continue anyway, might still work
                    }
                }
                
                // Run tests using npm test
                var npmCommand2 = GetNpmCommand();
                var testResult = await ExecuteCommand(npmCommand2, "test", workingDirectory, TestTimeoutSeconds);
                
                result.Success = testResult.Success;
                result.Output = CleanTestOutput(testResult.Output, workingDirectory);
                result.Error = CleanTestOutput(testResult.Error, workingDirectory);
                result.TimedOut = testResult.TimedOut;
                result.Duration = DateTime.UtcNow - startTime;
                
                // Count syntax errors in the output
                if (!string.IsNullOrEmpty(result.Output) || !string.IsNullOrEmpty(result.Error))
                {
                    var combinedOutput = result.Output + " " + result.Error;
                    result.SyntaxErrors = CountOccurrences(combinedOutput, "SyntaxError");
                    result.SyntaxErrors += CountOccurrences(combinedOutput, "Unexpected token");
                    result.SyntaxErrors += CountOccurrences(combinedOutput, "Parse error");
                }
                
                // For JavaScript, syntax errors are also compilation errors
                result.CompilationErrors = result.SyntaxErrors;
                
                CoplayLogger.Log($"JavaScript test completed for {exercise.Name}: Success={result.Success}, Duration={result.Duration.TotalSeconds:F1}s");
                
                return result;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.Duration = DateTime.UtcNow - startTime;
                CoplayLogger.LogError($"Exception running JavaScript tests for {exercise.Name}: {ex.Message}", ex);
                return result;
            }
        }
        
        protected override void PrepareTestEnvironment(BenchmarkExercise exercise, string exerciseDirectory)
        {
            // Since we're running tests in the original exercise folder, no file copying is needed
            // This method can be used for any JavaScript-specific environment setup if needed
            CoplayLogger.Log($"Preparing JavaScript test environment for {exercise.Name} in {exerciseDirectory}");
        }
        
        private async Task CreateMinimalPackageJson(string workingDirectory)
        {
            var packageJson = @"{
  ""name"": ""benchmark-exercise"",
  ""version"": ""1.0.0"",
  ""description"": ""Benchmark exercise"",
  ""scripts"": {
    ""test"": ""jest""
  },
  ""devDependencies"": {
    ""jest"": ""^29.0.0""
  }
}";
            
            var packageJsonPath = Path.Combine(workingDirectory, "package.json");
            await File.WriteAllTextAsync(packageJsonPath, packageJson);
            CoplayLogger.Log("Created minimal package.json for JavaScript exercise");
        }
        
        private int CountOccurrences(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return 0;
            
            return text.Split(new[] { pattern }, StringSplitOptions.None).Length - 1;
        }
    }
}
