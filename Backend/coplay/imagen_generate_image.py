import base64

from google import genai
from google.genai import types

from coplay.constants import GEMINI_API_KEY
from coplay.models import ImageFunctionsRequest, ImageGenerationRequest


async def imagen_generate_image(request: ImageFunctionsRequest | ImageGenerationRequest) -> str:
    """
    Generates an image using Google Imagen 4 via Vertex AI based on the provided request.

    Returns a data URI with the PNG image encoded in base64.
    """
    # Initialize Imagen client
    client = genai.Client(api_key=GEMINI_API_KEY)

    # Build prompt depending on request type
    if isinstance(request, ImageFunctionsRequest):
        prompt_str = request.prompt
    elif isinstance(request, ImageGenerationRequest):
        prompt_str = f"{request.object_prompt} {request.style_prompt}".strip()

    # Generate image using Google Imagen
    response = await client.aio.models.generate_images(
        model="imagen-3.0-generate-002",
        prompt=prompt_str,
        config=types.GenerateImagesConfig(number_of_images=1)
    )

    # Extract image bytes and encode as base64 data URI
    image_bytes = response.generated_images[0].image.image_bytes
    b64 = base64.b64encode(image_bytes).decode("utf-8")
    return f"data:image/png;base64,{b64}"
