using System;
using System.Threading.Tasks;

namespace Coplay.Services.Threading
{
    /// <summary>
    /// Interface for executing actions on the Unity editor thread
    /// </summary>
    public interface IEditorThreadHelper : IDisposable
    {
        /// <summary>
        /// Executes an action on the editor thread
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <returns>A task that completes when the action is executed</returns>
        Task ExecuteOnEditorThreadAsync(Action action);

        /// <summary>
        /// Executes a function on the editor thread and returns the result
        /// </summary>
        /// <typeparam name="T">The return type of the function</typeparam>
        /// <param name="func">The function to execute</param>
        /// <returns>A task that completes with the result of the function</returns>
        Task<T> ExecuteOnEditorThreadAsync<T>(Func<T> func);

        /// <summary>
        /// Executes an async function on the editor thread and returns the result
        /// </summary>
        /// <typeparam name="T">The return type of the async function</typeparam>
        /// <param name="asyncFunc">The async function to execute</param>
        /// <returns>A task that completes with the result of the async function</returns>
        Task<T> ExecuteAsyncOnEditorThreadAsync<T>(Func<Task<T>> asyncFunc);
    }
}
