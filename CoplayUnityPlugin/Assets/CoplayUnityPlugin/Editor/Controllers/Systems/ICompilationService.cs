using System.Threading;
using System.Threading.Tasks;

namespace Coplay.Controllers.Systems
{
    public interface ICompilationService
    {
        bool IsCompilationSkipped(string message);
        Task<string> CreateScriptWithCompilationCheck(string relativePath, string content);
        Task<string> CheckCompilationErrors(CancellationToken cancellationToken);
        Task WaitForScriptCompilation(CancellationToken cancellationToken);
    }
}
