/**
 * API Error Types
 * 
 * Type-safe error interfaces for API error handling
 */

/**
 * Base interface for errors with a type property
 */
export interface TypedError extends Error {
  type: string;
}

/**
 * NeverBounce throttle error interface
 * Used for rate limiting scenarios
 */
export interface ThrottleError extends TypedError {
  type: "ThrottleError";
  message: string;
}

/**
 * Factory function to create a ThrottleError
 */
export function createThrottleError(message: string = "Rate limit exceeded"): ThrottleError {
  const error = new Error(message) as ThrottleError;
  error.type = "ThrottleError";
  return error;
}

/**
 * Type guard to check if an error is a ThrottleError
 */
export function isThrottleError(error: unknown): error is ThrottleError {
  return error instanceof Error &&
    'type' in error &&
    error.type === "ThrottleError";
}

/**
 * Generic API error interface for consistent error handling
 */
export interface ApiError extends TypedError {
  type: string;
  statusCode?: number;
  details?: Record<string, unknown>;
}

/**
 * Factory function to create an ApiError
 */
export function createApiError(
  type: string,
  message: string,
  statusCode?: number,
  details?: Record<string, unknown>
): ApiError {
  const error = new Error(message) as ApiError;
  error.type = type;
  error.statusCode = statusCode;
  error.details = details;
  return error;
}
