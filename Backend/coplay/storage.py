import base64
from datetime import datetime, timezone
from io import BufferedReader, FileIO
import logging
from pathlib import Path
from typing import Union
import uuid

from storage3.exceptions import StorageApiError
from supabase import AsyncClient

from coplay.constants import SUPABASE_BUCKET

logger = logging.getLogger(__name__)


async def save_b64_to_supabase(data_uri: str, supabase_client: AsyncClient) -> str:
    """
    Upload base64 image data to Supabase Storage and return a signed URL.

    Args:
        data_uri: Base64 data URI (e.g., "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")

    Returns:
        Signed URL that expires in 1 hour
    """
    try:
        # Parse the data URI to extract the base64 data
        if not data_uri.startswith("data:image/"):
            raise ValueError("Invalid data URI format")

        # Extract the base64 data (after the comma)
        base64_data = data_uri.split(",")[1]
        image_bytes = base64.b64decode(base64_data)

        # Generate a unique filename with timestamp
        now = datetime.now(timezone.utc)
        date_path = now.strftime("%Y/%m/%d")
        filename = f"{uuid.uuid4()}.png"
        full_path = f"generated-images/{date_path}/{filename}"

        # Upload to the target bucket (e.g. "coplay-prod" or a local bucket)
        try:
            result = await supabase_client.storage.from_(SUPABASE_BUCKET).upload(
                path=full_path,
                file=image_bytes,
                file_options={"content-type": "image/png"}
            )
        except StorageApiError as e:
            logger.error(f"Supabase storage upload error: {str(e)}")
            raise Exception(f"Upload failed: {str(e)}")

        # Handle old SDK (returns dict with `error`) and new SDK (raises StorageApiError and returns UploadResponse without `error`)
        upload_error = getattr(result, "error", None)
        if upload_error:
            logger.error(f"Failed to upload image to Supabase: {result.error}")
            raise Exception(f"Upload failed: {result.error}")

        # Create a signed URL that expires in 180 days (15,552,000 seconds)
        try:
            signed_url_result = await supabase_client.storage.from_(SUPABASE_BUCKET).create_signed_url(
                path=full_path,
                expires_in=15552000
            )
        except StorageApiError as e:
            logger.error(f"Supabase storage signed URL error: {str(e)}")
            raise Exception(f"Signed URL creation failed: {str(e)}")

        signed_url_error = getattr(signed_url_result, "error", None)
        if signed_url_error:
            logger.error(
                f"Failed to create signed URL: {signed_url_result.error}")
            raise Exception(
                f"Signed URL creation failed: {signed_url_result.error}")

        # Extract signed URL from different possible response shapes
        if isinstance(signed_url_result, dict):
            url = signed_url_result.get(
                "signedURL") or signed_url_result.get("signed_url")
        else:
            url = getattr(signed_url_result, "signed_url", None)

        if not url:
            logger.error("Signed URL missing in Supabase response")
            raise Exception("Signed URL missing in Supabase response")

        logger.info(f"Successfully uploaded image to Supabase: {full_path}")
        return url

    except Exception as e:
        logger.error(f"Error in save_b64_to_supabase: {str(e)}")
        raise


async def upload_file_to_supabase(data: Union[BufferedReader, bytes, FileIO, str, Path], user_id: str, key: str, supabase_client: AsyncClient) -> str:
    """
    Upload bytes to Supabase Storage and return the path.
    """
    try:
        result = await supabase_client.storage.from_(SUPABASE_BUCKET).upload(
            path=f"user-uploads/{user_id}/{key}",
            file=data,
            file_options={"content-type": "application/octet-stream"}
        )
    except Exception as e:
        logger.error(f"Error in upload_bytes_to_supabase: {str(e)}")
        raise

    return result.path

async def download_bytes_from_supabase(path: str, supabase_client: AsyncClient) -> bytes:
    """
    Download bytes from Supabase Storage and return the bytes.
    """
    try:
        result = await supabase_client.storage.from_(SUPABASE_BUCKET).download(path)
    except Exception as e:
        logger.error(f"Error in download_bytes_from_supabase: {str(e)}")
        raise
    
    return result