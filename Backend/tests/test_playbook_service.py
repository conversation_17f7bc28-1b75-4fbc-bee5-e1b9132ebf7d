"""
Unit tests for playbook service functionality.
"""

import pytest
from unittest.mock import patch, MagicMock

from coplay.playbooks.playbook_service import (
    get_playbook_from_keyword,
    _extract_description_from_content,
    _get_playbooks_cached,
    CacheEntry,
    _PLAYBOOK_DIR,
    <PERSON><PERSON><PERSON>BO<PERSON>_EXTENSION,
    DESCRIPTION_MARKER,
    BODY_MARKER
)


class TestGetPlaybookFromKeyword:
    """Test the get_playbook_from_keyword function."""

    @patch('coplay.playbooks.playbook_service._PLAYBOOK_DIR')
    def test_get_existing_playbook(self, mock_playbook_dir):
        """Test retrieving an existing playbook."""
        # Setup mock path
        mock_path = MagicMock()
        mock_path.exists.return_value = True
        mock_path.read_text.return_value = "Sample playbook content"
        mock_playbook_dir.__truediv__.return_value = mock_path
        
        result = get_playbook_from_keyword("test_playbook")
        assert result == "Sample playbook content"
        mock_path.read_text.assert_called_once_with(encoding='utf-8')

    def test_empty_keyword_raises_error(self):
        """Test error handling for empty keyword."""
        with pytest.raises(ValueError) as exc_info:
            get_playbook_from_keyword("")
        assert "cannot be empty" in str(exc_info.value).lower()

    def test_whitespace_keyword_raises_error(self):
        """Test error handling for whitespace-only keyword."""
        with pytest.raises(ValueError) as exc_info:
            get_playbook_from_keyword("   \t\n  ")
        assert "cannot be empty" in str(exc_info.value).lower()

    @patch('coplay.playbooks.playbook_service._PLAYBOOK_DIR')
    def test_path_traversal_sanitization(self, mock_playbook_dir):
        """Test that path traversal attempts are sanitized."""
        mock_path = MagicMock()
        mock_path.exists.return_value = True
        mock_path.read_text.return_value = "Safe content"
        mock_playbook_dir.__truediv__.return_value = mock_path
        
        # Try path traversal attack
        result = get_playbook_from_keyword("../../../etc/passwd")
        
        # Should have sanitized the keyword
        mock_playbook_dir.__truediv__.assert_called_once_with("......etcpasswd.txt")

    @patch('coplay.playbooks.playbook_service._PLAYBOOK_DIR')
    def test_get_nonexistent_playbook(self, mock_playbook_dir):
        """Test error handling for non-existent playbook."""
        mock_path = MagicMock()
        mock_path.exists.return_value = False
        mock_playbook_dir.__truediv__.return_value = mock_path
        
        with pytest.raises(FileNotFoundError) as exc_info:
            get_playbook_from_keyword("nonexistent")
        
        assert "not found" in str(exc_info.value)
        assert "nonexistent" in str(exc_info.value)

    @patch('coplay.playbooks.playbook_service._PLAYBOOK_DIR')
    def test_playbook_read_error(self, mock_playbook_dir):
        """Test error handling for file read errors."""
        mock_path = MagicMock()
        mock_path.exists.return_value = True
        mock_path.read_text.side_effect = IOError("Permission denied")
        mock_playbook_dir.__truediv__.return_value = mock_path
        
        with pytest.raises(IOError) as exc_info:
            get_playbook_from_keyword("test_playbook")
        
        assert "Error reading playbook file" in str(exc_info.value)
        assert "test_playbook" in str(exc_info.value)


class TestExtractDescriptionFromContent:
    """Test the _extract_description_from_content helper function."""

    def test_extract_with_description_and_body_markers(self):
        """Test extraction with proper DESCRIPTION: and BODY: markers."""
        content = """DESCRIPTION:
        This is a test description
        with multiple lines.
        BODY:
        This is the body content."""
        
        result = _extract_description_from_content(content)
        expected = "This is a test description with multiple lines."
        assert result == expected

    def test_extract_with_description_no_body_marker(self):
        """Test extraction with DESCRIPTION: but no BODY: marker."""
        content = """DESCRIPTION:
        This is a test description
        without a body marker."""
        
        result = _extract_description_from_content(content)
        expected = "This is a test description without a body marker."
        assert result == expected

    def test_extract_without_markers_uses_first_line(self):
        """Test fallback to first line when no markers present."""
        content = """First line should be used as description.
        Second line should be ignored."""
        
        result = _extract_description_from_content(content)
        assert result == "First line should be used as description."

    def test_extract_empty_content(self):
        """Test handling of empty content."""
        result = _extract_description_from_content("")
        assert result == "No content available"

    def test_extract_whitespace_only_content(self):
        """Test handling of whitespace-only content."""
        result = _extract_description_from_content("   \n  \t  ")
        assert result == "No description available"


class TestPlaybookValidation:
    """Test that actual playbook files exist and have correct format."""

    def test_playbook_directory_exists_and_has_playbooks(self):
        """Test that the playbook directory exists and contains at least one playbook."""
        assert _PLAYBOOK_DIR.exists(), f"Playbook directory does not exist: {_PLAYBOOK_DIR}"
        assert _PLAYBOOK_DIR.is_dir(), f"Playbook path is not a directory: {_PLAYBOOK_DIR}"
        
        # Get all .txt files in the playbook directory
        playbook_files = list(_PLAYBOOK_DIR.glob(f"*{PLAYBOOK_EXTENSION}"))
        
        assert len(playbook_files) > 0, f"No playbook files found in {_PLAYBOOK_DIR}"
        
        print(f"Found {len(playbook_files)} playbook files: {[f.name for f in playbook_files]}")

    def test_all_playbooks_have_valid_format(self):
        """Test that all playbook files in the directory have valid format."""
        if not _PLAYBOOK_DIR.exists():
            pytest.skip(f"Playbook directory does not exist: {_PLAYBOOK_DIR}")
        
        playbook_files = list(_PLAYBOOK_DIR.glob(f"*{PLAYBOOK_EXTENSION}"))
        
        if not playbook_files:
            pytest.skip(f"No playbook files found in {_PLAYBOOK_DIR}")
        
        invalid_files = []
        
        for playbook_file in playbook_files:
            try:
                # Read the playbook content
                content = playbook_file.read_text(encoding='utf-8')
                
                # Basic format validation
                if not content.strip():
                    invalid_files.append(f"{playbook_file.name}: File is empty")
                    continue
                
                # Check if file has proper structure
                has_description_marker = DESCRIPTION_MARKER in content
                has_body_marker = BODY_MARKER in content
                
                # If it has description marker, it should have proper structure
                if has_description_marker:
                    desc_start = content.find(DESCRIPTION_MARKER)
                    if has_body_marker:
                        body_start = content.find(BODY_MARKER)
                        if body_start <= desc_start:
                            invalid_files.append(f"{playbook_file.name}: BODY marker appears before DESCRIPTION marker")
                            continue
                        
                        # Check that there's content in description section
                        desc_content = content[desc_start + len(DESCRIPTION_MARKER):body_start].strip()
                        if not desc_content:
                            invalid_files.append(f"{playbook_file.name}: Empty description section")
                        
                        # Check that there's content in body section
                        body_content = content[body_start + len(BODY_MARKER):].strip()
                        if not body_content:
                            invalid_files.append(f"{playbook_file.name}: Empty body section")
                    else:
                        # Has description but no body - check description has content
                        desc_content = content[desc_start + len(DESCRIPTION_MARKER):].strip()
                        if not desc_content:
                            invalid_files.append(f"{playbook_file.name}: Empty description section")
                
                # Test that the description extraction works without errors
                try:
                    description = _extract_description_from_content(content)
                    if not description or description.strip() == "":
                        invalid_files.append(f"{playbook_file.name}: Could not extract valid description")
                except Exception as e:
                    invalid_files.append(f"{playbook_file.name}: Error extracting description: {str(e)}")
                
                print(f"✓ {playbook_file.name}: Valid format")
                
            except Exception as e:
                invalid_files.append(f"{playbook_file.name}: Error reading file: {str(e)}")
        
        # Assert that no invalid files were found
        if invalid_files:
            error_message = "Found playbook files with invalid format:\n" + "\n".join(f"  - {error}" for error in invalid_files)
            pytest.fail(error_message)


class TestGetPlaybooksCached:
    """Test the _get_playbooks_cached function with caching."""

    def setup_method(self):
        """Reset global cache before each test."""
        import coplay.playbooks.playbook_service
        coplay.playbooks.playbook_service._cache = None

    @patch('coplay.playbooks.playbook_service._get_directory_signature')
    @patch('coplay.playbooks.playbook_service._scan_playbooks')
    def test_get_playbooks_cached_first_call(self, mock_scan, mock_signature):
        """Test first call to cached function."""
        mock_signature.return_value = 123.45
        mock_scan.return_value = {"test": "description"}
        
        result = _get_playbooks_cached()
        
        assert result == {"test": "description"}
        mock_scan.assert_called_once()
        mock_signature.assert_called_once()

    @patch('coplay.playbooks.playbook_service._get_directory_signature')
    @patch('coplay.playbooks.playbook_service._scan_playbooks')
    def test_get_playbooks_cached_uses_cache(self, mock_scan, mock_signature):
        """Test that subsequent calls use cache when directory unchanged."""
        mock_signature.return_value = 123.45
        mock_scan.return_value = {"test": "description"}
        
        # First call
        result1 = _get_playbooks_cached()
        # Second call
        result2 = _get_playbooks_cached()
        
        assert result1 == result2 == {"test": "description"}
        # Should only call scan once due to caching
        assert mock_scan.call_count == 1
        # Signature should be checked on both calls
        assert mock_signature.call_count == 2

    @patch('coplay.playbooks.playbook_service._get_directory_signature')
    @patch('coplay.playbooks.playbook_service._scan_playbooks')
    def test_get_playbooks_cached_refreshes_on_change(self, mock_scan, mock_signature):
        """Test that cache refreshes when directory signature changes."""
        mock_signature.side_effect = [123.45, 678.90]
        mock_scan.side_effect = [{"test1": "desc1"}, {"test2": "desc2"}]
        
        # First call
        result1 = _get_playbooks_cached()
        # Second call with changed signature
        result2 = _get_playbooks_cached()
        
        assert result1 == {"test1": "desc1"}
        assert result2 == {"test2": "desc2"}
        # Should call scan twice due to signature change
        assert mock_scan.call_count == 2

    @patch('coplay.playbooks.playbook_service._get_directory_signature')
    @patch('coplay.playbooks.playbook_service._scan_playbooks')
    def test_cache_entry_structure(self, mock_scan, mock_signature):
        """Test that cache uses CacheEntry namedtuple structure."""
        mock_signature.return_value = 999.99
        mock_scan.return_value = {"cached": "playbook"}
        
        # Make the call to populate cache
        _get_playbooks_cached()
        
        # Access the global cache directly
        import coplay.playbooks.playbook_service
        cache = coplay.playbooks.playbook_service._cache
        
        # Verify it's a CacheEntry with correct structure
        assert isinstance(cache, CacheEntry)
        assert cache.playbooks == {"cached": "playbook"}
        assert cache.signature == 999.99


if __name__ == "__main__":
    pytest.main([__file__])
