from enum import Enum

from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event
from supabase_auth.types import User
from langsmith import RunTree

from .bigquery.usage import insert_run_data
from .bigquery.tool_call_results import insert_tool_call_result_data
from .supabase_db import consume_user_credits
from .notifications import DiscordNotifier
from .models import CoplayRun, Prompt, ToolCallResult
from .langsmith_url_service import get_langsmith_trace_url
from .logger import logger


class CoplayEvent(Enum):
    LANGCHAIN_RUN_START = "langchain.run.start"
    USAGE_DATA_UPDATE = "coplay.usage.update"
    TOOL_CALL_RESULT = "coplay.tool_call_result"


@local_handler.register(event_name=CoplayEvent.LANGCHAIN_RUN_START)
async def langchain_run_start_handler(event: Event):
    run: RunTree = event[1]
    prompt: Prompt = run.inputs.get("prompt")

    # Only log root traces if they have a prompt (i.e. human message)
    if run.parent_run_id is None and prompt.prompt:
        run_url = run.get_url()
        logger.info(f"Langsmith run_url: {run_url}")
        thread_id = run.metadata.get("thread_id")
        trace_url = ""
        if thread_id:
            trace_url = await get_langsmith_trace_url(thread_id)
            logger.info(f"Langsmith thread_url: {trace_url}")
        model = prompt.model if prompt.model else None
        coplay_version = run.metadata.get("coplay_version")
        user: User = run.inputs.get("user")
        turn = run.metadata.get("turn")
        user_id = user.id if user else None
        user_email = user.email if user else None
        await DiscordNotifier.send_user_prompt_to_discord(run_url, trace_url, prompt.assistant_mode, prompt.prompt, turn, user_email, model, coplay_version, user_id)


@local_handler.register(event_name=CoplayEvent.USAGE_DATA_UPDATE)
async def usage_update_handler(event: Event):
    run: CoplayRun = event[1]

    logger.info(f"Usage data: {run}")

    if run.usage is None:
        logger.info(f"Usage data is None for run {run.id}")
        return
    
    # Going forward we always consume credits, even for paying users. There is no unlimited plan.
    credit_balance = await consume_user_credits(run.user_id, run.usage.cost_usd)
    if credit_balance is not None:
        total_balance = credit_balance.total
        logger.info(f"User {run.user_id} has ${total_balance:.4f} USD credits remaining (subscription: ${credit_balance.subscription_credits:.4f}, topup: ${credit_balance.topup_credits:.4f})")
        run.usage.credit_balance = total_balance
    else:
        logger.error(f"Failed to consume credits for user {run.user_id}")
    
    await insert_run_data(run)

@local_handler.register(event_name=CoplayEvent.TOOL_CALL_RESULT)
async def tool_call_result_handler(event: Event):
    tool_call_result: ToolCallResult = event[1]
    await insert_tool_call_result_data(tool_call_result)