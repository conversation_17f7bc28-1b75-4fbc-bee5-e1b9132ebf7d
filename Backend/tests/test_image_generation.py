from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from coplay.image_functions import process_image_functions
from coplay.imagen_generate_image import imagen_generate_image
from coplay.models import ImageFunctionsRequest
from coplay.storage import save_b64_to_supabase


@pytest.fixture
def mock_supabase_client():
    """Return a bare-minimum AsyncMock that mimics Supabase storage client."""
    mock_storage_bucket = MagicMock()
    mock_storage_bucket.upload = AsyncMock(return_value=MagicMock(error=None))
    mock_storage_bucket.create_signed_url = AsyncMock(return_value=MagicMock(
        error=None, signed_url="https://supabase.example.com/url"))

    mock_storage = MagicMock()
    mock_storage.from_ = MagicMock(return_value=mock_storage_bucket)

    client = AsyncMock()
    client.storage = mock_storage
    return client


async def test_save_b64_to_supabase_success(mock_supabase_client):
    data_uri = (
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    )

    # Arrange mock storage behaviour
    bucket = mock_supabase_client.storage.from_.return_value
    bucket.upload.return_value = MagicMock(error=None)
    bucket.create_signed_url.return_value = MagicMock(
        error=None, signed_url="https://supabase.example.com/url")

    url = await save_b64_to_supabase(data_uri, mock_supabase_client)
    assert url == "https://supabase.example.com/url"
    bucket.upload.assert_called_once()
    bucket.create_signed_url.assert_called_once()


async def test_save_b64_to_supabase_invalid_data_uri(mock_supabase_client):
    with pytest.raises(ValueError):
        await save_b64_to_supabase("not_a_data_uri", mock_supabase_client)


@patch("coplay.imagen_generate_image.genai.Client")
async def test_imagen_generate_image_success(mock_client_class):
    mock_client = AsyncMock()
    mock_client_class.return_value = mock_client

    mock_resp = MagicMock()
    mock_img = MagicMock()
    mock_img.image_bytes = b"fake_image_data"
    mock_resp.generated_images = [MagicMock(image=mock_img)]
    mock_client.aio.models.generate_images.return_value = mock_resp

    req = ImageFunctionsRequest(
        is_edit=False, prompt="A beautiful sunset", provider="imagen")
    result = await imagen_generate_image(req)

    assert result.startswith("data:image/png;base64,")
    assert "ZmFrZV9pbWFnZV9kYXRh" in result  # base64 of fake_image_data
    mock_client.aio.models.generate_images.assert_called_once()


@patch("coplay.image_functions.AsyncOpenAI")
async def test_process_image_functions_gpt(mock_openai_class, mock_supabase_client):
    mock_openai = AsyncMock()
    mock_openai_class.return_value = mock_openai

    # Create pseudo-stream of events
    ev1 = MagicMock()
    ev1.type = "response.image_generation_call.partial_image"
    ev1.partial_image_index = 1
    ev1.partial_image_b64 = "chunk_b64"
    ev2 = MagicMock()
    ev2.type = "response.image_generation_call.completed"

    stream = AsyncMock()
    stream.__aiter__.return_value = [ev1, ev2]
    mock_openai.responses.create.return_value = stream

    req = ImageFunctionsRequest(
        is_edit=False, prompt="Sunset", provider="gpt_image_1")
    chunks = [c async for c in process_image_functions(req, mock_supabase_client)]
    # final chunk + auto cropped chunk
    assert chunks == ["data:image/png;base64,chunk_b64", "data:image/png;base64,chunk_b64"]


@patch("coplay.image_functions.imagen_generate_image")
@patch("coplay.image_functions.save_b64_to_supabase")
async def test_process_image_functions_imagen(mock_save_b64, mock_imagen_gen, mock_supabase_client):
    mock_imagen_gen.return_value = "data:image/png;base64,fake"

    req = ImageFunctionsRequest(
        is_edit=False, prompt="Sunset", provider="imagen")
    chunks = [c async for c in process_image_functions(req, mock_supabase_client)]
    assert chunks == ["data:image/png;base64,fake"]

    mock_imagen_gen.assert_called_once_with(req)
