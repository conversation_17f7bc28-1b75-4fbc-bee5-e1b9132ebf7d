using System.IO;
using Coplay.Common.CoplayApi;
using Coplay.Common.Logging.Sinks.CoplayApi;
using Coplay.Common.Logging.Sinks.Unity;
using Coplay.Services;
using Coplay.Services.Chat;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace Coplay.Common.Logging
{
    public static class CoplayLoggerConfiguration
    {
        /// <summary>
        /// Configures the logger to use the Coplay API sink.
        /// </summary>
        /// <param name="services">The service collection to configure.</param>
        /// <returns>The updated service collection.</returns>
        public static IServiceCollection AddCoplayLogging(
            this IServiceCollection services)
        {
            return services
                .AddLogging()
                .AddSerilog((provider, configuration) =>
                {
                    var applicationPaths = provider.GetRequiredService<IApplicationPaths>();
                    configuration
                        .MinimumLevel.Debug()
                        .Enrich.FromLogContext()
                        .WriteTo.Async(a => a.File(Path.Combine(applicationPaths.DefaultLogsFolderPath, "console_logs_.log"), rollingInterval: RollingInterval.Day, retainedFileCountLimit: 7))
                        .WriteTo.CoplayApi(provider.GetRequiredService<ICoplayApiClient>(), provider.GetRequiredService<ICoplayThreadContext>());

#if !DISABLE_CONSOLE_LOGGING
                    configuration.WriteTo.UnityLog();
#endif
                });
        }
    }
}
