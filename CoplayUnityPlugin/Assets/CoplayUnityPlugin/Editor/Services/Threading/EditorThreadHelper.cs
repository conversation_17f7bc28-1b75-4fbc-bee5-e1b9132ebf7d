using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEditor;
using Microsoft.Extensions.Logging;

namespace Coplay.Services.Threading
{
    /// <summary>
    /// Helper for executing actions on the Unity editor thread
    /// </summary>
    public class EditorThreadHelper : IEditorThreadHelper
    {
        private readonly ILogger<EditorThreadHelper> _logger;
        private readonly IEditorStateService _editorStateService;
        private readonly Queue<(Action action, TaskCompletionSource<bool> completion)> _actionQueue =
            new Queue<(Action action, TaskCompletionSource<bool> completion)>();
        private readonly Queue<(Func<object> function, TaskCompletionSource<object> completion)> _funcQueue = 
            new Queue<(Func<object> function, TaskCompletionSource<object> completion)>();
        private readonly object _lockObject = new object();
        private bool _isDisposed;

        /// <summary>
        /// Initializes the editor thread helper
        /// </summary>
        public EditorThreadHelper(ILogger<EditorThreadHelper> logger, IEditorStateService editorStateService)
        {
            _logger = logger;
            _editorStateService = editorStateService;

            _logger.LogInformation("Initializing EditorThreadHelper");
            EditorApplication.update += Update;
            _logger.LogInformation("EditorThreadHelper initialized successfully");
        }

        /// <summary>
        /// Called during the editor update
        /// </summary>
        private void Update()
        {
            if (_isDisposed)
                return;

            // Process all queued actions
            lock (_lockObject)
            {
                // Process actions
                while (_actionQueue.Count > 0)
                {
                    var (action, completion) = _actionQueue.Dequeue();
                    try
                    {
                        action();
                        completion.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error executing action on editor thread: {ex.Message}");
                        completion.SetException(ex);
                    }
                }
                
                // Process functions
                while (_funcQueue.Count > 0)
                {
                    var (function, completion) = _funcQueue.Dequeue();
                    try
                    {
                        var result = function();
                        completion.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error executing function on editor thread: {ex.Message}");
                        completion.SetException(ex);
                    }
                }
            }
        }

        /// <summary>
        /// Executes an action on the editor thread
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <returns>A task that completes when the action is executed</returns>
        public Task ExecuteOnEditorThreadAsync(Action action)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EditorThreadHelper));

            if (action == null)
                return Task.CompletedTask;

            // If we're already on the main thread and in edit mode, execute immediately
            if (IsMainThread() && !_editorStateService.IsPlayingOrWillChangePlaymode)
            {
                action();
                return Task.CompletedTask;
            }

            // Otherwise, queue the action
            var tcs = new TaskCompletionSource<bool>();
            
            lock (_lockObject)
            {
                _actionQueue.Enqueue((action, tcs));
            }
            
            return tcs.Task;
        }

        /// <summary>
        /// Executes a function on the editor thread and returns the result
        /// </summary>
        /// <typeparam name="T">The return type of the function</typeparam>
        /// <param name="func">The function to execute</param>
        /// <returns>A task that completes with the result of the function</returns>
        public Task<T> ExecuteOnEditorThreadAsync<T>(Func<T> func)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EditorThreadHelper));

            if (func == null)
                return Task.FromResult(default(T));

            // If we're already on the main thread and in edit mode, execute immediately
            if (IsMainThread() && !_editorStateService.IsPlayingOrWillChangePlaymode)
            {
                return Task.FromResult(func());
            }

            // Otherwise, queue the function
            var tcs = new TaskCompletionSource<object>();

            lock (_lockObject)
            {
                _funcQueue.Enqueue((() => func(), tcs));
            }

            return tcs.Task.ContinueWith(t => (T)t.Result);
        }

        /// <summary>
        /// Executes an async function on the editor thread and returns the result
        /// </summary>
        /// <typeparam name="T">The return type of the async function</typeparam>
        /// <param name="asyncFunc">The async function to execute</param>
        /// <returns>A task that completes with the result of the async function</returns>
        public async Task<T> ExecuteAsyncOnEditorThreadAsync<T>(Func<Task<T>> asyncFunc)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EditorThreadHelper));

            if (asyncFunc == null)
                return default(T);

            // If we're already on the main thread and in edit mode, execute immediately
            if (IsMainThread() && !_editorStateService.IsPlayingOrWillChangePlaymode)
            {
                return await asyncFunc();
            }

            // Otherwise, we need to marshal the async operation to the main thread
            var tcs = new TaskCompletionSource<T>();

            // Queue an action that will start the async operation on the main thread
            await ExecuteOnEditorThreadAsync(() =>
            {
                // Start the async operation on the main thread and handle its completion
                asyncFunc().ContinueWith(task =>
                {
                    if (task.IsFaulted)
                    {
                        tcs.SetException(task.Exception?.InnerException ?? new Exception("Unknown error"));
                    }
                    else if (task.IsCanceled)
                    {
                        tcs.SetCanceled();
                    }
                    else
                    {
                        tcs.SetResult(task.Result);
                    }
                }, TaskScheduler.FromCurrentSynchronizationContext());
            });

            return await tcs.Task;
        }
        
        /// <summary>
        /// Checks if the current thread is the main thread
        /// </summary>
        /// <returns>True if the current thread is the main thread, false otherwise</returns>
        private static bool IsMainThread()
        {
            return System.Threading.Thread.CurrentThread.ManagedThreadId == 1;
        }

        /// <summary>
        /// Disposes the editor thread helper
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;

            EditorApplication.update -= Update;
            _isDisposed = true;
            
            // Complete any remaining tasks with cancellation
            lock (_lockObject)
            {
                while (_actionQueue.Count > 0)
                {
                    var (_, completion) = _actionQueue.Dequeue();
                    completion.SetCanceled();
                }
                
                while (_funcQueue.Count > 0)
                {
                    var (_, completion) = _funcQueue.Dequeue();
                    completion.SetCanceled();
                }
            }
            
            _logger.LogInformation("EditorThreadHelper disposed");
        }
    }
}
