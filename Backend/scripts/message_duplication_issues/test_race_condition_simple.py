#!/usr/bin/env python3
"""
This script is related to cases where we try to prevent OpenAI models resulting in duplicate messages in the user's UI.

Simplified race condition reproduction focusing on the core issue.

The race condition happens because:
1. The background task rapidly updates BACKGROUND_TASK_RESULTS[task_id].chunk
2. Each chunk can have a different message_id during the streaming process
3. The polling endpoint returns whatever chunk is currently stored
4. If timing is unlucky, the test sees chunks with different message_ids

This is most likely to happen when:
- The AI model generates multiple message chunks with different IDs
- The background task updates the shared state between polls
- CI environments have variable timing that increases the window for this race
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the Backend directory to the Python path so we can import coplay modules
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

import pytest
from unittest.mock import AsyncMock, patch
from coplay.models import PromptResponse, AIModel, AssistantMode
from coplay.jobs import BACKGROUND_TASK_RESULTS
from coplay.models import CoplayTask


def test_race_condition_simulation():
    """
    Simulate the race condition by manually manipulating the BACKGROUND_TASK_RESULTS
    to show how different message IDs can be seen during polling.
    """
    task_id = "test_device_race_condition_123"
    
    # Simulate what happens in the streaming process
    # First, a chunk with reasoning run ID appears
    reasoning_chunk = PromptResponse(
        type="message",
        thread_id="test_thread",
        task_id=task_id,
        message_id="run--9cf8def4-9f31-4728-b3c2-f9d53be856f3",  # Reasoning ID
        message="Thinking about the problem...",
        finished=False
    )
    
    # Then, a chunk with actual message ID appears
    message_chunk = PromptResponse(
        type="message", 
        thread_id="test_thread",
        task_id=task_id,
        message_id="msg_689e3f23e6c4819985f2ec698da714920a5471005de68d2d",  # Actual message ID
        message="Here's my response...",
        finished=False
    )
    
    # Final chunk
    final_chunk = PromptResponse(
        type="message",
        thread_id="test_thread", 
        task_id=task_id,
        message_id="msg_689e3f23e6c4819985f2ec698da714920a5471005de68d2d",  # Same as message
        message="Complete response here.",
        finished=True
    )
    
    # Simulate the race condition scenario
    BACKGROUND_TASK_RESULTS[task_id] = CoplayTask(
        chunk=reasoning_chunk,
        cancel_scope=asyncio.Event()
    )
    
    # Test sees reasoning chunk first
    seen_ids = set()
    if BACKGROUND_TASK_RESULTS[task_id].chunk.message_id:
        seen_ids.add(BACKGROUND_TASK_RESULTS[task_id].chunk.message_id)
    
    print(f"First poll - Message ID: {BACKGROUND_TASK_RESULTS[task_id].chunk.message_id}")
    
    # Background task updates to message chunk
    BACKGROUND_TASK_RESULTS[task_id].chunk = message_chunk
    
    # Test polls again and sees different message ID
    if BACKGROUND_TASK_RESULTS[task_id].chunk.message_id:
        seen_ids.add(BACKGROUND_TASK_RESULTS[task_id].chunk.message_id)
    
    print(f"Second poll - Message ID: {BACKGROUND_TASK_RESULTS[task_id].chunk.message_id}")
    
    # Background task updates to final chunk
    BACKGROUND_TASK_RESULTS[task_id].chunk = final_chunk
    
    # Test polls final time
    if BACKGROUND_TASK_RESULTS[task_id].chunk.message_id:
        seen_ids.add(BACKGROUND_TASK_RESULTS[task_id].chunk.message_id)
    
    print(f"Final poll - Message ID: {BACKGROUND_TASK_RESULTS[task_id].chunk.message_id}")
    print(f"All message IDs seen: {seen_ids}")
    
    # This demonstrates the race condition - multiple message IDs seen
    assert len(seen_ids) > 1, f"Expected multiple message IDs, got: {seen_ids}"
    print("✅ Race condition successfully simulated!")
    
    # Clean up
    del BACKGROUND_TASK_RESULTS[task_id]


async def test_concurrent_polling_race():
    """
    Test concurrent polling to simulate the actual race condition timing.
    """
    task_id = "test_device_concurrent_123"
    
    # Set up initial state
    BACKGROUND_TASK_RESULTS[task_id] = CoplayTask(
        chunk=PromptResponse(
            type="message",
            thread_id="test_thread",
            task_id=task_id,
            message_id="run--initial-reasoning-id",
            message="Initial reasoning...",
            finished=False
        ),
        cancel_scope=asyncio.Event()
    )
    
    seen_message_ids = set()
    
    async def background_updater():
        """Simulate the background task updating chunks rapidly"""
        await asyncio.sleep(0.01)  # Small delay
        
        # Update to reasoning chunk
        BACKGROUND_TASK_RESULTS[task_id].chunk = PromptResponse(
            type="message",
            thread_id="test_thread", 
            task_id=task_id,
            message_id="run--reasoning-process-123",
            message="Reasoning in progress...",
            finished=False
        )
        
        await asyncio.sleep(0.01)
        
        # Update to message chunk
        BACKGROUND_TASK_RESULTS[task_id].chunk = PromptResponse(
            type="message",
            thread_id="test_thread",
            task_id=task_id, 
            message_id="msg_actual_response_456",
            message="Actual response content...",
            finished=False
        )
        
        await asyncio.sleep(0.01)
        
        # Final update
        BACKGROUND_TASK_RESULTS[task_id].chunk = PromptResponse(
            type="message",
            thread_id="test_thread",
            task_id=task_id,
            message_id="msg_actual_response_456", 
            message="Complete response.",
            finished=True
        )
    
    async def poller():
        """Simulate the test polling the endpoint"""
        for i in range(10):  # Poll 10 times
            if task_id in BACKGROUND_TASK_RESULTS:
                chunk = BACKGROUND_TASK_RESULTS[task_id].chunk
                if chunk.message_id:
                    seen_message_ids.add(chunk.message_id)
                    print(f"Poll {i+1}: {chunk.message_id}")
                
                if chunk.finished:
                    break
            
            await asyncio.sleep(0.005)  # Poll every 5ms
    
    # Run both concurrently
    await asyncio.gather(background_updater(), poller())
    
    print(f"Concurrent test - All message IDs seen: {seen_message_ids}")
    
    # Clean up
    if task_id in BACKGROUND_TASK_RESULTS:
        del BACKGROUND_TASK_RESULTS[task_id]
    
    if len(seen_message_ids) > 1:
        print("✅ Concurrent race condition reproduced!")
        return True
    else:
        print("❌ Concurrent race condition not reproduced")
        return False


if __name__ == "__main__":
    print("=== Testing Race Condition Simulation ===")
    test_race_condition_simulation()
    
    print("\n=== Testing Concurrent Polling Race ===")
    result = asyncio.run(test_concurrent_polling_race())
    
    if result:
        print("\n🎯 RACE CONDITION SUCCESSFULLY REPRODUCED!")
        print("\nThe issue occurs because:")
        print("1. Background task rapidly updates BACKGROUND_TASK_RESULTS[task_id].chunk")
        print("2. Different chunks can have different message_ids (reasoning vs actual)")
        print("3. Test polling can catch the state between updates")
        print("4. This creates the appearance of inconsistent message IDs")
        
        print("\n💡 SOLUTION:")
        print("The streaming logic should ensure message_id consistency by:")
        print("- Filtering out reasoning run IDs (run--*)")
        print("- Only exposing the final message ID to polling clients")
        print("- Or using a separate field for reasoning vs message IDs")
    else:
        print("\n⚠️  Race condition timing may vary - try running multiple times")
