using System;
using System.Collections.Generic;
using System.Linq;

namespace Coplay.Models
{
    /// <summary>
    /// Enum representing available AI models for use in Coplay
    /// </summary>
    public enum AIModel
    {
        /// <summary>GPT-4o model</summary>
        GPT4o,

        /// <summary>GPT-4.1 model</summary>
        GPT41,

        /// <summary>GPT-5 model</summary>
        GPT5,

        /// <summary>GPT-4o mini model</summary>
        GPT4oMini,

        /// <summary>O3 model</summary>
        O3,

        /// <summary>O4-mini model</summary>
        O4Mini,

        /// <summary>Claude 3.5 Sonnet model</summary>
        Claude35Sonnet,

        /// <summary>Claude 3.7 Sonnet model</summary>
        Claude37Sonnet,

        /// <summary>Claude 3.7 Sonnet model with thinking enabled</summary>
        Claude37Thinking,

        /// <summary>Claude 4 Sonnet model</summary>
        Claude4Sonnet,

        /// <summary>Claude 4 Sonnet model with thinking enabled</summary>
        Claude4Thinking,

        /// <summary>Grok-4 model</summary>
        Grok4,
        /// <summary>Grok Code Fast 1 model</summary>
        GrokCodeFast1,

        /// <summary>Gemini 2 Flash model</summary>
        Gemini2Flash,

        /// <summary>Gemini 2.5 Pro model</summary>
        Gemini25Pro,

        /// <summary>Gemini 2.5 Flash model</summary>
        Gemini25Flash,

        /// <summary>Mercury Coder Small model</summary>
        // MercuryCoderSmall,

        /// <summary>Mercury Coder Small Stream model</summary>
        // MercuryCoderSmallStream

        /// <summary>Kimi K2 model</summary>
        KimiK2,
        /// <summary>Qwen 3 Coder model</summary>
        Qwen3Coder,
        /// <summary>GLM 4.5 model</summary>
        ZAI_GLM_4_5,
        /// <summary>LLama 3.3 70B model</summary>
        MetaLlama3,
        /// <summary>GPT-OSS 120B model</summary>
        GPT_OSS_120B,
    }

    /// <summary>
    /// Holds comprehensive information about an AI model including backend ID, display name, and capabilities
    /// </summary>
    public class AIModelInfo
    {
        /// <summary>
        /// The AIModel enum value
        /// </summary>
        public AIModel EnumValue { get; }

        /// <summary>
        /// The backend identifier string
        /// </summary>
        public string BackendId { get; }

        /// <summary>
        /// The user-friendly display name
        /// </summary>
        public string DisplayName { get; }

        /// <summary>
        /// Creates a new AIModelInfo instance
        /// </summary>
        /// <param name="enumValue">The AIModel enum value</param>
        /// <param name="backendId">The backend identifier string</param>
        /// <param name="displayName">The user-friendly display name</param>
        public AIModelInfo(AIModel enumValue, string backendId, string displayName)
        {
            EnumValue = enumValue;
            BackendId = backendId ?? throw new ArgumentNullException(nameof(backendId));
            DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        }
        /// <summary>
        /// All available AI model information in one central location
        /// </summary>
        public static IReadOnlyList<AIModelInfo> All { get; } = new[]
        {
            // OpenAI models
            new AIModelInfo(AIModel.GPT41,       "gpt-4.1",          "OpenAI GPT-4.1"),
            new AIModelInfo(AIModel.GPT5,        "gpt-5",            "OpenAI GPT-5"),
            new AIModelInfo(AIModel.O3,          "o3",               "OpenAI O3"),
            new AIModelInfo(AIModel.O4Mini,      "o4-mini",          "OpenAI O4 Mini"),
            
            // Anthropic models
            new AIModelInfo(AIModel.Claude37Sonnet,   "claude-3-7-sonnet",   "Claude 3.7 Sonnet"),
            new AIModelInfo(AIModel.Claude37Thinking, "claude-3-7-thinking", "Claude 3.7 Thinking"),
            new AIModelInfo(AIModel.Claude4Sonnet,    "claude-4-sonnet",     "Claude 4 Sonnet"),
            new AIModelInfo(AIModel.Claude4Thinking,  "claude-4-thinking",   "Claude 4 Thinking"),
            
            // xAI models
            new AIModelInfo(AIModel.Grok4,       "grok-4",           "Grok-4"),
            new AIModelInfo(AIModel.GrokCodeFast1, "grok-code-fast-1", "Grok Code Fast 1"),
            
            // Google models
            new AIModelInfo(AIModel.Gemini25Pro,   "gemini-2-5-pro",   "Gemini 2.5 Pro"),
            new AIModelInfo(AIModel.Gemini25Flash, "gemini-2-5-flash", "Gemini 2.5 Flash"),
            
            // OpenRouter models (non-streaming)
            new AIModelInfo(AIModel.KimiK2,      "kimi-k2",          "Kimi K2"),
            new AIModelInfo(AIModel.Qwen3Coder,  "qwen3-coder",      "Qwen 3 Coder"),
            new AIModelInfo(AIModel.ZAI_GLM_4_5, "z-ai-glm-4-5",          "Z.AI: GLM 4.5"),
            new AIModelInfo(AIModel.MetaLlama3,  "llama-3-3-70b",    "Meta Llama 3 (Free)"),
            new AIModelInfo(AIModel.GPT_OSS_120B, "gpt-oss-120b",    "OpenAI GPT-OSS 120B")
        };

        /// <summary>
        /// Gets model info by enum value
        /// </summary>
        /// <param name="model">The AIModel enum value</param>
        /// <returns>The corresponding AIModelInfo</returns>
        /// <exception cref="ArgumentException">Thrown when model is not found</exception>
        public static AIModelInfo FromEnum(AIModel model) =>
            All.FirstOrDefault(x => x.EnumValue == model)
            ?? throw new ArgumentException($"Unknown model: {model}");

        /// <summary>
        /// Gets model info by backend ID
        /// </summary>
        /// <param name="id">The backend identifier string</param>
        /// <returns>The corresponding AIModelInfo</returns>
        /// <exception cref="ArgumentException">Thrown when backend ID is not found</exception>
        public static AIModelInfo FromBackendId(string id) =>
            All.FirstOrDefault(x => x.BackendId == id)
            ?? throw new ArgumentException($"Unknown model string: {id}");
    }

    /// <summary>
    /// Extension methods for the AIModel enum
    /// </summary>
    public static class AIModelExtensions
    {
        /// <summary>
        /// List of models that don't support image processing
        /// </summary>
        private static readonly AIModel[] ModelsWithoutImageSupport = new[]
        {
            AIModel.O4Mini,
            AIModel.GrokCodeFast1,
            AIModel.Grok4,
            AIModel.Grok4
        };

        /// <summary>
        /// The default AI model to use when no specific model is specified
        /// </summary>
        public static AIModel DefaultModel => AIModel.Claude4Sonnet;

        /// <summary>
        /// Gets the string representation of the default model
        /// </summary>
        /// <returns>The string representation of the default model</returns>
        public static string DefaultModelString => DefaultModel.ToModelString();

        /// <summary>
        /// Converts an AIModel enum value to its backend string representation
        /// </summary>
        /// <param name="model">The AIModel enum value</param>
        /// <returns>The backend string representation of the model</returns>
        public static string ToModelString(this AIModel model) =>
            AIModelInfo.FromEnum(model).BackendId;

        /// <summary>
        /// Gets the display name for an AIModel enum value
        /// </summary>
        /// <param name="model">The AIModel enum value</param>
        /// <returns>The user-friendly display name of the model</returns>
        public static string ToDisplayName(this AIModel model) =>
            AIModelInfo.FromEnum(model).DisplayName;

        /// <summary>
        /// Converts a backend string representation to an AIModel enum value
        /// </summary>
        /// <param name="modelString">The backend string representation of the model</param>
        /// <returns>The corresponding AIModel enum value</returns>
        public static AIModel ToAIModel(this string modelString) =>
            AIModelInfo.FromBackendId(modelString).EnumValue;

        /// <summary>
        /// Gets all available model backend strings
        /// </summary>
        /// <returns>An array of all available model backend strings</returns>
        public static string[] GetAllModelStrings() =>
            AIModelInfo.All.Select(x => x.BackendId).ToArray();

        /// <summary>
        /// Gets all available AIModel enum values
        /// </summary>
        /// <returns>An array of all available AIModel enum values</returns>
        public static AIModel[] GetAllModels() =>
            AIModelInfo.All.Select(x => x.EnumValue).ToArray();

        /// <summary>
        /// Checks if a model supports image processing
        /// </summary>
        /// <param name="model">The model to check</param>
        /// <returns>True if the model supports image processing, false otherwise</returns>
        public static bool SupportsImageProcessing(this AIModel model)
        {
            return !ModelsWithoutImageSupport.Contains(model);
        }

        /// <summary>
        /// Checks if a model supports image processing
        /// </summary>
        /// <param name="modelString">The backend string representation of the model to check</param>
        /// <returns>True if the model supports image processing, false otherwise</returns>
        public static bool SupportsImageProcessing(string modelString)
        {
            try
            {
                var model = modelString.ToAIModel();
                return model.SupportsImageProcessing();
            }
            catch (ArgumentException)
            {
                // If we can't parse the model string, assume it supports image processing
                return true;
            }
        }
    }
}
