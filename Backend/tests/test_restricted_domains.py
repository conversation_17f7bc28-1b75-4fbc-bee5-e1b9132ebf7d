from coplay.restricted_domains import is_email_domain_restricted, RESTRICTED_EMAIL_DOMAINS


class TestRestrictedDomains:
    """Test cases for the restricted email domains feature."""

    def test_is_email_domain_restricted_with_configured_domains(self):
        """Test domain restriction with the configured restricted domains."""
        # Test the actual configured restricted domains
        restricted_cases = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",  # Test case insensitivity
            "<EMAIL>"
        ]
        
        for email in restricted_cases:
            assert is_email_domain_restricted(email), f"Should restrict: {email}"
        
        # Should not be restricted
        allowed_cases = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",  # Different from unity3d.com
            "<EMAIL>"   # Different from bytedance.com
        ]
        
        for email in allowed_cases:
            assert not is_email_domain_restricted(email), f"Should not restrict: {email}"