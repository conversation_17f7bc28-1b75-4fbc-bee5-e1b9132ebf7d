import logging
from typing import Dict, Optional, NamedTuple
from pathlib import Path
from dataclasses import dataclass


logger = logging.getLogger(__name__)

# Constants
PLAYBOOK_EXTENSION = ".txt"
DESCRIPTION_MARKER = "DESCRIPTION:"
BODY_MARKER = "BODY:"
DEFAULT_DESCRIPTION = "No description available"
DEFAULT_CONTENT_MESSAGE = "No content available"

# Path configuration
_CURRENT_DIR = Path(__file__).parent
_PLAYBOOK_DIR = _CURRENT_DIR / "playbook_strings"


class CacheEntry(NamedTuple):
    """Cache entry structure for playbook data."""
    playbooks: Dict[str, str]
    signature: float


@dataclass(frozen=True)
class PlaybookError:
    """Structured error information for playbook operations."""
    keyword: str
    message: str
    error_type: str


# Global cache
_cache: Optional[CacheEntry] = None


def get_playbook_from_keyword(keyword: str) -> str:
    """
    Retrieve playbook content for a given keyword.
    
    Args:
        keyword: The playbook identifier
        
    Returns:
        The complete playbook content as a string
        
    Raises:
        FileNotFoundError: If the playbook file doesn't exist
        IOError: If there's an error reading the file
        ValueError: If keyword is empty or invalid
    """
    if not keyword or not keyword.strip():
        raise ValueError("Keyword cannot be empty or whitespace")
    
    # Sanitize keyword to prevent path traversal
    keyword = keyword.strip().replace('/', '').replace('\\', '')
    
    playbook_path = _PLAYBOOK_DIR / f"{keyword}{PLAYBOOK_EXTENSION}"
    
    if not playbook_path.exists():
        logger.warning(f"Playbook file not found: {playbook_path}")
        raise FileNotFoundError(f"Playbook file for keyword '{keyword}' not found at: {playbook_path}")
    
    try:
        content = playbook_path.read_text(encoding='utf-8')
        logger.debug(f"Successfully loaded playbook: {keyword}")
        return content
    except IOError as e:
        logger.error(f"Error reading playbook file '{keyword}': {e}")
        raise IOError(f"Error reading playbook file for keyword '{keyword}': {str(e)}") from e


def dynamic_playbook_docstring(func):
    """Decorator that defers any file I/O until __doc__ is actually accessed."""
    return _DocstringCallable(func)


def _get_directory_signature(directory: Path) -> float:
    """
    Calculate directory signature based on modification times.
    
    Args:
        directory: Path to the directory to analyze
        
    Returns:
        Latest modification time in the directory, or -1.0 if directory doesn't exist
    """
    if not directory.exists():
        return -1.0
    
    try:
        latest = directory.stat().st_mtime
        
        # Check all files in directory for their modification times
        for file_path in directory.iterdir():
            try:
                if file_path.is_file():
                    latest = max(latest, file_path.stat().st_mtime)
            except (OSError, PermissionError):
                # Skip files that can't be accessed
                continue
                
        return latest
    except (OSError, PermissionError) as e:
        logger.warning(f"Could not access directory {directory}: {e}")
        return -1.0


def _extract_description_from_content(content: str) -> str:
    """
    Extract description from playbook content.
    
    Args:
        content: Raw playbook content string
        
    Returns:
        Extracted description or default message if not found
    """
    if not content:
        return DEFAULT_CONTENT_MESSAGE
    
    if DESCRIPTION_MARKER in content:
        desc_start = content.find(DESCRIPTION_MARKER) + len(DESCRIPTION_MARKER)
        desc_end = content.find(BODY_MARKER) if BODY_MARKER in content else len(content)
        description = content[desc_start:desc_end].strip()
        # Normalize whitespace
        return ' '.join(description.split()) or DEFAULT_DESCRIPTION
    
    # Fallback to first line if no description marker found
    first_line = content.split('\n', 1)[0].strip()
    return first_line or DEFAULT_DESCRIPTION


def _scan_playbooks() -> Dict[str, str]:
    """
    Scan playbook directory and extract descriptions from all playbook files.
    
    Returns:
        Dictionary mapping playbook names to their descriptions
    """
    if not _PLAYBOOK_DIR.exists():
        logger.info(f"Playbook directory does not exist: {_PLAYBOOK_DIR}")
        return {}
    
    result: Dict[str, str] = {}
    
    try:
        # Use pathlib to iterate over files
        for file_path in _PLAYBOOK_DIR.iterdir():
            if file_path.is_file() and file_path.suffix == PLAYBOOK_EXTENSION:
                name = file_path.stem  # Get filename without extension
                
                try:
                    content = file_path.read_text(encoding="utf-8").strip()
                    result[name] = _extract_description_from_content(content)
                    logger.debug(f"Successfully processed playbook: {name}")
                except OSError as e:
                    error_msg = f"Error reading file: {e}"
                    result[name] = error_msg
                    logger.warning(f"Failed to read playbook '{name}': {e}")
    except OSError as e:
        logger.error(f"Failed to scan playbook directory {_PLAYBOOK_DIR}: {e}")
        return {}
    
    logger.info(f"Scanned {len(result)} playbook files")
    return result


def _get_playbooks_cached() -> Dict[str, str]:
    """
    Get playbooks with caching based on directory modification time.
    
    Returns:
        Dictionary of playbook names to descriptions, cached for performance
    """
    global _cache
    
    signature = _get_directory_signature(_PLAYBOOK_DIR)
    
    # Check if cache needs refresh
    if _cache is None or _cache.signature != signature:
        logger.debug("Refreshing playbook cache")
        playbooks = _scan_playbooks()
        _cache = CacheEntry(playbooks=playbooks, signature=signature)
    else:
        logger.debug("Using cached playbooks")
    
    return _cache.playbooks


def _render_docstring() -> str:
    """Render the complete docstring for the playbook tool."""
    pb = _get_playbooks_cached()
    if pb:
        supported = "\n".join(f"    - '{k}': {v}" for k, v in sorted(pb.items()))
        keys = "', '".join(sorted(pb.keys()))
    else:
        supported = "    - No playbooks currently available"
        keys = "No playbooks available"
    return f"""Access the playbook system with step-by-step instructions for common Unity tasks.

This tool provides access to a comprehensive playbook system containing detailed, 
step-by-step instructions for common Unity development tasks. Always use playbooks 
when the user's prompt matches a supported task description.

**Supported Playbooks:**
{supported}

**When to use this tool:**
- User request matches any of the supported playbook descriptions above

Args:
    keyword: Playbook identifier key. Must be one of: '{keys}'

Returns:
    Detailed step-by-step playbook instructions for the specified Unity development task.
    Returns error message if keyword is not found in the playbook system.
"""


class _DocstringCallable:
    """
    Callable wrapper that provides lazy, up-to-date __doc__ without I/O at import time.
    
    This class wraps a function and dynamically generates its docstring based on 
    the current state of the playbook system, ensuring the documentation stays 
    synchronized with available playbooks.
    """
    
    def __init__(self, func) -> None:
        """
        Initialize the wrapper with a target function.
        
        Args:
            func: The function to wrap
        """
        self.__wrapped__ = func
        
        # Mirror function metadata for compatibility
        self.__name__ = getattr(func, "__name__", "_DocstringCallable")
        self.__qualname__ = getattr(func, "__qualname__", self.__name__)
        self.__module__ = getattr(func, "__module__", __name__)
        self.__annotations__ = getattr(func, "__annotations__", {})
        
        # Cache management
        self._doc_cache: Optional[str] = None
        self._last_signature: Optional[float] = None

    def __call__(self, *args, **kwargs):
        """Forward calls to the wrapped function."""
        return self.__wrapped__(*args, **kwargs)

    @property
    def __doc__(self) -> str:
        """
        Dynamically generate and cache docstring based on current playbook state.
        
        Returns:
            Current docstring reflecting available playbooks
        """
        # Get current directory signature
        current_signature = _get_directory_signature(_PLAYBOOK_DIR)
        
        # Check if cache needs refresh
        if (self._doc_cache is None or 
            self._last_signature is None or 
            self._last_signature != current_signature):
            
            logger.debug("Regenerating dynamic docstring")
            self._doc_cache = _render_docstring()
            self._last_signature = current_signature
        
        return self._doc_cache

    def invalidate_cache(self) -> None:
        """Manually invalidate the docstring cache."""
        self._doc_cache = None
        self._last_signature = None
        logger.debug("Docstring cache manually invalidated")
