using UnityEngine;
using UnityEngine.UIElements;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Coplay.Common;

namespace Coplay.Views.Components
{
    /// <summary>
    /// Manages thumbnail creation, caching, and cleanup for UI elements
    /// </summary>
    public class ThumbnailManager : IDisposable
    {
        private const int DefaultThumbnailSize = 128; // Maximum thumbnail size in pixels for context items
        
        private readonly Dictionary<string, Texture2D> _thumbnailCache = new Dictionary<string, Texture2D>();
        private readonly Dictionary<VisualElement, Texture2D> _elementTextureMap = new Dictionary<VisualElement, Texture2D>();

        /// <summary>
        /// Loads a thumbnail for a context item (small size)
        /// </summary>
        public void LoadContextThumbnail(string imagePath, VisualElement thumbnailElement)
        {
            LoadImageThumbnail(imagePath, thumbnailElement, DefaultThumbnailSize, SetThumbnailTexture);
        }

        /// <summary>
        /// Cleans up texture mappings for all elements under a container
        /// </summary>
        public void CleanupElementTextures(VisualElement container)
        {
            // Find all elements under this container that have textures mapped
            var elementsToCleanup = _elementTextureMap.Keys
                .Where(element => IsChildOf(element, container))
                .ToList();
            
            foreach (var element in elementsToCleanup)
            {
                _elementTextureMap.Remove(element);
            }
        }

        /// <summary>
        /// Disposes all cached textures and clears mappings
        /// </summary>
        public void Dispose()
        {
            CleanupAllThumbnails();
        }

        private void LoadImageThumbnail(string imagePath, VisualElement targetElement, int maxSize, Action<VisualElement, Texture2D, int> setTextureAction)
        {
            try
            {
                // Handle data URIs and file paths
                byte[] imageBytes;
                string cacheKey = imagePath;
                
                if (imagePath.StartsWith("data:"))
                {
                    imageBytes = ContextHelpers.LoadDataUriToTexture(imagePath);
                }
                else if (File.Exists(imagePath))
                {
                    imageBytes = File.ReadAllBytes(imagePath);
                }
                else
                {
                    CoplayLogger.LogWarning($"Image file not found: {imagePath}");
                    return;
                }

                // Check cache first (use size-specific cache key)
                string sizedCacheKey = $"{maxSize}_{cacheKey}";
                if (_thumbnailCache.TryGetValue(sizedCacheKey, out var cachedTexture) && cachedTexture != null)
                {
                    setTextureAction(targetElement, cachedTexture, maxSize);
                    return;
                }

                // Load and create thumbnail
                var originalTexture = new Texture2D(2, 2);
                
                if (originalTexture.LoadImage(imageBytes))
                {
                    // Create thumbnail version
                    var thumbnailTexture = CreateThumbnailTexture(originalTexture, maxSize);
                    
                    // Dispose original texture immediately as we only need the thumbnail
                    UnityEngine.Object.DestroyImmediate(originalTexture);
                    
                    // Cache the thumbnail
                    _thumbnailCache[sizedCacheKey] = thumbnailTexture;
                    
                    // Set the thumbnail
                    setTextureAction(targetElement, thumbnailTexture, maxSize);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(originalTexture);
                    CoplayLogger.LogWarning($"Failed to load image texture: {imagePath}");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error loading image thumbnail for {imagePath}: {ex.Message}");
            }
        }

        private void SetThumbnailTexture(VisualElement thumbnailElement, Texture2D texture, int maxSize)
        {
            // Clean up any existing texture for this element
            if (_elementTextureMap.TryGetValue(thumbnailElement, out var existingTexture))
            {
                if (existingTexture != texture) // Only dispose if it's a different texture
                {
                    // Don't dispose here - it might be cached. The cache will handle disposal.
                    _elementTextureMap.Remove(thumbnailElement);
                }
            }
            
            // Set the new texture
            thumbnailElement.style.backgroundImage = new StyleBackground(texture);
            thumbnailElement.style.unityBackgroundScaleMode = ScaleMode.ScaleAndCrop;
            
            // Track this element-texture mapping
            _elementTextureMap[thumbnailElement] = texture;
        }

        private Texture2D CreateThumbnailTexture(Texture2D originalTexture, int maxSize)
        {
            // Calculate thumbnail dimensions while maintaining aspect ratio
            int originalWidth = originalTexture.width;
            int originalHeight = originalTexture.height;
            
            float scaleFactor = Mathf.Min((float)maxSize / originalWidth, (float)maxSize / originalHeight);
            int thumbnailWidth = Mathf.RoundToInt(originalWidth * scaleFactor);
            int thumbnailHeight = Mathf.RoundToInt(originalHeight * scaleFactor);
            
            // Create thumbnail texture
            var thumbnailTexture = new Texture2D(thumbnailWidth, thumbnailHeight, TextureFormat.RGB24, false);
            
            // Resize using Unity's built-in method
            var renderTexture = RenderTexture.GetTemporary(thumbnailWidth, thumbnailHeight);
            Graphics.Blit(originalTexture, renderTexture);
            
            RenderTexture.active = renderTexture;
            thumbnailTexture.ReadPixels(new Rect(0, 0, thumbnailWidth, thumbnailHeight), 0, 0);
            thumbnailTexture.Apply();
            
            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(renderTexture);
            
            return thumbnailTexture;
        }

        private bool IsChildOf(VisualElement child, VisualElement parent)
        {
            var current = child.parent;
            while (current != null)
            {
                if (current == parent)
                    return true;
                current = current.parent;
            }
            return false;
        }

        private void CleanupAllThumbnails()
        {
            // Dispose all cached textures
            foreach (var texture in _thumbnailCache.Values)
            {
                if (texture != null)
                {
                    UnityEngine.Object.DestroyImmediate(texture);
                }
            }
            
            _thumbnailCache.Clear();
            _elementTextureMap.Clear();
            
            CoplayLogger.Log("Cleaned up all thumbnail textures");
        }
    }
} 