using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using UnityEngine;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Controllers.Systems;
using Newtonsoft.Json;
using System.IO.Abstractions;
using Coplay.Services.Chat;

namespace Coplay.Services.LogCollection
{
    /// <summary>
    /// Service for collecting and uploading Unity and Coplay logs
    /// </summary>
    public class LogCollectionService
    {
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly IApplicationPaths _applicationPaths;
        private readonly SettingsController _settingsController;
        private readonly IFileSystem _filesystem;
        private readonly IChatThreads _chatThreads;

        public LogCollectionService(ICoplayApiClient coplayApiClient, IApplicationPaths applicationPaths, SettingsController settingsController, IFileSystem filesystem, IChatThreads chatThreads)
        {
            _coplayApiClient = coplayApiClient;
            _applicationPaths = applicationPaths;
            _settingsController = settingsController;
            _filesystem = filesystem;
            _chatThreads = chatThreads;
        }

        /// <summary>
        /// Collects all available logs, compresses them into an archive, and uploads to the backend
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task CollectAndUploadLogsAsync()
        {
            try
            {
                CoplayLogger.Log("Starting log collection and upload process...");

                // Create temporary directory for collecting logs
                string tempDir = _filesystem.Path.Combine(_filesystem.Path.GetTempPath(), "CoplayLogCollection_" + Guid.NewGuid().ToString("N")[..8]);
                _filesystem.Directory.CreateDirectory(tempDir);

                try
                {
                    // Collect all types of logs
                    CollectUnityLogs(tempDir);
                    CollectCoplayLogs(tempDir);
                    CollectUnityEditorLogs(tempDir);
                    await CollectSystemInfoAsync(tempDir);

                    // Create compressed archive
                    string archivePath = Path.Combine(Path.GetTempPath(), $"coplay_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip");
                    ZipFile.CreateFromDirectory(tempDir, archivePath);

                    // Upload to backend
                    await UploadLogArchiveAsync(archivePath);

                    CoplayLogger.Log($"Log collection completed successfully. Archive uploaded: {Path.GetFileName(archivePath)}");

                    // Cleanup
                    _filesystem.File.Delete(archivePath);
                }
                finally
                {
                    // Cleanup temporary directory
                    if (_filesystem.Directory.Exists(tempDir))
                    {
                        _filesystem.Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to collect and upload logs: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Collects Unity runtime logs from UnityLogsRepository
        /// </summary>
        private void CollectUnityLogs(string outputDir)
        {
            try
            {
                string unityLogsDir = _filesystem.Path.Combine(outputDir, "UnityLogs");
                _filesystem.Directory.CreateDirectory(unityLogsDir);

                // Copy Unity log files from Temp/Coplay/UnityLogs/
                string sourceLogsDir = _filesystem.Path.Combine(Application.dataPath, "..", "Temp", "Coplay", "UnityLogs");
                
                if (_filesystem.Directory.Exists(sourceLogsDir))
                {
                    var logFiles = _filesystem.Directory.GetFiles(sourceLogsDir, "logs*.txt");
                    foreach (var logFile in logFiles)
                    {
                        string fileName = _filesystem.Path.GetFileName(logFile);
                        string destPath = _filesystem.Path.Combine(unityLogsDir, fileName);
                        _filesystem.File.Copy(logFile, destPath, true);
                    }
                    
                    CoplayLogger.Log($"Collected {logFiles.Length} Unity log files");
                }
                else
                {
                    CoplayLogger.LogWarning("Unity logs directory not found");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to collect Unity logs: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Collects Coplay console logs from the configured logs folder
        /// </summary>
        private void CollectCoplayLogs(string outputDir)
        {
            try
            {
                string coplayLogsDir = _filesystem.Path.Combine(outputDir, "CoplayLogs");
                _filesystem.Directory.CreateDirectory(coplayLogsDir);

                // Copy Coplay console logs
                string sourceLogsDir = _applicationPaths.DefaultLogsFolderPath;
                
                if (_filesystem.Directory.Exists(sourceLogsDir))
                {
                    var logFiles = _filesystem.Directory.GetFiles(sourceLogsDir, "console_logs_*.log");
                    foreach (var logFile in logFiles)
                    {
                        string fileName = _filesystem.Path.GetFileName(logFile);
                        string destPath = _filesystem.Path.Combine(coplayLogsDir, fileName);
                        _filesystem.File.Copy(logFile, destPath, true);
                    }
                    
                    CoplayLogger.Log($"Collected {logFiles.Length} Coplay console log files");
                }
                else
                {
                    CoplayLogger.LogWarning("Coplay logs directory not found");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to collect Coplay logs: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Collects Unity Editor logs from platform-specific locations
        /// </summary>
        private void CollectUnityEditorLogs(string outputDir)
        {
            try
            {
                string editorLogsDir = _filesystem.Path.Combine(outputDir, "UnityEditorLogs");
                _filesystem.Directory.CreateDirectory(editorLogsDir);

                List<string> editorLogPaths = GetUnityEditorLogPaths();
                
                int collectedCount = 0;
                foreach (var logPath in editorLogPaths)
                {
                    if (_filesystem.File.Exists(logPath))
                    {
                        string fileName = _filesystem.Path.GetFileName(logPath);
                        // Add timestamp to avoid conflicts
                        string destFileName = $"{Path.GetFileNameWithoutExtension(fileName)}_{DateTime.UtcNow:yyyyMMdd_HHmmss}{Path.GetExtension(fileName)}";
                        string destPath = _filesystem.Path.Combine(editorLogsDir, destFileName);
                        _filesystem.File.Copy(logPath, destPath, true);
                        collectedCount++;
                    }
                }
                
                CoplayLogger.Log($"Collected {collectedCount} Unity Editor log files");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to collect Unity Editor logs: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets Unity Editor log file paths for the current platform
        /// </summary>
        private List<string> GetUnityEditorLogPaths()
        {
            var paths = new List<string>();

            try
            {
                // Detect OS at runtime since this will be packaged into a single DLL
                string osName = SystemInfo.operatingSystem.ToLower();
                
                if (osName.Contains("windows"))
                {
                    // Windows Unity Editor logs
                    string localAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                    string unityLogsPath = _filesystem.Path.Combine(localAppData, "Unity", "Editor");
                    
                    if (_filesystem.Directory.Exists(unityLogsPath))
                    {
                        paths.AddRange(_filesystem.Directory.GetFiles(unityLogsPath, "Editor.log"));
                        paths.AddRange(_filesystem.Directory.GetFiles(unityLogsPath, "Editor-prev.log"));
                    }
                }
                else if (osName.Contains("mac"))
                {
                    // macOS Unity Editor logs
                    string homePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
                    string unityLogsPath = _filesystem.Path.Combine(homePath, "Library", "Logs", "Unity");
                    
                    if (_filesystem.Directory.Exists(unityLogsPath))
                    {
                        paths.AddRange(Directory.GetFiles(unityLogsPath, "Editor.log"));
                        paths.AddRange(Directory.GetFiles(unityLogsPath, "Editor-prev.log"));
                    }
                }
                else
                {
                    CoplayLogger.LogWarning($"Unknown operating system: {osName}. Unity Editor logs may not be collected.");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to get Unity Editor log paths: {ex.Message}", ex);
            }

            return paths;
        }

        /// <summary>
        /// Collects system information for debugging context
        /// </summary>
        private async Task CollectSystemInfoAsync(string outputDir)
        {
            try
            {
                var systemInfo = new
                {
                    Timestamp = DateTime.UtcNow,
                    UnityVersion = Application.unityVersion,
                    Platform = Application.platform.ToString(),
                    OperatingSystem = SystemInfo.operatingSystem,
                    ProcessorType = SystemInfo.processorType,
                    ProcessorCount = SystemInfo.processorCount,
                    SystemMemorySize = SystemInfo.systemMemorySize,
                    GraphicsDeviceName = SystemInfo.graphicsDeviceName,
                    GraphicsDeviceVersion = SystemInfo.graphicsDeviceVersion,
                    DeviceId = _settingsController.DeviceId,
                    ProjectPath = _applicationPaths.ProjectRoot,
                    DataPath = _applicationPaths.DataPath,
                    CurrentThreadId = _chatThreads.CurrentThread?.id
                };

                string systemInfoJson = JsonConvert.SerializeObject(systemInfo, Formatting.Indented);
                string systemInfoPath = _filesystem.Path.Combine(outputDir, "system_info.json");
                await _filesystem.File.WriteAllTextAsync(systemInfoPath, systemInfoJson);
                
                CoplayLogger.Log("Collected system information");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to collect system info: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Uploads the log archive to the backend
        /// </summary>
        private async Task UploadLogArchiveAsync(string archivePath)
        {
            try
            {
                CoplayLogger.Log($"Uploading log archive: {_filesystem.Path.GetFileName(archivePath)}");
                
                // Read the archive as bytes
                byte[] archiveBytes = await _filesystem.File.ReadAllBytesAsync(archivePath);
                
                // Create upload request with metadata
                // Note: DeviceId and UserEmail are already sent in headers by the API client
                var uploadRequest = new LogArchiveUploadRequest
                {
                    ArchiveFileName = _filesystem.Path.GetFileName(archivePath),
                    ArchiveSize = archiveBytes.Length,
                    Timestamp = DateTime.UtcNow,
                    UnityVersion = Application.unityVersion,
                    Platform = Application.platform.ToString()
                };

                // Upload using the API client
                await _coplayApiClient.UploadLogArchiveAsync(uploadRequest, archiveBytes);
                
                CoplayLogger.Log("Log archive uploaded successfully");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to upload log archive: {ex.Message}", ex);
                throw;
            }
        }
    }

    /// <summary>
    /// Request model for log archive upload
    /// Note: DeviceId and UserEmail are sent via headers by the API client
    /// </summary>
    public class LogArchiveUploadRequest
    {
        public string ArchiveFileName { get; set; }
        public long ArchiveSize { get; set; }
        public DateTime Timestamp { get; set; }
        public string UnityVersion { get; set; }
        public string Platform { get; set; }
    }
}