using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System;
using System.Net.Http;
using UnityEditor;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Common.Services.BackgroundTasks;

namespace Coplay.Controllers.Generation.Textures
{
    public class ImagenTextureGenerator : ITextureGenerator
    {
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly BackgroundTaskService _backgroundTaskService;
        private static readonly Dictionary<string, string> qualityDict = new()
        {
            { "standard", "standard" },
            { "high", "hd" }
        };
        private static readonly Dictionary<string, string> sizeDict = new()
        {
            { "1024x1024", "1024x1024" },
            { "1792x1024", "1792x1024" },
            { "1024x1792", "1024x1792" }
        };
        private static readonly Dictionary<string, string> styleDict = new()
        {
            { "natural", "natural" },
            { "vivid", "vivid" }
        };

        public ImagenTextureGenerator(ICoplayApiClient coplayApiClient, BackgroundTaskService backgroundTaskService)
        {
            _coplayApiClient = coplayApiClient;
            _backgroundTaskService = backgroundTaskService;
        }

        public async Task<bool> Generate3DModelTexture(string outputPath, Dictionary<string, string> options = null)
        {
            BackgroundTask bgTask = null;
            try
            {
                bgTask = _backgroundTaskService.CreateTask("Initializing Imagen...", 0.1f);
                var cancellationToken = bgTask.CancellationTokenSource.Token;

                var qualityStr = options?.GetValueOrDefault("quality", "standard") ?? "standard";
                var sizeStr = options?.GetValueOrDefault("size", "1024x1024") ?? "1024x1024";
                var styleStr = options?.GetValueOrDefault("style", "natural") ?? "natural";

                bgTask.UpdateProgress("Setting up image generation...", 0.2f);

                var objectPrompt = options?.GetValueOrDefault("object_prompt", "") ?? "";
                var stylePrompt = options?.GetValueOrDefault("style_prompt", "") ?? "";

                bgTask.UpdateProgress("Generating image with Imagen...", 0.4f);

                var response = await _coplayApiClient.GenerateImage(
                    objectPrompt,
                    stylePrompt,
                    qualityDict.TryGetValue(qualityStr, out var quality) ? quality : qualityDict["standard"],
                    sizeDict.TryGetValue(sizeStr, out var size) ? size : sizeDict["1024x1024"],
                    styleDict.TryGetValue(styleStr, out var style) ? style : styleDict["natural"],
                    provider: "imagen",
                    cancellationToken: cancellationToken
                );

                bgTask.UpdateProgress("Processing generated image...", 0.6f);
                byte[] imageBytes;
                if (response.ImageUrl.StartsWith("data:"))
                {
                    var base64Data = response.ImageUrl.Substring(response.ImageUrl.IndexOf(",") + 1);
                    imageBytes = Convert.FromBase64String(base64Data);
                }
                else
                {
                    using var httpClient = new HttpClient();
                    imageBytes = await httpClient.GetByteArrayAsync(response.ImageUrl);
                }

                bgTask.UpdateProgress("Saving generated image...", 0.9f);
                string directoryPath = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directoryPath))
                    Directory.CreateDirectory(directoryPath);

                await File.WriteAllBytesAsync(outputPath, imageBytes, cancellationToken);
                AssetDatabase.Refresh();

                return true;
            }
            catch (OperationCanceledException)
            {
                CoplayLogger.Log("Imagen texture creation cancelled");
                throw new Exception("Imagen texture creation cancelled");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to generate texture using Imagen", ex);
                throw;
            }
            finally
            {
                bgTask?.Complete();
            }
        }
    }
}
