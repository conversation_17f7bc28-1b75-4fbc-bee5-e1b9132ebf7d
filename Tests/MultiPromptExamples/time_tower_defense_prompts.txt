1.	Implement 2-D player movement for keyboard and game-pad: move in four directions, keep the ship inside the play area, rotate instantly to face its movement vector, and highlight the tile beneath the ship with a 20 %-opacity square only when that tile is not part of the designated “path” layer.
====
	2.	Create a radial Player Menu component: opens with an Action button, shows up to four icons at top/bottom/left/right, disables normal movement while open, supports both keyboard and game-pad with a configurable input map, keeps the whole menu on-screen near edges, and cleans up debug or per-frame logs.
====
	3.	Add a tower-building system: when “Build” is selected the chosen tower prefab is placed on the highlighted non-path tile, building is blocked on occupied or path tiles, a registry tracks occupied tiles for later sell/upgrade, and draw order always keeps the player sprite and menus above towers.
====
	4.	Give enemies basic walking AI: each enemy moves toward a target position every frame and plays an animation matching its current movement vector.
====
	5.	Create a generic rewindable entity base class that snapshots its serializable state every N ticks, supports FORWARD and REWIND modes, plays animations in reverse while rewinding, discards future states when rewinding ends, and shows a small “rewind” icon on-screen.
====
	6.	Implement A* path-finding for enemies along a “path” tile layer (diagonals allowed). Method setTargetWithPath finds the route, chooses the furthest straight-line tile each step, adds a random offset inside that tile, and renders debug paths when debug mode is on.
====
	7.	Build an Enemy Spawner that instantiates enemies at random points inside its bounds every two seconds (and via a spawn() call) and immediately assigns them to path-find toward the Goal, stopping just short of it.
====
	8.	Make player-built towers shoot: each tower checks for enemies in range, spawns bullets toward the first target, bullets move and detect collisions, mark enemies dead, and both bullets and towers respect rewind mode (towers inactive, bullets rewindable).
====
	9.	Give enemies hit points and a red hit-flash that fades out smoothly over 0.5 s when struck; enemy dies only when HP reaches 0.
====
	10.	Polish rewind logic: on exiting rewind, enemies recalculate their paths; spawners are paused while rewinding; and any enemy rewound past its spawn point is destroyed.
====
	11.	Add an Energy system: player has a max energy pool (default 100), rewind drains 1 energy per tick, building costs energy, energy regenerates 1 every 5 frames (except while rewinding), enemies drop Energy Crystals worth 10 energy, and crystals despawn after 15 s with a flashing warning.
====
	12.	Introduce Goal HP: a goal gem with 100 HP and an on-screen bar; enemies adjacent to the Goal inflict 5 HP every second; when HP hits 0 the game pauses and shows a Game Over overlay that can be restarted via UI or Action key.
====
	13.	Support multiple enemy types and a Wave System: each type has speed, max HP, tint, etc.; waves specify enemy batches with amount, interval and optional delay; next wave starts only when current enemies are defeated; show “WAVE n START!” overlay for 2 s; display a “Congratulations!” screen when all waves end.
====
	14.	Create a Title screen with START and CONFIGURE buttons; START loads the Level scene, CONFIGURE opens a configuration UI.
====
	15.	Build a global Config System: stores all tunable constants (wave list, enemy stats, player speed, max energy, goal HP, etc.), exposes them to scenes, saves edits from a modal JSON editor opened via CONFIGURE, merges older config versions, and lets players return to Title with R.
====
	16.	Expand to four tower types—Basic, Sniper (double range & bullet speed, slower fire), Slowdown (periodic slow aura), and Splash (radial blast)—with a shared base class, full config, non-stacking slow effects, and a build submenu that replaces the older “building” concept.
====
	17.	Refine tower VFX: Splash tower blast now scales from tower center without drifting; Slowdown tower uses a similar animation tinted blue.
====
	18.	Implement a scripted tutorial sequence: disables normal menu, shows dialog boxes, highlights tiles, guides the player to build, spawns test enemies, requires a rewind, pauses and unpauses systems at each step, and ends with final dialog.
====
	19.	Ensure the Player Menu renders only defined menu items and correctly resets after sub-menus close.
====
	20.	Fix an intermittent null-reference that prevents the “Wave Start” overlay from appearing; ensure overlay always shows and scales properly.
====
	21.	While the tutorial is active, disable rewind and energy regeneration until the designated tutorial step begins.
====
	22.	Correct setRestrictedMode so the Player Menu displays only items explicitly allowed (e.g., a single Build → Basic option when restricted).
====
	23.	Prevent all tower types (Sniper and others) from firing while the game is in rewind mode.
====
	24.	Review and tighten tutorial dialog: keep tone light, concise, and consistent with a retro-space theme.
====
	25.	Add a top-level skipTutorial boolean in the config; when true, start waves immediately and bypass the tutorial entirely.
====
	26.	Change enemy–Goal interaction: when an enemy reaches the Goal it explodes and instantly subtracts its remaining HP from the Goal’s HP.
====
	27.	Add a subtle pulsing glow and drop shadow to both the Goal gem and Energy Crystals; sync glow with crystal bobbing motion; fade glow and shadow during crystal expiration or collection; reuse fade-out helper code.
====
	28.	Update player-ship movement: instant heading changes, acceleration when input is held, gentle deceleration when released, working for any vector.
====
	29.	Create a Music System: plays scene-specific tracks (menu vs. game), stops previous tracks on scene change, and swaps to a reversed track during rewind by seeking to the mirrored time position.
====
	30.	Improve performance by removing dead bullets and enemies from update lists once they are destroyed or fully rewound out of existence.
====
	31.	Replace the tutorial’s highlight square with a pulsing tile-highlight sprite (36 × 36 px).
====
	32.	Display a slim HP bar above any enemy that is damaged; update bar position during rewind so it follows the enemy.
====
	33.	Add energyDropRate (default 1.0) to enemy-wave configs to control how often defeated enemies drop Energy Crystals.
====
	34.	Restyle Energy and Goal HP bars: match visual style, add text shadows, shrink font, and relocate both bars (with “Energy” and “Goal HP” labels) to (10, 400) stacked vertically.
====
	35.	Build a global Sound System capable of multiple simultaneous effects; hook it into menu highlight/select, dialog confirm, turret fire (shoot/slow/blast), enemy explosions, and a three-second red-alert sequence during the tutorial.
====
	36.	Introduce a Boss enemy flag that doubles sprite scale (and any other intended boss traits).
====
	37.	Add a full-screen background image to the Title screen and adjust button colors to match a dark-blue/purple palette; remove pulsing title text.
====
	38.	Use the message-box graphic (468 × 129 px) as the dialog background instead of drawn rectangles.
====
	39.	Add a soft shadow beneath the player ship similar to the Goal’s shadow.
====
	40.	Restyle the Game Over overlay to match the updated Title screen aesthetics.
====
	41.	When any dialog box is open, pause enemy movement, turret actions, and state recording; on resume, ensure rewind history doesn’t include the paused frames.