import { NextRequest } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";

export interface AuthenticatedUser {
  id: string;
  email: string;
}

/**
 * Sanitizes header values to prevent injection attacks
 */
function sanitizeHeaderValue(value: string | null): string {
  if (!value) return "";

  return value
    .replace(/[\r\n]/g, '') // Remove newlines to prevent header injection
    .replace(/[<>]/g, '') // Remove angle brackets to prevent XSS
    .replace(/['"]/g, '') // Remove quotes to prevent SQL injection
    .replace(/[;|&$`\\]/g, '') // Remove shell command injection characters
    .trim();
}

/**
 * Validates that a user ID is in a safe format
 */
function isValidUserId(userId: string): boolean {
  // User IDs should be UUIDs or similar safe formats
  const userIdRegex = /^[a-zA-Z0-9_-]+$/;
  return userIdRegex.test(userId) && userId.length > 0 && userId.length <= 100;
}

/**
 * Get the authenticated user from middleware headers
 * This is the preferred method for protected API routes that are secured by middleware
 */
export function getUserFromHeaders(request: NextRequest): AuthenticatedUser | null {
  const rawUserId = request.headers.get("x-user-id");
  const rawUserEmail = request.headers.get("x-user-email");

  if (!rawUserId) {
    return null;
  }

  const sanitizedUserId = sanitizeHeaderValue(rawUserId);
  const sanitizedUserEmail = sanitizeHeaderValue(rawUserEmail);

  // Validate user ID format for security
  if (!isValidUserId(sanitizedUserId)) {
    console.warn("Invalid user ID format detected in headers");
    return null;
  }

  return {
    id: sanitizedUserId,
    email: sanitizedUserEmail,
  };
}

/**
 * Get the authenticated user directly from Supabase
 * Use this for public API routes that need optional authentication
 * or as a fallback for routes not covered by middleware
 */
export async function getUserFromSupabase(): Promise<{
  user: AuthenticatedUser | null;
  error: string | null;
}> {
  try {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { user: null, error: "Unauthorized" };
    }

    return {
      user: {
        id: user.id,
        email: user.email || "",
      },
      error: null,
    };
  } catch (error) {
    console.error("Auth error:", error);
    return { user: null, error: "Authentication failed" };
  }
}

/**
 * Check if an error indicates a refresh token issue
 */
export function isRefreshTokenError(error: any): boolean {
  if (!error) return false;

  const errorMessage = typeof error === 'string' ? error :
    (error.message || error.error_description || '');

  return errorMessage.includes('Refresh Token Not Found') ||
    errorMessage.includes('Invalid Refresh Token') ||
    errorMessage.includes('Session expired') ||
    errorMessage.includes('refresh_token_not_found') ||
    (error.status === 400 && errorMessage.includes('refresh'));
}

/**
 * Get the current session (includes access token)
 * Use when you need the access token for external API calls
 */
export async function getSessionFromSupabase() {
  try {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);

    // First try to refresh the session if needed
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      console.warn("Session error detected:", error);

      // Handle refresh token errors specifically
      if (isRefreshTokenError(error)) {
        console.warn("Invalid refresh token detected, clearing session data");

        // Attempt to clear the invalid session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.warn("Failed to sign out during refresh token cleanup:", signOutError);
        }

        return {
          session: null,
          error: {
            code: "invalid_refresh_token",
            message: "Session expired. Please log in again.",
            __isAuthError: true,
            status: 401
          }
        };
      }

      return { session: null, error };
    }

    if (!session) {
      return { session: null, error: { code: "no_session", message: "No session found" } };
    }

    // Verify the session is still valid by checking expiration with a 5-minute buffer
    // This prevents edge cases where the token expires during the request
    const now = Math.floor(Date.now() / 1000);
    const EXPIRY_BUFFER_SECONDS = 5 * 60; // 5 minutes buffer
    if (session.expires_at && session.expires_at <= (now + EXPIRY_BUFFER_SECONDS)) {
      console.warn("Session will expire soon (within 5 minutes), attempting refresh");

      // Try to refresh the session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError || !refreshData.session) {
        console.warn("Failed to refresh session:", refreshError);

        if (isRefreshTokenError(refreshError)) {
          // Clear the invalid session
          try {
            await supabase.auth.signOut();
          } catch (signOutError) {
            console.warn("Failed to sign out during refresh error cleanup:", signOutError);
          }

          return {
            session: null,
            error: {
              code: "invalid_refresh_token",
              message: "Session expired. Please log in again.",
              __isAuthError: true,
              status: 401
            }
          };
        }

        return { session: null, error: refreshError };
      }

      return { session: refreshData.session, error: null };
    }

    return { session, error: null };
  } catch (error) {
    console.error("Session error:", error);

    // Check if this is an auth error with refresh token issues
    if (isRefreshTokenError(error)) {
      // Attempt to clear any invalid session
      try {
        const cookieStore = await cookies();
        const supabase = createClient(cookieStore);
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.warn("Failed to sign out during error cleanup:", signOutError);
      }

      return {
        session: null,
        error: {
          code: "invalid_refresh_token",
          message: "Session expired. Please log in again.",
          __isAuthError: true,
          status: 401
        }
      };
    }

    return { session: null, error: "Failed to get session" };
  }
}

/**
 * Create a standardized unauthorized response
 */
export function createUnauthorizedResponse(message = "Unauthorized access. Please log in.") {
  return Response.json({ error: message }, { status: 401 });
}

/**
 * Sanitizes error messages to prevent information leakage
 */
function sanitizeErrorMessage(message: string): string {
  // Remove potentially sensitive information
  const sanitized = message
    .replace(/password[:\s]*[^\s]*/gi, 'password: [REDACTED]')
    .replace(/token[:\s]*[^\s]*/gi, 'token: [REDACTED]')
    .replace(/key[:\s]*[^\s]*/gi, 'key: [REDACTED]')
    .replace(/secret[:\s]*[^\s]*/gi, 'secret: [REDACTED]')
    .replace(/database[:\s]*[^\s]*/gi, 'database: [REDACTED]')
    .replace(/<script[\s\S]*?<\/script>/gi, '[XSS_ATTEMPT_BLOCKED]')
    .replace(/javascript:/gi, '[JS_INJECTION_BLOCKED]')
    .replace(/[\r\n]/g, ' '); // Remove newlines

  return sanitized.substring(0, 200); // Limit error message length
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(message: string, status = 500) {
  const sanitizedMessage = sanitizeErrorMessage(message);
  return Response.json({ error: sanitizedMessage }, { status });
}
