using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Coplay.Common;

namespace Coplay.Tests.Benchmark.LanguageTestRunners
{
    public class CSharpTestRunner : LanguageTestRunner
    {
        public override string Language => "csharp";
        
        public override async Task<bool> IsLanguageAvailable()
        {
            try
            {
                var result = await ExecuteCommand("dotnet", "--version", Environment.CurrentDirectory, 10, false);
                if (result.Success)
                {
                    CoplayLogger.Log($"dotnet available: {result.Output.Trim()}");
                    return true;
                }
                
                CoplayLogger.LogWarning("dotnet CLI not available on this system");
                return false;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogWarning($"dotnet CLI not available on this system: {ex.Message}");
                return false;
            }
        }
        
        public override async Task<TestExecutionResult> RunTests(BenchmarkExercise exercise, string workingDirectory)
        {
            var result = new TestExecutionResult();
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Prepare test environment (no file copying needed since we're running in original folder)
                PrepareTestEnvironment(exercise, workingDirectory);
                
                // Restore NuGet packages
                var restoreResult = await ExecuteCommand("dotnet", "restore", workingDirectory, 60);
                if (!restoreResult.Success)
                {
                    result.Error = $"Failed to restore packages: {restoreResult.Error}";
                    result.CompilationErrors = 1;
                    return result;
                }
                
                // Build the project
                var buildResult = await ExecuteCommand("dotnet", "build --no-restore", workingDirectory, 60);
                if (!buildResult.Success)
                {
                    result.Error = CleanTestOutput(buildResult.Error, workingDirectory);
                    result.Output = CleanTestOutput(buildResult.Output, workingDirectory);
                    result.CompilationErrors = CountCompilationErrors(buildResult.Output + buildResult.Error);
                    result.Duration = DateTime.UtcNow - startTime;
                    return result;
                }
                
                // Run tests
                var testResult = await ExecuteCommand("dotnet", "test --no-build --verbosity normal", workingDirectory, TestTimeoutSeconds);
                
                result.Success = testResult.Success;
                result.Output = CleanTestOutput(testResult.Output, workingDirectory);
                result.Error = CleanTestOutput(testResult.Error, workingDirectory);
                result.TimedOut = testResult.TimedOut;
                result.Duration = DateTime.UtcNow - startTime;
                
                // Count compilation errors in the output
                if (!string.IsNullOrEmpty(result.Output) || !string.IsNullOrEmpty(result.Error))
                {
                    result.CompilationErrors = CountCompilationErrors(result.Output + result.Error);
                    result.SyntaxErrors = CountSyntaxErrors(result.Output + result.Error);
                }
                
                CoplayLogger.Log($"C# test completed for {exercise.Name}: Success={result.Success}, Duration={result.Duration.TotalSeconds:F1}s");
                
                return result;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.Duration = DateTime.UtcNow - startTime;
                CoplayLogger.LogError($"Exception running C# tests for {exercise.Name}: {ex.Message}", ex);
                return result;
            }
        }
        
        protected override void PrepareTestEnvironment(BenchmarkExercise exercise, string exerciseDirectory)
        {
            // Since we're running tests in the original exercise folder, no file copying is needed
            // This method can be used for any C#-specific environment setup if needed
            CoplayLogger.Log($"Preparing C# test environment for {exercise.Name} in {exerciseDirectory}");
        }
        
        private int CountCompilationErrors(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;
            
            var errorCount = 0;
            errorCount += CountOccurrences(text, "error CS");
            errorCount += CountOccurrences(text, "Build FAILED");
            errorCount += CountOccurrences(text, "compilation error");
            
            return errorCount;
        }
        
        private int CountSyntaxErrors(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;
            
            var syntaxErrorCount = 0;
            syntaxErrorCount += CountOccurrences(text, "error CS1002"); // ; expected
            syntaxErrorCount += CountOccurrences(text, "error CS1003"); // Syntax error
            syntaxErrorCount += CountOccurrences(text, "error CS1513"); // } expected
            syntaxErrorCount += CountOccurrences(text, "error CS1514"); // { expected
            syntaxErrorCount += CountOccurrences(text, "error CS1525"); // Invalid expression term
            syntaxErrorCount += CountOccurrences(text, "error CS1026"); // ) expected
            syntaxErrorCount += CountOccurrences(text, "error CS1001"); // Identifier expected
            
            return syntaxErrorCount;
        }
        
        private int CountOccurrences(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return 0;
            
            return text.Split(new[] { pattern }, StringSplitOptions.None).Length - 1;
        }
    }
}
