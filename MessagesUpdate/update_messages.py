#!/usr/bin/env python3
"""
Update a JSONB column in Supabase from a large local JSON file.

- Table: set TABLE_NAME
- PK: id (set THREAD_ID)
- Column to update: messages (JSONB)
- Supabase Admin Service Key is hardcoded for simplicity
"""

import json
import sys
import logging
from pathlib import Path
from supabase import create_client, Client
from typing import Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# -----------------------
# CONFIG — EDIT THESE
# -----------------------
SUPABASE_URL = "http://127.0.0.1:54321"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"  # admin service key
TABLE_NAME = "threads"
THREAD_ID = "cpl_thread_477f9560_5b4c_4ad8_a6b0_5a0c8221f3c1"
JSON_FILE = "messages.json"
# -----------------------


def load_json(path: str | Path) -> Any:
    p = Path(path)
    if not p.exists():
        logger.error(f"File not found: {p}")
        sys.exit(1)
    try:
        # Using .read_text() then json.loads() avoids issues with BOM and lets us
        # provide clearer error messages if parsing fails.
        return json.loads(p.read_text(encoding="utf-8"))
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in {p} (line {e.lineno}, col {e.colno}): {e}")
        sys.exit(1)


def get_client() -> Client:
    if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
        logger.error("Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY.")
        sys.exit(1)
    return create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)


def update_messages_json(supabase: Client, table: str, thread_id: str, payload: dict) -> None:
    # Replace the entire 'messages' JSONB value with the loaded payload.
    # If you want to *merge* instead of replace, consider a Postgres RPC function
    # using jsonb || or jsonb_set and call it via supabase.rpc().
    try:
        res = supabase.table(table).update(
            {"messages": payload}).eq("id", thread_id).execute()

        # Basic checks
        if hasattr(res, "data") and res.data:
            logger.info(f"Success: Updated {table}.id='{thread_id}' with {len(json.dumps(payload))} bytes.")
        else:
            logger.warning(f"No rows updated. Does id '{thread_id}' exist in '{table}'?")
            logger.info(f"Response: {res}")
    except Exception as e:
        logger.error(f"Error details: {e}")
        logger.error(f"Error type: {type(e)}")
        # Try to get more info about the HTTP response
        if hasattr(e, '__cause__') and e.__cause__:
            logger.error(f"Underlying error: {e.__cause__}")
        raise


def main():
    # 1) Load JSON payload from disk
    payload = load_json(JSON_FILE)

    # 2) Validate payload type (optional but helpful)
    if not isinstance(payload, (dict, list)):
        logger.error("Top-level JSON should be an object or array for a JSONB column.")
        sys.exit(1)

    # 3) Connect and update
    client = get_client()
    update_messages_json(client, TABLE_NAME, THREAD_ID, payload)


if __name__ == "__main__":
    main()
