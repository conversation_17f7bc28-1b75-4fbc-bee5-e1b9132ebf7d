# Coplay Dependencies Installer

This package provides automated tools for installing Coplay Unity Plugin dependencies through Unity's menu system.

## Overview

The CoplayDepsInstaller provides a streamlined workflow for setting up all necessary dependencies for the Coplay Unity Plugin. It automates the installation process through a series of Unity Editor menu items accessible via `Coplay > Dev Dependencies`.

## Installation Flow

Follow these steps in order to properly install all dependencies:

### 1. Install NuGet for Unity

**Menu Path:** `Coplay > Dev Dependencies > 1. Install NuGet for Unity`

This step installs the NuGet for Unity package manager, which is required for managing .NET dependencies in Unity projects.

**What it does:**
- Adds the NuGet for Unity package to your project's `Packages/manifest.json`
- Uses the GitHub repository: `https://github.com/GlitchEnzo/NuGetForUnity.git`
- Package ID: `com.github-glitchenzo.nugetforunity`

**After running this step:**
- You need to **deactivate and then reactivate the Unity window** for the changes to take effect
- The NuGet for Unity package will be downloaded and integrated into your project

### 2. Install NuGet Dependencies

**Menu Path:** `Coplay > Dev Dependencies > 2. Install NuGet Dependencies`

This step automatically installs all NuGet packages marked as manually installed in the project's `packages.config` file.

**What it does:**
- Reads the `packages.config` file located in the Assets directory
- Identifies packages with `manuallyInstalled="true"` attribute
- Uses reflection to access NuGet for Unity's installation methods
- Automatically installs each required package

**Prerequisites:**
- NuGet for Unity must be installed (Step 1)
- `packages.config` file must exist in the Assets directory

### 3. Install Coplay from Sources

**Menu Path:** `Coplay > Dev Dependencies > 3. Install Coplay from Sources`

This final step installs the Coplay Unity Plugin from local sources.

**What it does:**
- Locates the CoplayUnityPlugin directory relative to the installer
- Adds the Coplay package as a local file dependency in `Packages/manifest.json`
- Package ID: `com.coplaydev.coplay`
- Uses file:// URL format to reference the local CoplayUnityPlugin directory

**Prerequisites:**
- CoplayUnityPlugin directory must exist with a valid `package.json` file

**After running this step:**
- You need to **deactivate and then reactivate the Unity window** for the changes to take effect

## File Structure

```
Assets/CoplayDepsInstaller/
├── README.md                                    # This file
├── package.json                                 # Package definition
└── Editor/
    ├── CoplayDependenciesInstaller.cs          # Main installer script
    └── CoplayDependenciesInstaller.cs.meta     # Unity meta file
```

## Dependencies

- **Unity Editor**: Required for menu integration
- **Newtonsoft.Json**: Used for JSON parsing of manifest files
- **System.Xml**: Used for parsing packages.config

## Troubleshooting

### Common Issues

1. **"NuGet for Unity assembly not found"**
   - Ensure Step 1 was completed successfully
   - Try restarting Unity after installing NuGet for Unity

2. **"packages.config not found"**
   - Verify that `packages.config` exists in the Assets directory
   - Check that the file contains packages with `manuallyInstalled="true"`

3. **"CoplayUnityPlugin directory not found"**
   - Ensure the CoplayUnityPlugin directory exists at the expected relative path
   - Verify that `package.json` exists in the CoplayUnityPlugin directory

### Debug Information

All installation steps provide detailed logging in Unity's Console window. Check the Console for:
- Installation progress messages
- Error details if something goes wrong
- Success confirmations

## Technical Details

The installer uses reflection to interact with NuGet for Unity's internal APIs, specifically:
- `NugetForUnity.NugetPackageInstaller.InstallIdentifier()` method
- `NugetForUnity.Models.NugetPackageIdentifier` class

This approach ensures compatibility with NuGet for Unity while providing automated installation capabilities.
