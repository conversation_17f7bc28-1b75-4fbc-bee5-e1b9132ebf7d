[{"type": "function", "function": {"name": "replace_in_file", "strict": true, "description": "Request to replace sections of content in an existing file using SEARCH/REPLACE blocks that define exact changes to specific parts of the file. This tool should be used when you need to make targeted changes to specific parts of a file.\nCritical rules:\n1. SEARCH content must match the associated file section to find EXACTLY: character-for-character including whitespace, indentation, line endings, comments, etc.\n2. SEARCH/REPLACE blocks will ONLY replace the first match occurrence. Include multiple unique SEARCH/REPLACE blocks for multiple changes, listed in the order they appear in the file.\n3. Keep SEARCH/REPLACE blocks concise: Include just the changing lines, and a few surrounding lines if needed for uniqueness. Do not include long runs of unchanging lines. Each line must be complete.\n4. Special operations: To move code, use two SEARCH/REPLACE blocks (one to delete from original + one to insert at new location). To delete code, use an empty REPLACE section.", "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "The path of the file to modify (relative to the project root {{ projectRoot }})"}, "diff": {"type": "string", "description": "One or more SEARCH/REPLACE blocks following this exact format:\n```\n------- SEARCH\n[exact content to find]\n=======\n[new content to replace with]\n+++++++ REPLACE\n```"}}, "required": ["path", "diff"], "additionalProperties": false}}}]