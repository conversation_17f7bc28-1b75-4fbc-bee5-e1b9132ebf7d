using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Coplay.Common;
using JetBrains.Annotations;
using UnityEngine;
using Newtonsoft.Json;
using System.Reactive.Subjects;
using System.Reactive.Linq;

namespace Coplay.Services.UnityLogs
{

    /// <summary>
    /// Logger for Unity that listens to Unity logs and stores them in a buffer and writes them to files
    /// </summary>
    public class UnityLogsRepository : IUnityLogsRepository, IDisposable
    {
        private readonly object _logLock = new();
        private readonly Subject<UnityLogEntry> _logStream = new();

        // File logging properties
        private readonly string _logDirectory;
        private readonly int _maxLogsPerFile = 1000;
        private readonly int _maxLogFiles = 10;
        private int _currentLogFileIndex = 1;
        private int _currentLogCount;

        public IObservable<UnityLogEntry> LogStream => _logStream.AsObservable();

        /// <summary>
        /// Creates a new Unity logger
        /// </summary>
        public UnityLogsRepository()
        {
            // Create log directory
            _logDirectory = Path.Combine(Application.dataPath, "..", "Temp", "Coplay", "UnityLogs");
            Directory.CreateDirectory(_logDirectory);

            // Initialize log file index and count
            InitializeLogFileState();
            Application.logMessageReceivedThreaded += HandleUnityLog;
        }

        public void Dispose()
        {
            Application.logMessageReceivedThreaded -= HandleUnityLog;
            _logStream.OnCompleted();
            _logStream.Dispose();
        }

        /// <summary>
        /// Initializes the log file state by checking existing log files
        /// </summary>
        private void InitializeLogFileState()
        {
            try
            {
                // Clean up old log files if there are more than max allowed
                CleanupOldLogFiles();

                // Find the highest log file index
                var logFiles = Directory.GetFiles(_logDirectory, "logs*.txt")
                    .Select(Path.GetFileName)
                    .Where(f => f.StartsWith("logs") && f.EndsWith(".txt"));

                foreach (var file in logFiles)
                {
                    if (int.TryParse(file.Substring(4, file.Length - 8), out int fileIndex))
                    {
                        _currentLogFileIndex = Math.Max(_currentLogFileIndex, fileIndex);
                    }
                }

                // Count lines in the current log file
                string currentLogFile = Path.Combine(_logDirectory, $"logs{_currentLogFileIndex}.txt");
                if (File.Exists(currentLogFile))
                {
                    _currentLogCount = File.ReadAllLines(currentLogFile).Length;

                    // If the current file is full, move to the next file
                    if (_currentLogCount >= _maxLogsPerFile)
                    {
                        _currentLogFileIndex++;
                        _currentLogCount = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error initializing log file state: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Handles Unity log messages
        /// </summary>
        /// <param name="logString">The log message</param>
        /// <param name="stackTrace">The stack trace</param>
        /// <param name="type">The log type</param>
        private void HandleUnityLog(string logString, string stackTrace, LogType type)
        {
            // Ignore strings like this:
            //Assets/Packages/Microsoft.CodeAnalysis.Analyzers.3.11.0/buildTransitive/config already contains a globalconfig file: Assets/Packages/Microsoft.CodeAnalysis.Analyzers.3.11.0/buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.globalconfig will be ignored.
            if (logString.Contains("Assets/Packages/Microsoft.CodeAnalysis.Analyzers"))
                return;

            // Ignore logs from CoplayLogger to avoid capturing our own logging
            if (!string.IsNullOrEmpty(stackTrace) && stackTrace.Contains("Coplay.Common.CoplayLogger"))
                return;

            // Determine log level and message
            var level = UnityLogLevel.Info;
            var message = logString;

            switch (type)
            {
                case LogType.Error:
                case LogType.Exception:
                    level = UnityLogLevel.Error;
                    if (!string.IsNullOrEmpty(stackTrace))
                    {
                        message += $"\n{stackTrace}";
                    }
                    break;
                case LogType.Warning:
                    level = UnityLogLevel.Warning;
                    break;
            }

            // Create log entry
            var logEntry = new UnityLogEntry
            {
                time = DateTimeOffset.Now,
                level = level,
                message = message
            };
            _logStream.OnNext(logEntry);

            // Serialize to JSON
            var jsonLog = JsonConvert.SerializeObject(logEntry);

            // Write the log to file
            WriteLogToFile(jsonLog);
        }

        /// <summary>
        /// Writes a log message to the current log file
        /// </summary>
        /// <param name="logMessage">The log message to write</param>
        private void WriteLogToFile(string logMessage)
        {
            try
            {
                lock (_logLock)
                {
                    // Check if we need to move to a new file
                    if (_currentLogCount >= _maxLogsPerFile)
                    {
                        _currentLogFileIndex++;
                        _currentLogCount = 0;

                        // Clean up old log files if needed
                        CleanupOldLogFiles();
                    }

                    // Write the log to the current file
                    string logFilePath = Path.Combine(_logDirectory, $"logs{_currentLogFileIndex}.txt");
                    File.AppendAllText(logFilePath, logMessage + Environment.NewLine);
                    _currentLogCount++;
                }
            }
            catch (Exception)
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Cleans up old log files if there are more than the maximum allowed
        /// </summary>
        private void CleanupOldLogFiles()
        {
            try
            {
                var logFiles = Directory.GetFiles(_logDirectory, "logs*.txt")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                // Keep only the most recent files up to _maxLogFiles
                for (int i = _maxLogFiles; i < logFiles.Count; i++)
                {
                    try
                    {
                        logFiles[i].Delete();
                    }
                    catch (Exception ex)
                    {
                        CoplayLogger.LogError($"Error deleting old log file {logFiles[i].FullName}: {ex.Message}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error cleaning up old log files: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets logs sorted from newest to oldest, applying filtering and pagination.
        /// </summary>
        /// <param name="skip">Number of newest logs to skip.</param>
        /// <param name="limit">Maximum number of logs to return.</param>
        /// <param name="showLogs">Include INFO logs.</param>
        /// <param name="showWarnings">Include WARNING logs.</param>
        /// <param name="showErrors">Include ERROR logs.</param>
        /// <param name="searchTerm">Filter logs containing this text (case-insensitive).</param>
        /// <returns>An array of filtered and paginated log strings, ordered oldest first.</returns>
        public UnityLogEntry[] GetLogs(int skip, int limit, bool showLogs = true, bool showWarnings = true, bool showErrors = true, [CanBeNull] string searchTerm = null)
        {
            try
            {
                // Filter logs first (maintains oldest-to-newest order)
                return GetLogsInternal().Where(logEntry =>
                {
                    try
                    {
                        // --- Type Filtering ---
                        bool typeMatch = (showLogs && logEntry.level == UnityLogLevel.Info) ||
                                         (showWarnings && logEntry.level == UnityLogLevel.Warning) ||
                                         (showErrors && logEntry.level == UnityLogLevel.Error);

                        if (!typeMatch) return false;

                        // --- Unity-MCP Log Filtering ---
                        // Always exclude Unity-MCP logs
                        // TODO: we should have some test to check that the logs don't make their way through. In case the Unity-MCP log strings get updated in the future.
                        if (logEntry.message != null && 
                            (logEntry.message.Contains("UNITY-MCP", StringComparison.OrdinalIgnoreCase) ||
                             logEntry.message.Contains("UnityMcpBridge", StringComparison.OrdinalIgnoreCase)))
                        {
                            return false;
                        }

                        // --- Search Term Filtering ---
                        if (string.IsNullOrEmpty(searchTerm))
                        {
                            return true;
                        }

                        return logEntry.message.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                    }
                    catch (Exception)
                    {
                        // If JSON parsing fails, skip this log entry
                        return false;
                    }
                }).Skip(skip).Take(limit).ToArray();
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error getting logs: {ex.Message}", ex);
                return Array.Empty<UnityLogEntry>();
            }
        }

        private IEnumerable<UnityLogEntry> GetLogsInternal()
        {
            var logFile = Directory.GetFiles(_logDirectory, "logs*.txt")
                .Select(f => new { Path = f, Index = int.Parse(Path.GetFileNameWithoutExtension(f).Substring(4)) })
                .OrderByDescending(f => f.Index)
                .FirstOrDefault();
            if (logFile == null)
                yield break;

            for (var i = logFile.Index; i >= 0; --i)
            {
                var logFileName = Path.Combine(_logDirectory, $"logs{i}.txt");
                if (!File.Exists(logFileName))
                    break;

                var logLines = File.ReadAllLines(logFileName);
                for (var j = logLines.Length - 1; j >= 0; --j)
                {
                    if (string.IsNullOrWhiteSpace(logLines[j]))
                        continue;

                    UnityLogEntry logEntry = null;
                    try
                    {
                        logEntry = JsonConvert.DeserializeObject<UnityLogEntry>(logLines[j]);
                        if (logEntry == null)
                            continue;
                    }
                    catch
                    {
                        continue;
                    }

                    if (logEntry != null)
                        yield return logEntry;
                }
            }
        }

        /// <summary>
        /// Clears all log files
        /// </summary>
        public void ClearLogs()
        {
            try
            {
                lock (_logLock)
                {
                    // Delete all log files
                    var logFiles = Directory.GetFiles(_logDirectory, "logs*.txt");
                    foreach (var file in logFiles)
                    {
                        File.Delete(file);
                    }

                    // Reset log file index and count
                    _currentLogFileIndex = 1;
                    _currentLogCount = 0;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error clearing log files: {ex.Message}");
            }
        }
    }
}
