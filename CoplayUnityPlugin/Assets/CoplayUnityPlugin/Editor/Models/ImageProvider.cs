using System;

namespace CoplayUnityPlugin.Editor.Models
{
    /// <summary>
    /// Supported AI providers for image generation
    /// </summary>
    [Serializable]
    public enum ImageProvider
    {
        /// <summary>
        /// OpenAI GPT-Image-1 - provides streaming partial previews during generation
        /// </summary>
        GPTImage1,

        /// <summary>
        /// Google Imagen - provides high-quality final results with no preview
        /// </summary>
        Imagen
    }

    /// <summary>
    /// Extension methods for ImageProvider enum
    /// </summary>
    public static class ImageProviderExtensions
    {
        /// <summary>
        /// Convert ImageProvider enum to backend API string
        /// </summary>
        public static string ToApiString(this ImageProvider provider)
        {
            return provider switch
            {
                ImageProvider.GPTImage1 => "gpt_image_1",
                ImageProvider.Imagen => "imagen",
                _ => "gpt_image_1" // Default fallback
            };
        }

        /// <summary>
        /// Convert backend API string to ImageProvider enum
        /// </summary>
        public static ImageProvider FromApiString(string apiString)
        {
            return apiString switch
            {
                "gpt_image_1" => ImageProvider.GPTImage1,
                "imagen" => ImageProvider.Imagen,
                _ => ImageProvider.GPTImage1 // Default fallback
            };
        }

        /// <summary>
        /// Get display name for UI
        /// </summary>
        public static string GetDisplayName(this ImageProvider provider)
        {
            return provider switch
            {
                ImageProvider.GPTImage1 => "GPT-Image-1",
                ImageProvider.Imagen => "Imagen",
                _ => "GPT-Image-1"
            };
        }

        /// <summary>
        /// Get description for tooltips
        /// </summary>
        public static string GetDescription(this ImageProvider provider)
        {
            return provider switch
            {
                ImageProvider.GPTImage1 => "OpenAI's image generation with streaming partial previews",
                ImageProvider.Imagen => "Google's high-quality image generation (no preview)",
                _ => "Default image generation provider"
            };
        }
    }
}
