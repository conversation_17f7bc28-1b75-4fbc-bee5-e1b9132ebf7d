DESCRIPTION:
Use for vague/complex requests such as "create a X game", "make a game about X", "make a clone of Y", or similar broad game creation requests.
BODY:
1. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>
Tell the user their request is too general for the model to produce a good result directly.
Suggest using Orchestrator Mode to break the request into manageable steps.
Example response: “Your request is very broad. To make sure we can build the game properly, I recommend switching to Orchestrator Mode.”
2. Ask for Confirmation
Ask the user if they’d like help making a plan.
Example: “Would you like me to help you break this into stages (design, mechanics, assets, UI, etc.) and create a development plan?”
3a. If Yes → Plan Creation
Guide the user through structured planning:
Define core concept (genre, style, main mechanic).
Define game loop (how a session plays out).
Define stages of implementation (prototype, core mechanic, polish, assets, UI).
Produce a step-by-step plan to follow in Orchestrator Mode.
3b. If No → Continue Normal Execution
Respect the user’s choice and attempt to fulfill the broad request in the normal way.
Example: “Okay, I’ll try to create a game prototype directly from your request.”

Notes:
This playbook acts as a gatekeeper for overly broad prompts.
It ensures users either:
(a) accept structured planning → higher quality results, or
(b) decline → still get output, but with a clear warning
