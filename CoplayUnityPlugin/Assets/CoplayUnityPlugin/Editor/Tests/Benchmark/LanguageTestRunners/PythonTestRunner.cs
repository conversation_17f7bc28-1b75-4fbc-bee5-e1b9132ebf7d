using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Coplay.Common;

namespace Coplay.Tests.Benchmark.LanguageTestRunners
{
    public class PythonTestRunner : LanguageTestRunner
    {
        private static string _pythonCommand = null;
        private static bool _pythonCommandChecked = false;
        private static readonly object _lockObject = new object();
        
        public override string Language => "python";
        
        public override async Task<bool> IsLanguageAvailable()
        {
            try
            {
                // Thread-safe check to ensure only one instance performs the detection
                lock (_lockObject)
                {
                    if (_pythonCommandChecked)
                    {
                        CoplayLogger.Log($"Using cached Python command: {_pythonCommand ?? "none"}");
                        return _pythonCommand != null;
                    }
                }
                
                CoplayLogger.Log("Checking Python availability...");
                
                // Try python3 first since it's more likely to exist on macOS
                var result = await ExecuteCommand("python3", "--version", Environment.CurrentDirectory, 10, false);
                if (result.Success)
                {
                    lock (_lockObject)
                    {
                        if (!_pythonCommandChecked)
                        {
                            CoplayLogger.Log($"Python3 available: {result.Output.Trim()}");
                            _pythonCommand = "python3";
                            _pythonCommandChecked = true;
                        }
                    }
                    return true;
                }
                
                // Try python as fallback
                result = await ExecuteCommand("python", "--version", Environment.CurrentDirectory, 10, false);
                if (result.Success)
                {
                    lock (_lockObject)
                    {
                        if (!_pythonCommandChecked)
                        {
                            CoplayLogger.Log($"Python available: {result.Output.Trim()}");
                            _pythonCommand = "python";
                            _pythonCommandChecked = true;
                        }
                    }
                    return true;
                }
                
                lock (_lockObject)
                {
                    if (!_pythonCommandChecked)
                    {
                        _pythonCommandChecked = true;
                        CoplayLogger.LogWarning("Neither python nor python3 are available on this system");
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error checking Python availability: {ex.Message}", ex);
                lock (_lockObject)
                {
                    _pythonCommandChecked = true; // Mark as checked even on error to prevent infinite retries
                }
                return false;
            }
        }
        
        public override async Task<TestExecutionResult> RunTests(BenchmarkExercise exercise, string workingDirectory)
        {
            var result = new TestExecutionResult();
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Prepare test environment (no file copying needed since we're running in original folder)
                PrepareTestEnvironment(exercise, workingDirectory);
                
                // Determine which Python command to use - ensure we've checked availability first
                string pythonCmd;
                lock (_lockObject)
                {
                    if (!_pythonCommandChecked)
                    {
                        // Release lock before async call
                    }
                    else
                    {
                        pythonCmd = _pythonCommand ?? "python";
                    }
                }
                
                if (!_pythonCommandChecked)
                {
                    await IsLanguageAvailable();
                    lock (_lockObject)
                    {
                        pythonCmd = _pythonCommand ?? "python";
                    }
                }
                else
                {
                    lock (_lockObject)
                    {
                        pythonCmd = _pythonCommand ?? "python";
                    }
                }
                
                // Check if pytest is available, otherwise try to install it
                var pytestCheck = await ExecuteCommand(pythonCmd, "-m pytest --version", workingDirectory, 30);
                if (!pytestCheck.Success)
                {
                    CoplayLogger.Log("pytest not found, attempting to install...");
                    var installResult = await ExecuteCommand(pythonCmd, "-m pip install pytest", workingDirectory, 60);
                    if (!installResult.Success)
                    {
                        result.Error = "Failed to install pytest";
                        return result;
                    }
                }
                
                // Run pytest
                var testResult = await ExecuteCommand(pythonCmd, "-m pytest --tb=short --no-header -v", workingDirectory, TestTimeoutSeconds);
                
                result.Success = testResult.Success;
                result.Output = CleanTestOutput(testResult.Output, workingDirectory);
                result.Error = CleanTestOutput(testResult.Error, workingDirectory);
                result.TimedOut = testResult.TimedOut;
                result.Duration = DateTime.UtcNow - startTime;
                
                // Count syntax errors in the output
                if (!string.IsNullOrEmpty(result.Output))
                {
                    result.SyntaxErrors = CountOccurrences(result.Output, "SyntaxError");
                    result.SyntaxErrors += CountOccurrences(result.Error, "SyntaxError");
                }
                
                // For Python, syntax errors are also compilation errors
                result.CompilationErrors = result.SyntaxErrors;
                
                CoplayLogger.Log($"Python test completed for {exercise.Name}: Success={result.Success}, Duration={result.Duration.TotalSeconds:F1}s");
                
                return result;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.Duration = DateTime.UtcNow - startTime;
                CoplayLogger.LogError($"Exception running Python tests for {exercise.Name}: {ex.Message}", ex);
                return result;
            }
        }
        
        protected override void PrepareTestEnvironment(BenchmarkExercise exercise, string exerciseDirectory)
        {
            // Since we're running tests in the original exercise folder, no file copying is needed
            // This method can be used for any Python-specific environment setup if needed
            CoplayLogger.Log($"Preparing Python test environment for {exercise.Name} in {exerciseDirectory}");
        }
        
        private int CountOccurrences(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return 0;
            
            return text.Split(new[] { pattern }, StringSplitOptions.None).Length - 1;
        }
    }
}
