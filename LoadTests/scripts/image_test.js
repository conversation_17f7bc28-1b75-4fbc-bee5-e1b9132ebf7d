// scripts/image_test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
  vus: __ENV.VUS ? parseInt(__ENV.VUS) : 10, // Lower VUs for GPU-heavy operations
  duration: __ENV.DURATION || '2m',
  thresholds: {
    http_req_failed: ['rate<0.01'],
    http_req_duration: ['p(95)<30000'], // 30s for image generation
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8080';
const AUTH_TOKEN = __ENV.K6_AUTH_TOKEN;
const DEVICE_ID = __ENV.K6_DEVICE_ID || 'cpl-device-k6-load-test';

export default function () {
  const payload = JSON.stringify({
    object_prompt: 'A futuristic Unity game scene with neon lighting and cyberpunk elements',
    style_prompt: 'digital art, high quality, detailed, vibrant colors',
    quality: 'standard',
    size: '1024x1024',
    style: 'vivid'
  });

  const headers = {
    'Content-Type': 'application/json',
    'x-device-id': DEVICE_ID,
  };

  if (AUTH_TOKEN) {
    headers['Authorization'] = `Bearer ${AUTH_TOKEN}`;
  }

  const res = http.post(`${BASE_URL}/image/generate?provider=imagen`, payload, {
    headers: headers,
    timeout: '60s',
  });

  check(res, {
    'status ok': (r) => r.status >= 200 && r.status < 300,
    'has response body': (r) => r.body.length > 0,
    'response has image data': (r) => {
      const jsonResponse = r.json();
      return jsonResponse && (jsonResponse.image_url || jsonResponse.image_data);
    },
  });

  // Longer think time for image generation (GPU-heavy, lower RPS)
  sleep(Math.random() * 10 + 15);
}
