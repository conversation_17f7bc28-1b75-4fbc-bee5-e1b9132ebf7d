from dataclasses import dataclass

from .logger import logger
from .models import AIModel


@dataclass
class ModelPricing:
    input: float
    output: float
    cache_read: float
    cache_write: float


def get_model_pricing(model: AIModel, total_tokens: int) -> ModelPricing:
    """Return pricing info for a model in USD per 1M tokens."""
    if model == AIModel.GEMINI_2_5_FLASH_LITE:
        return ModelPricing(0.1, 0.4, 0.025, 0.1833)
    if model == AIModel.GEMINI_2_5_FLASH:
        return ModelPricing(0.3, 2.5, 0.075, 0.3833)
    if model == AIModel.GEMINI_2_5_PRO:
        if total_tokens <= 200000:
            return ModelPricing(1.25, 10, 0.31, 1.625)
        else:
            # The cache_write cost is based on the 21min TTL we set for the google cache somewhere in our code. According to Claude.
            return ModelPricing(2.5, 15, 0.625, 1.625)
    if model in {AIModel.CLAUDE_3_5_SONNET, AIModel.CLAUDE_3_7_SONNET, AIModel.CLAUDE_3_7_THINKING, AIModel.CLAUDE_4_SONNET, AIModel.CLAUDE_4_THINKING}:
        if total_tokens <= 200000:
            return ModelPricing(3, 15, 0.3, 3.75)
        else:
            return ModelPricing(6, 22.5, 0.6, 7.5)
    if model == AIModel.GPT4O:
        return ModelPricing(2.5, 10, 1.25, 2.5)
    if model == AIModel.GPT4O_MINI:
        return ModelPricing(0.15, 0.6, 0.075, 0.15)
    if model == AIModel.GPT41:
        return ModelPricing(2, 8, 0.5, 2)
    if model == AIModel.GPT5:
        return ModelPricing(1.25, 10.0, 0.125, 1.25)
    if model == AIModel.O3:
        return ModelPricing(2, 8, 0.5, 2)
    if model == AIModel.O4_MINI:
        return ModelPricing(1.1, 4.4, 0.275, 1.1)
    if model == AIModel.GROK4:
        return ModelPricing(3, 15, 0.75, 3)
    if model == AIModel.GROK_CODE_FAST_1:
        return ModelPricing(0.2, 1.5, 0.02, 0.2)
    if model in {AIModel.MERCURY_CODER_SMALL, AIModel.MERCURY_CODER_SMALL_STREAM}:
        return ModelPricing(0.25, 1, 0.0, 0.25)
    if model == AIModel.KIMI_K2:
        return ModelPricing(0.6, 2.5, 0.0, 0.0)
    if model == AIModel.QWEN3_CODER:
        return ModelPricing(0.3, 1.2, 0.15, 0.0)
    if model == AIModel.META_LLAMA_3_3_70B:
        return ModelPricing(0, 0, 0.0, 0.0)
    if model == AIModel.Z_AI_GLM_4_5:
        return ModelPricing(0.2, 0.2, 0.1, 0.0)
    if model == AIModel.GPT_OSS_120B:
        return ModelPricing(0.25, 0.69, 0.025, 0.0)
    raise ValueError(f"No pricing information for model {model}")


def calculate_cost(
    input_tokens: int,
    output_tokens: int,
    cache_read: int,
    cache_write: int,
    model: AIModel,
) -> float:
    """Return total cost in USD for the given usage."""
    if (input_tokens < 0 or output_tokens < 0 or cache_read < 0 or cache_write < 0):
        logger.error(f"Negative token usage: input_tokens={input_tokens}, output_tokens={output_tokens}, cache_read={cache_read}, cache_write={cache_write}")
        return 0.0

    pricing = get_model_pricing(
        model, total_tokens=input_tokens + output_tokens + cache_read + cache_write)

    # Use float calculations; round to 6 decimal places at return
    million = 1_000_000.0
    cost = (
        input_tokens * pricing.input / million
        + output_tokens * pricing.output / million
        + cache_read * pricing.cache_read / million
        + cache_write * pricing.cache_write / million
    )
    # Round to 6 decimal places to align with BigQuery NUMERIC precision expectations
    return round(float(cost), 6)
