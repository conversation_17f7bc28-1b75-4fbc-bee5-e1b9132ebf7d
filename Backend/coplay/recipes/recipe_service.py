from coplay.turbopuffer import get_text_embedding_similarities
from pathlib import Path
import os
import json
import re

current_dir = Path(__file__).parent
keywords = ["terrain", "ui"] # TODO: should read this from some file somewhere probably

async def get_recipe(prompt: str, image_attached: bool = False) -> str:
    recipe_str = ""
    for keyword in keywords:
        if keyword == "ui" and not image_attached: # The UI recipe requires a reference image.
            continue # We exit early to avoid extra processing.
        # If the keyword is in the prompt.
        if re.search(r'\b' + re.escape(keyword) + r'\b', prompt.lower()):
            _, successful_embeddings = await get_text_embedding_similarities([prompt], namespace="recipes", context_id=keyword)
            # TODO: reading the threshold from file might become unnecessarily expensive. Could make this all better in the future if we use this feature.
            if successful_embeddings[0].dist > get_cosine_similarity_threshold(keyword): # I.e., if the prompt is not close to the negative prompts.
                recipe_str = get_recipe_from_keyword(keyword)
                break # TODO: maybe make this return multiple recipes, but i doubt we'd soon have an example of that.
    return recipe_str


def get_recipe_from_keyword(keyword: str) -> str:
    with open (os.path.join(current_dir, "recipe_strings", f"{keyword}.txt"), "r", encoding='utf-8') as f:
        recipe_str = f.read()

    return recipe_str


def get_cosine_similarity_threshold(keyword: str) -> float:
    with open(os.path.join(current_dir, "recipe_thresholds", f"{keyword}.json"), "r", encoding='utf-8') as f:
        return json.load(f)["cosine_similarity_threshold"]



