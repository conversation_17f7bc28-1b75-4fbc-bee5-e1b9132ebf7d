#!/usr/bin/env python3
"""
Integration test for request cancellation in AssistantService.
"""

import pytest
import asyncio
import uuid
from unittest.mock import AsyncMock, patch
from datetime import datetime, timezone
from pydantic import BaseModel

from coplay.assistants.chat.agent import send_message_to_llm
from coplay.models import Prompt, AIModel, AssistantMode, CoplayThread
import coplay.assistants.chat.threads as threads

# Mock user and request
class MockUser(BaseModel):
    email: str = "<EMAIL>"
    id: str = str(uuid.uuid4())

class MockRequest:
    def __init__(self):
        self.is_disconnected = AsyncMock(return_value=False)

@pytest.fixture
def mock_db_calls():
    """Mocks database-related function calls within the assistant service."""
    def mock_dispatch_func(event_name, payload):
        # Mock the dispatch function to avoid context variable issues
        return None
    
    with patch('coplay.assistants.chat.agent.can_user_make_requests', new_callable=AsyncMock, return_value=True), \
         patch('coplay.assistants.chat.agent.get_user_anthropic_key', new_callable=AsyncMock, return_value=None), \
         patch('coplay.assistants.chat.agent.check_if_user_has_subscription', new_callable=AsyncMock, return_value=True), \
         patch('coplay.assistants.chat.agent.dispatch', side_effect=mock_dispatch_func):
        yield

@pytest.mark.asyncio
@pytest.mark.integration
@pytest.mark.parametrize("delay", [0, 0.5, 4]) # 0 is before the graph is invoked
async def test_llm_cancellation_and_resumption(mock_db_calls, delay):
    """
    Tests that an LLM request can be cancelled and a new request on the same thread
    can be successfully processed.
    """
    user = MockUser()
    request = MockRequest()
    device_id = "test_device_cancellation"
    thread_id = f"cpl-thread-{uuid.uuid4()}".replace("-", "_")

    # In-memory store to simulate database for threads
    thread_store = {}

    async def mock_create_thread(assistant_mode, device_id, name=None):
        new_thread = CoplayThread(
            id=thread_id,
            assistant_mode=assistant_mode,
            name=name,
            created_at=datetime.now(timezone.utc),
            messages=[],
            turn=0
        )
        thread_store[thread_id] = new_thread
        # Return a copy to avoid shared object modification issues
        return new_thread.model_copy(deep=True)

    async def mock_get_thread(tid):
        if tid in thread_store:
            # Return a copy
            return thread_store[tid].model_copy(deep=True)
        return None

    async def mock_update_thread_messages(tid, messages, turn):
        if tid in thread_store:
            thread_store[tid].messages = messages
            thread_store[tid].turn = turn

    async def mock_delete_thread(tid):
        if tid in thread_store:
            del thread_store[tid]

    # Patch the database-interacting methods of AssistantService
    with patch.object(threads, 'create_thread', side_effect=mock_create_thread), \
         patch('coplay.assistants.chat.agent.get_thread', side_effect=mock_get_thread), \
         patch('coplay.assistants.chat.agent.update_thread_messages', side_effect=mock_update_thread_messages), \
         patch.object(threads, 'delete_thread', side_effect=mock_delete_thread):

        created_thread = await threads.create_thread(
            assistant_mode=AssistantMode.NORMAL,
            device_id=device_id,
            name="cancellation-test-thread"
        )

        # First message, which will be cancelled
        prompt1 = Prompt(
            prompt="This is a long running request that will be cancelled.",
            thread_id=created_thread.id,
            # model=AIModel.GPT4O, # GPT 4o works, but claude should fail with the double system prompt error
            model=AIModel.CLAUDE_4_SONNET,
            assistant_mode=AssistantMode.NORMAL,
            context={}
        )
        cancel_event = asyncio.Event()

        async def cancel_after(delay):
            await asyncio.sleep(delay)
            cancel_event.set()

        async def consume_generator(generator):
            responses = []
            try:
                async for response in generator:
                    responses.append(response)
            except asyncio.CancelledError:
                # This is expected when the task is cancelled.
                pass
            return responses

        llm_generator = send_message_to_llm(
            prompt=prompt1,
            device_id=device_id,
            user=user,
            request=request,
            cancel_scope=cancel_event
        )

        llm_task = asyncio.create_task(consume_generator(llm_generator))
        cancel_task = asyncio.create_task(cancel_after(delay))

        await asyncio.gather(llm_task, cancel_task)

        # Verify that the thread state was updated even after cancellation
        # assert thread_store[created_thread.id].turn > 0
        # assert len(thread_store[created_thread.id].messages) > 0

        # Second message, after cancellation
        prompt2 = Prompt(
            prompt="This is a new message after cancellation.",
            thread_id=created_thread.id,
            # model=AIModel.GPT4O, # GPT 4o works, but claude should fail with the double system prompt error
            model=AIModel.CLAUDE_4_SONNET,  
            assistant_mode=AssistantMode.NORMAL,
            context={}
        )

        second_llm_generator = send_message_to_llm(
            prompt=prompt2,
            device_id=device_id,
            user=user,
            request=request,
        )

        second_responses = await consume_generator(second_llm_generator)

        # Assert that the second request completed successfully
        assert len(second_responses) > 0
        assert not any(r.type == "error" for r in second_responses), "Second request should not have errors. This test checks that if we cancel after the first message, claude does not give an error about consecutive system prompts."
        
        # Clean up the thread
        await threads.delete_thread(created_thread.id)
        assert created_thread.id not in thread_store

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )

if __name__ == "__main__":
    pytest.main(['-v', __file__, '-m', 'integration'])
