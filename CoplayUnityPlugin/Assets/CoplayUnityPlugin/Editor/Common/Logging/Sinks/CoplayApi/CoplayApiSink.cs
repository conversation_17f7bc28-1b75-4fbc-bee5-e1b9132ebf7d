using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Coplay.Common.CoplayApi;
using Coplay.Services.Chat;
using Serilog.Core;
using Serilog.Events;

namespace Coplay.Common.Logging.Sinks.CoplayApi
{
    public class CoplayApiSink : ILogEventSink, IDisposable
    {
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly ICoplayThreadContext _threadContext;
        private readonly Queue<LogEntry> _logQueue = new Queue<LogEntry>();
        private readonly object _lockObject = new object();
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private bool _isProcessing;
        private bool _disposed;

        private class LogEntry
        {
            public string Severity { get; set; }
            public string Message { get; set; }
            public string ThreadId { get; set; }
        }

        public CoplayApiSink(ICoplayApiClient coplayApiClient, ICoplayThreadContext coplayThreadContext)
        {
            _coplayApiClient = coplayApiClient;
            _threadContext = coplayThreadContext;
        }

        public void Emit(LogEvent logEvent)
        {
            if (logEvent == null || _disposed)
                return;

            var severity = GetSeverityString(logEvent.Level);
            var message = LogUtils.SanitizeForLogging(logEvent.RenderMessage());

            var logEntry = new LogEntry
            {
                Severity = severity,
                Message = message,
                ThreadId = _threadContext.CurrentThreadId,
            };

            lock (_lockObject)
            {
                if (_disposed)
                    return;

                _logQueue.Enqueue(logEntry);
                
                if (!_isProcessing)
                {
                    _isProcessing = true;
                    Task.Run(() => ProcessLogQueueAsync(_cancellationTokenSource.Token));
                }
            }
        }

        private async Task ProcessLogQueueAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                LogEntry entry;
                
                lock (_lockObject)
                {
                    if (_logQueue.Count == 0)
                    {
                        _isProcessing = false;
                        return;
                    }
                    entry = _logQueue.Dequeue();
                }

                try
                {
                    await _coplayApiClient.SubmitLog(entry.Severity, entry.Message, entry.ThreadId, cancellationToken);
                }
                catch (Exception)
                {
                    // Silently ignore API submission errors to prevent logging loops
                    // The CoplayApiClient should handle retries internally
                }
            }
        }

        private static string GetSeverityString(LogEventLevel level)
        {
            return level switch
            {
                LogEventLevel.Verbose => "VERBOSE",
                LogEventLevel.Debug => "DEBUG",
                LogEventLevel.Information => "INFO",
                LogEventLevel.Warning => "WARNING",
                LogEventLevel.Error => "ERROR",
                LogEventLevel.Fatal => "FATAL",
                _ => "INFO"
            };
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            // Cancel background processing
            _cancellationTokenSource?.Cancel();

            // Process remaining logs synchronously (with timeout)
            lock (_lockObject)
            {
                var remainingLogs = new List<LogEntry>();
                while (_logQueue.Count > 0)
                {
                    remainingLogs.Add(_logQueue.Dequeue());
                }

                // Submit remaining logs with a timeout
                if (remainingLogs.Count > 0)
                {
                    Task.Run(async () =>
                    {
                        using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                        foreach (var entry in remainingLogs)
                        {
                            try
                            {
                                await _coplayApiClient.SubmitLog(entry.Severity, entry.Message, entry.ThreadId, timeoutCts.Token);
                            }
                            catch
                            {
                                // Ignore errors during disposal
                                break;
                            }
                        }
                    });
                }
            }

            _cancellationTokenSource?.Dispose();
        }
    }
}
