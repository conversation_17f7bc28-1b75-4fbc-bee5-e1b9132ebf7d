import json
import uuid

from langchain_core.messages import AIMessage


def is_web_search_message(message: AIMessage) -> bool:
    """Check if an AI message contains web search results"""
    if not isinstance(message.content, list):
        return False
    
    for content_block in message.content:
        if (content_block.get("type") == "server_tool_use" and 
            content_block.get("name") == "web_search"):
            return True
        if content_block.get("type") == "web_search_tool_result":
            return True
        if content_block.get("type") == "web_search_call":
            return True
        
    return False

def extract_web_search_info(message: AIMessage) -> dict:
    """Extract web search query and results from AI message content"""
    search_info = {
        "query": "",
        "tool_call_id": "",
        "results_count": 0,
        "sources": []
    }
    
    if not isinstance(message.content, list):
        return search_info
    
    for content_block in message.content:
        # Claude: Extract search query from server_tool_use block
        if (content_block.get("type") == "server_tool_use" and 
            content_block.get("name") == "web_search"):
            search_info["tool_call_id"] = content_block.get("id", f"websearch_{uuid.uuid4().hex[:8]}")
            # Parse the partial_json to get the query
            partial_json = content_block.get("partial_json", "{}")
            try:
                query_data = json.loads(partial_json)
                search_info["query"] = query_data.get("query", "web search")
            except json.JSONDecodeError:
                search_info["query"] = "web search"
        
        # Claude: Count results and extract source URLs
        elif content_block.get("type") == "web_search_tool_result":
            search_info["results_count"] += 1
            if "content" in content_block and isinstance(content_block["content"], list):
                for result in content_block["content"]:
                    if result.get("type") == "web_search_result" and result.get("url"):
                        search_info["sources"].append({
                            "title": result.get("title", "Unknown"),
                            "url": result.get("url")
                        })
        # OpenAI: Extract query from web_search_call
        elif content_block.get("type") == "web_search_call":
            search_info["tool_call_id"] = content_block.get("id", f"websearch_{uuid.uuid4().hex[:8]}")
            search_info["query"] = content_block.get("action", {}).get("query", "web search")
       
        # OpenAI: Extract results from text block
        elif content_block.get("type") == "text" and content_block.get("annotations"):
            annotations = content_block.get("annotations", [])
            url_citations = [ann for ann in annotations if ann.get("type") == "url_citation"]
            
            if url_citations:
                search_info["results_count"] = len(url_citations)
                
                # Extract unique sources (deduplicate by URL)
                seen_urls = set()
                for citation in url_citations:
                    url = citation.get("url", "")
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        search_info["sources"].append({
                            "title": citation.get("title", "Unknown"),
                            "url": url
                        })
                
                break  # Only process the first text block with annotations
    
    return search_info

