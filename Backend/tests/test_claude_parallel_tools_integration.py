#!/usr/bin/env python3
"""
Integration tests for Claude-4 configuration comparison.
These tests make actual API calls to verify both configurations result in 5 tool calls.
"""

import pytest
import async<PERSON>
from typing import List, Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage

from coplay.assistants.model_config import ModelConfig
from coplay.models import AIModel, AssistantMode
from coplay.prompts import CHAT_ASSISTANT_INSTRUCTIONS
from coplay.context.tools import SERVER_SIDE_TOOLS


class TestClaudeParallelToolsIntegration:
    """Integration test to check that claude calls tools in parallel with our system prompt."""

    @pytest.fixture
    def test_prompt(self):
        """Test prompt fixture."""
        return "Create 5 cubes."

    @pytest.fixture
    def tool_context(self):
        """Tool context fixture."""
        return {
            "project_root": "/path/to/unity/project",
            "system_details": "Unity 2023.3.0f1, macOS"
        }

    def create_claude_config_with_system_prompt(self, tool_context: Dict[str, str]) -> ModelConfig:
        """Create Claude-4 configuration with chat prompt as system prompt."""
        config = ModelConfig.for_claude(AIModel.CLAUDE_4_SONNET)

        # Bind tools to the configuration
        llm_with_tools = config.with_tools(
            mode=AssistantMode.NORMAL,
            tool_context=tool_context,
            server_side_tools=SERVER_SIDE_TOOLS
        )

        # Update the config with the tools-bound LLM
        config.llm = llm_with_tools

        return config

    def create_claude_config_without_system_prompt(self, tool_context: Dict[str, str]) -> ModelConfig:
        """Create Claude-4 configuration without system prompt."""
        config = ModelConfig.for_claude(AIModel.CLAUDE_4_SONNET)

        # Bind tools to the configuration
        llm_with_tools = config.with_tools(
            mode=AssistantMode.NORMAL,
            tool_context=tool_context,
            server_side_tools=SERVER_SIDE_TOOLS
        )

        # Update the config with the tools-bound LLM
        config.llm = llm_with_tools

        return config

    def create_messages_with_system_prompt(self, test_prompt: str, tool_context: Dict[str, str]) -> List[BaseMessage]:
        """Create message list with system prompt."""
        system_prompt = CHAT_ASSISTANT_INSTRUCTIONS.format(
            project_root=tool_context["project_root"],
            system_details=tool_context["system_details"],
            batch_tool_instructions="",
            environment_details="",
            mcp_servers="",
            active_unity_hierarchy="",
            editor_state="",
            context="",
            prompt=test_prompt,
            screenshots_enabled=False,
        )

        return [
            SystemMessage(content=system_prompt),
            HumanMessage(content=test_prompt)
        ]

    def create_messages_without_system_prompt(self, test_prompt: str) -> List[BaseMessage]:
        """Create message list without system prompt."""
        return [
            HumanMessage(content=test_prompt)
        ]

    @pytest.mark.integration
    async def test_claude_with_system_prompt_returns_5_tool_calls_real_api(
        self, test_prompt, tool_context
    ):
        """Integration test: Claude with system prompt returns exactly 5 tool calls via real API."""
        # Create configuration
        config = self.create_claude_config_with_system_prompt(tool_context)

        # Create messages with system prompt
        messages = self.create_messages_with_system_prompt(
            test_prompt, tool_context)

        # Invoke the real model
        response = await config.llm.ainvoke(messages)

        # Verify response
        assert response is not None
        assert hasattr(response, 'tool_calls')

        # The key assertion: both configurations should return 5 tool calls
        assert len(
            response.tool_calls) == 5, f"Expected 5 tool calls, got {len(response.tool_calls)}"

        for i, tool_call in enumerate(response.tool_calls):
            assert 'name' in tool_call, f"Tool call {i} missing name attribute"

            if hasattr(tool_call, 'args'):
                assert isinstance(
                    tool_call['args'], dict), f"Tool call {i} args should be a dict"

        print(
            f"✅ Config WITH system prompt: {len(response.tool_calls)} tool calls")
        for i, tool_call in enumerate(response.tool_calls):
            print(f"  {i+1}. {tool_call['name']}: {tool_call['args']}")

    @pytest.mark.integration
    async def test_claude_without_system_prompt_returns_5_tool_calls_real_api(
        self, test_prompt, tool_context
    ):
        """Integration test: Claude without system prompt returns exactly 5 tool calls via real API."""
        # Create configuration
        config = self.create_claude_config_without_system_prompt(tool_context)

        # Create messages without system prompt
        messages = self.create_messages_without_system_prompt(test_prompt)

        # Invoke the real model
        response = await config.llm.ainvoke(messages)

        # Verify response
        assert response is not None
        assert hasattr(response, 'tool_calls')

        # The key assertion: both configurations should return 5 tool calls
        assert len(
            response.tool_calls) == 5, f"Expected 5 tool calls, got {len(response.tool_calls)}"

        for i, tool_call in enumerate(response.tool_calls):
            assert 'name' in tool_call, f"Tool call {i} missing name attribute"

            if hasattr(tool_call, 'args'):
                assert isinstance(
                    tool_call['args'], dict), f"Tool call {i} args should be a dict"

        print(
            f"✅ Config WITHOUT system prompt: {len(response.tool_calls)} tool calls")
        for i, tool_call in enumerate(response.tool_calls):
            print(f"  {i+1}. {tool_call['name']}: {tool_call['args']}")

# Pytest configuration for integration tests


def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )


# Helper function to run integration tests
def run_integration_tests():
    """Helper function to run integration tests manually."""
    import subprocess
    import sys

    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/test_claude_parallel_tools_integration.py",
            "-v", "-m", "integration"
        ], capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")
        print(result.stderr)
        print(f"\nReturn code: {result.returncode}")

    except Exception as e:
        print(f"Error running tests: {e}")


if __name__ == "__main__":
    run_integration_tests()
