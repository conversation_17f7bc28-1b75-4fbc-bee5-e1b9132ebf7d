from typing import Sequence

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage, ToolMessage, AIMessageChunk

from .websearch import extract_web_search_info, is_web_search_message
from ..model_transformations import normalize_zai_tool_call_args
from ...prompts import CHAT_USER_MESSAGE_PROMPT
from ...models import AIModel, CoplayToolCall, MessageType, Prompt, PromptResponse, AgentState
from ...logger import logger


def strip_old_context(history: Sequence[BaseMessage], keep_system_message: bool = True) -> Sequence[BaseMessage]:
    redacted_history = []
    for message in history:
        if message.type == "human":
            redacted_history.append(
                # We don't add recipes here because in this case the context is already too large.
                HumanMessage(
                    content=CHAT_USER_MESSAGE_PROMPT.format(
                        prompt=message.additional_kwargs["user_message"],
                        context="<REDACTED CONTEXT>",
                        environment_details=message.additional_kwargs["environment_details"],
                        active_unity_hierarchy=message.additional_kwargs.get("active_unity_hierarchy", ""),
                        editor_state=message.additional_kwargs.get("editor_state", ""),
                    ),
                    additional_kwargs={
                        "user_message": message.additional_kwargs["user_message"],
                        "environment_details": message.additional_kwargs["environment_details"],
                        "active_unity_hierarchy": message.additional_kwargs.get("active_unity_hierarchy", ""),
                        "editor_state": message.additional_kwargs.get("editor_state", ""),
                    }
                )
            )
        elif message.type == "system":
            if keep_system_message:
                redacted_history.append(message)
        else:
            if message.additional_kwargs.get("message_type") == MessageType.CONTEXT_UPDATE.value:
                # Redact context update messages
                continue
            redacted_history.append(message)
    return redacted_history

def revert_messages_to_last_tool_output(state: AgentState) -> None:
    """Revert the messages to the last tool output"""
    state["tool_outputs"] = []
    state["tool_calls"] = []
    last_index = -1
    for i, m in enumerate(reversed(state["messages"])):
        if m.type == "tool":
            last_index = len(state["messages"]) - i
            break
    state["messages"] = state["messages"][:last_index]

def get_last_context_hash(messages: list[BaseMessage]) -> str:
    for m in reversed(messages):
        if m.additional_kwargs.get("type") in [MessageType.CONTEXT_UPDATE.value, MessageType.HUMAN.value]:
            return m.additional_kwargs.get("context_hash")
    return None

def add_cache_control_params(messages: list[BaseMessage]) -> list[BaseMessage]:
    # TODO: it feels like it would be cleaner/nicer to implement this in model_config.py -- That feels like the place where you'd search for it.
    human_done = False
    out = []
    for message in reversed(messages):
        m = message.model_copy(deep=True)
        if (m.additional_kwargs.get("type") == "coplay_human_message" or m.type == "tool") and not human_done:
            if isinstance(m.content, str):
                m.content = [{"type": "text", "text": m.content, "cache_control": {"type": "ephemeral"}}]
            else:
                m.content[0]["cache_control"] = {"type": "ephemeral"}
            human_done = True
        elif isinstance(m, SystemMessage):
            m.content[0]["cache_control"] = {"type": "ephemeral"}
        out.append(m)
    
    out.reverse()

    return out

def strip_unsupported_blocks(messages: list[BaseMessage], current_model: AIModel | None = None) -> list[BaseMessage]:
    for m in messages:
        if isinstance(m, AIMessage):
            m.id = None
            original_model = AIModel.get_model(m.additional_kwargs.get("_coplay_model"))
            if original_model != current_model:
                if isinstance(m.content, list):
                    # Only keep text blocks
                    m.content = [{**c, 'id': None} for c in m.content if c.get("type") in ("text")]
    return messages

def extract_reasoning_output(message: AIMessageChunk) -> str:    
    if len(message.content) > 0 and isinstance(message.content[0], dict):
        if message.content[0].get("type") == "thinking":
            return "<thinking>" + message.content[0].get("thinking") + "</thinking>\n"
        elif message.content[0].get("type") == "reasoning":
            reasoning_block = message.content[0].get("summary")
            if reasoning_block is not None:
                reasonings = []
                for block in reasoning_block:
                    reasoning = block.get("text")
                    if reasoning is not None and reasoning.strip() != "":
                        reasonings.append(reasoning)
                if len(reasonings) > 0:
                    return "<thinking>" + "\n\n".join(reasonings) + "</thinking>\n"
    
    return ""

def build_chat_history(messages: list[BaseMessage], thread_id: str) -> list[PromptResponse]:
    """Returns a full chat history, matching tool outputs to tool calls"""
    history = []
    tool_outputs = {}
    
    # Extract tool outputs so we can match them to tool calls
    for m in messages:
        if m.additional_kwargs.get("type") == MessageType.SCREENSHOT.value:
            tool_outputs[m.additional_kwargs.get("tool_call_id")] = dict(image_urls=m.additional_kwargs.get("image_urls"), output=m.text())
        elif isinstance(m, ToolMessage):
            tool_outputs[m.tool_call_id] = dict(output=m.text())

    for m in messages:
        if m.additional_kwargs.get("type") == MessageType.ERROR.value:
            history.append(PromptResponse(
                type="error",
                thread_id=thread_id,
                message_id=m.id,
                message=m.text()
            ))
        elif isinstance(m, HumanMessage):
            message = m.additional_kwargs.get("prompt", {}).get("prompt")

            history.append(PromptResponse(
                type="human",
                thread_id=thread_id,
                message_id=m.id,
                message=message
            ))
        elif isinstance(m, AIMessage):
            history.append(PromptResponse(
                type="ai",
                thread_id=thread_id,
                message_id=m.id,
                message=m.text(),
                tool_calls=_to_coplay_tool_calls(m, tool_outputs=tool_outputs)
            ))
    return history

async def get_last_prompt(state: AgentState) -> Prompt | None:
    """Returns the last prompt with user message from the state"""
    for message in reversed(state["messages"]):
        if message.additional_kwargs.get("type") == MessageType.HUMAN.value:
            last_prompt = message.additional_kwargs.get("prompt")
            if last_prompt is not None:
                try:
                    return Prompt(**last_prompt)
                except Exception:
                    return None
    return None

def create_human_message_with_optional_images(
    text_content: str,
    image_urls: list[str] | None,
    tool_call_id: str | None,
    additional_kwargs: dict
) -> HumanMessage:
    """
    Creates a HumanMessage, including image content if image_urls are provided.
    """
    if image_urls and len(image_urls) > 0:
        content_list = [{"type": "text", "text": text_content}]
        for image_url in image_urls:
            content_list.append({
                "type": "image_url",
                "image_url": {"url": image_url}
            })
        logger.info(f"Creating HumanMessage with text and {len(image_urls)} image(s).")
        return HumanMessage(content=content_list, additional_kwargs=dict(type=MessageType.SCREENSHOT.value, image_urls=image_urls, tool_call_id=tool_call_id, **additional_kwargs))
    else:
        logger.info(f"Creating HumanMessage with text only (no images).")
        return HumanMessage(content=text_content, additional_kwargs=dict(type=MessageType.HUMAN.value, **additional_kwargs))

def _to_coplay_tool_calls(message: AIMessage, state: AgentState | None = None, tool_outputs: dict[str, dict] | None = None) -> list[CoplayToolCall]:
    if tool_outputs is None:
        tool_outputs = {}
    tool_descriptions = message.additional_kwargs.get("tool_descriptions", {})
    
    coplay_tool_calls = []
    
    # Check if this message contains web search results
    if is_web_search_message(message):
        search_info = extract_web_search_info(message)
        
        # Create a summary of search results
        sources_text = ""
        if search_info["sources"]:
            sources_text = "\n\nSources searched:\n" + "\n".join([
                f"• {source['title']} - {source['url']}" 
                for source in search_info["sources"][:5]  # Limit to first 5 sources
            ])
        
        # Create synthetic web search tool call
        web_search_output = f"Web search executed for: '{search_info['query']}'\n\nFound {search_info['results_count']} results and synthesized response with citations.{sources_text}"
        
        synthetic_tool_call = CoplayToolCall(
            id=search_info["tool_call_id"],
            name="web_search",
            args={"query": search_info["query"]},
            description=f"Web search: {search_info['query']}" if search_info['query'] else "Web search",
            output=web_search_output
        )
        coplay_tool_calls.append(synthetic_tool_call)
    
    # Add regular tool calls
    for tool_call in message.tool_calls:
        if tool_call["name"]:  # Skip empty tool call names
            if state and state["current_model"] == AIModel.Z_AI_GLM_4_5:
                tool_call["args"] = normalize_zai_tool_call_args(tool_call["args"], tool_call["name"])
            coplay_tool_calls.append(
                CoplayToolCall(
                    description=tool_descriptions.get(tool_call["id"], None), 
                    **tool_call, 
                    **tool_outputs.get(tool_call["id"], dict())
                )
            )
    
    return coplay_tool_calls