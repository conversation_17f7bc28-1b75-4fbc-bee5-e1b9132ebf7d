using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using UnityEngine;
using Coplay.Common;
using System.Diagnostics;

namespace Coplay.Tests.Benchmark
{
    public class BenchmarkExerciseManager
    {
        private static readonly string[] SupportedLanguages = { "python", "javascript", "rust", "csharp" };
        private const string PolyglotRepoUrl = "https://github.com/Aider-AI/polyglot-benchmark.git";
        public const string BenchmarkDataDir = "BenchmarkData~";
        
        private readonly string _benchmarkDataPath;
        private readonly string _polyglotPath;
        
        public BenchmarkExerciseManager()
        {
            // Get the Unity project root directory (parent of Assets folder)
            var projectRoot = Directory.GetParent(Application.dataPath).FullName;
            _benchmarkDataPath = Path.Combine(projectRoot, "Assets", BenchmarkDataDir);
            _polyglotPath = Path.Combine(_benchmarkDataPath, "polyglot-benchmark");
        }
        
        public async Task<bool> EnsurePolyglotBenchmarkExists()
        {
            try
            {
                if (!Directory.Exists(_benchmarkDataPath))
                {
                    Directory.CreateDirectory(_benchmarkDataPath);
                    CoplayLogger.Log($"Created benchmark data directory: {_benchmarkDataPath}");
                }
                
                if (!Directory.Exists(_polyglotPath))
                {
                    CoplayLogger.Log("Polyglot benchmark not found, cloning repository...");
                    return await ClonePolyglotRepository();
                }
                else
                {
                    CoplayLogger.Log("Polyglot benchmark found, updating repository...");
                    return await UpdatePolyglotRepository();
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to ensure polyglot benchmark exists: {ex.Message}", ex);
                return false;
            }
        }
        
        private async Task<bool> ClonePolyglotRepository()
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "git",
                    Arguments = $"clone {PolyglotRepoUrl} polyglot-benchmark",
                    WorkingDirectory = _benchmarkDataPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (var process = Process.Start(processInfo))
                {
                    if (process == null)
                    {
                        CoplayLogger.LogError("Failed to start git clone process");
                        return false;
                    }
                    
                    // Create tasks to read output and error streams
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();
                    
                    try
                    {
                        // Wait for process to exit using Task.Run wrapper
                        await Task.Run(() => process.WaitForExit());
                        
                        if (process.ExitCode == 0)
                        {
                            CoplayLogger.Log("Successfully cloned polyglot benchmark repository");
                            return true;
                        }
                        else
                        {
                            var error = await errorTask;
                            CoplayLogger.LogError($"Git clone failed with exit code {process.ExitCode}: {error}");
                            return false;
                        }
                    }
                    finally
                    {
                        // Ensure tasks are completed to avoid deadlocks
                        await outputTask;
                        await errorTask;
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Exception during git clone: {ex.Message}", ex);
                return false;
            }
        }
        
        private async Task<bool> UpdatePolyglotRepository()
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "git",
                    Arguments = "pull origin main",
                    WorkingDirectory = _polyglotPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (var process = Process.Start(processInfo))
                {
                    if (process == null)
                    {
                        CoplayLogger.LogError("Failed to start git pull process");
                        return false;
                    }
                    
                    // Create tasks to read output and error streams
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();
                    
                    try
                    {
                        // Wait for process to exit using Task.Run wrapper
                        await Task.Run(() => process.WaitForExit());
                        
                        if (process.ExitCode == 0)
                        {
                            CoplayLogger.Log("Successfully updated polyglot benchmark repository");
                            return true;
                        }
                        else
                        {
                            var error = await errorTask;
                            CoplayLogger.LogWarning($"Git pull completed with exit code {process.ExitCode}: {error}");
                            return true; // Still consider it successful if repo exists
                        }
                    }
                    finally
                    {
                        // Ensure tasks are completed to avoid deadlocks
                        await outputTask;
                        await errorTask;
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Exception during git pull: {ex.Message}", ex);
                return true; // Still consider it successful if repo exists
            }
        }
        
        public List<BenchmarkExercise> LoadExercises()
        {
            var exercises = new List<BenchmarkExercise>();
            
            if (!Directory.Exists(_polyglotPath))
            {
                CoplayLogger.LogError("Polyglot benchmark directory not found. Run EnsurePolyglotBenchmarkExists() first.");
                return exercises;
            }
            
            foreach (var language in SupportedLanguages)
            {
                var languageDir = Path.Combine(_polyglotPath, language);
                if (!Directory.Exists(languageDir))
                {
                    CoplayLogger.LogWarning($"Language directory not found: {language}");
                    continue;
                }
                
                var practiceDir = Path.Combine(languageDir, "exercises", "practice");
                if (!Directory.Exists(practiceDir))
                {
                    CoplayLogger.LogWarning($"Practice directory not found for language: {language}");
                    continue;
                }
                
                var exerciseDirs = Directory.GetDirectories(practiceDir);
                CoplayLogger.Log($"Found {exerciseDirs.Length} exercises for {language}");
                
                foreach (var exerciseDir in exerciseDirs)
                {
                    try
                    {
                        var exercise = ParseExercise(exerciseDir, language);
                        if (exercise != null)
                        {
                            exercises.Add(exercise);
                        }
                    }
                    catch (Exception ex)
                    {
                        CoplayLogger.LogError($"Failed to parse exercise {exerciseDir}: {ex.Message}", ex);
                    }
                }
            }
            
            CoplayLogger.Log($"Loaded {exercises.Count} total exercises across {SupportedLanguages.Length} languages");
            return exercises;
        }
        
        private BenchmarkExercise ParseExercise(string exerciseDir, string language)
        {
            var exerciseName = Path.GetFileName(exerciseDir);
            var configPath = Path.Combine(exerciseDir, ".meta", "config.json");
            
            if (!File.Exists(configPath))
            {
                CoplayLogger.LogWarning($"Config file not found for exercise: {exerciseName}");
                return null;
            }
            
            try
            {
                var configJson = File.ReadAllText(configPath);
                var config = JsonConvert.DeserializeObject<Dictionary<string, object>>(configJson);
                
                var exercise = new BenchmarkExercise
                {
                    Name = exerciseName,
                    Language = language,
                    ExercisePath = exerciseDir,
                    Config = config
                };
                
                // Parse file lists from config
                if (config.ContainsKey("files") && config["files"] is Newtonsoft.Json.Linq.JObject filesObj)
                {
                    var filesDict = filesObj.ToObject<Dictionary<string, List<string>>>();
                    
                    if (filesDict.ContainsKey("solution"))
                        exercise.SolutionFiles = filesDict["solution"];
                    
                    if (filesDict.ContainsKey("test"))
                        exercise.TestFiles = filesDict["test"];
                    
                    if (filesDict.ContainsKey("example"))
                        exercise.ExampleFiles = filesDict["example"];
                }
                
                // Load instructions
                var instructionsPath = Path.Combine(exerciseDir, ".docs", "instructions.md");
                if (File.Exists(instructionsPath))
                {
                    exercise.Instructions = File.ReadAllText(instructionsPath);
                }
                
                // Load introduction
                var introductionPath = Path.Combine(exerciseDir, ".docs", "introduction.md");
                if (File.Exists(introductionPath))
                {
                    exercise.Introduction = File.ReadAllText(introductionPath);
                }
                
                return exercise;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to parse config for exercise {exerciseName}: {ex.Message}", ex);
                return null;
            }
        }
        
        public List<BenchmarkExercise> GetExercisesForLanguage(string language)
        {
            return LoadExercises().Where(e => e.Language.Equals(language, StringComparison.OrdinalIgnoreCase)).ToList();
        }
        
        public BenchmarkExercise GetExercise(string language, string exerciseName)
        {
            return LoadExercises().FirstOrDefault(e => 
                e.Language.Equals(language, StringComparison.OrdinalIgnoreCase) && 
                e.Name.Equals(exerciseName, StringComparison.OrdinalIgnoreCase));
        }
        
        public void CleanupBenchmarkData()
        {
            try
            {
                if (Directory.Exists(_benchmarkDataPath))
                {
                    Directory.Delete(_benchmarkDataPath, true);
                    CoplayLogger.Log("Cleaned up benchmark data directory");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to cleanup benchmark data: {ex.Message}", ex);
            }
        }
    }
}
