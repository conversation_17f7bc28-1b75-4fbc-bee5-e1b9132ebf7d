using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Controllers.Functions;
using Coplay.Controllers.Systems;
using Coplay.Models.Configuration;
using System.Linq;
using Coplay.Models.Assistants;
using Coplay.Services;
using Coplay.Services.Chat;
using Coplay.Models;
using Coplay.Controllers.Functions.Models;
using System.IO.Abstractions;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Reactive;
using System.Reactive.Threading.Tasks;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Coplay.Controllers.Assistants
{
    public class AssistantController : IDisposable
    {
        private readonly ILogger<AssistantController> _logger;
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly CoplayStateModel _stateModel;
        private readonly ChatHistoryRepository _chatHistory;
        private readonly IChatThreads _chatThreads;
        private readonly StateController _stateManager;
        private readonly FunctionExecutionController _functionExecutionController;
        private readonly ContextSyncController _contextSyncController;
        private readonly IApplicationPaths _applicationPaths;
        private readonly IFileSystem _fileSystem;
        private readonly ContextHelpers _contextHelpers;
        private readonly AutoTopUpService _autoTopUpService;
        private readonly ICoplayThreadContext _threadContext;
        private readonly CompositeDisposable _disposables = new CompositeDisposable();
        private PromptRequest _currentRequest;

        // Public property to access the current thread ID
        public string CurrentThreadId => _chatThreads.CurrentThread?.id;

        public AssistantController(
            ILogger<AssistantController> logger,
            ICoplayApiClient coplayApiClient,
            ChatHistoryRepository chatHistory,
            IChatThreads chatThreads,
            StateController stateManager,
            FunctionExecutionController functionExecutionController,
            ContextSyncController contextSyncController,
            AssistantConfiguration assistantConfiguration,
            CoplayStateModel stateModel,
            IApplicationPaths applicationPaths,
            IFileSystem fileSystem,
            ContextHelpers contextHelpers,
            ICoplayThreadContext threadContext,
            AutoTopUpService autoTopUpService)
        {
            _logger = logger;
            _coplayApiClient = coplayApiClient;
            _chatHistory = chatHistory;
            _chatThreads = chatThreads;
            _stateManager = stateManager;
            _functionExecutionController = functionExecutionController;
            _contextSyncController = contextSyncController;
            _applicationPaths = applicationPaths;
            _assistantConfiguration = assistantConfiguration;
            _stateModel = stateModel;
            _fileSystem = fileSystem;
            _contextHelpers = contextHelpers;
            _threadContext = threadContext;
            _autoTopUpService = autoTopUpService;

            LoadState();

            var threadLoaded = _chatThreads.ThreadLoadedObservable
                .Do(OnThreadChanged)
                .Replay(1)
                .RefCount();

            stateModel.CurrentStateObservable
                // Ensure chat thread is loaded before processing state changes
                .CombineLatest(threadLoaded, (state, threads) => state)
                .DistinctUntilChanged()
                .Select(state => Observable.FromAsync(() => HandleStateChanged(state)))
                .Switch()
                .Retry()
                .Subscribe(_ => { }, ex => _logger.LogError(ex, $"Unhandled error in {nameof(HandleStateChanged)}"))
                .AddTo(_disposables);

            // Load streamed response
            threadLoaded
                .Select(HandleThreadLoaded)
                .Switch()
                .Subscribe(_ => { }, ex => _logger.LogError(ex, $"Unhandled error in {nameof(HandleThreadLoaded)}"))
                .AddTo(_disposables);

            // Subscribe to thread changes to update the static logger property
            _threadContext.CurrentThreadId = CurrentThreadId;
        }

        public void Dispose()
        {
            _disposables.Dispose();

            _logger.LogInformation($"{nameof(AssistantController)}: Dispose called");
            
            // Clear the static thread ID when disposing
            _threadContext.CurrentThreadId = null;
        }

        private async Task HandleStateChanged(CoplayState newState)
        {
            _logger.LogInformation($"Handling state change: {newState}");
            // Only process this event if we are not waiting for recompile and
            // the last message is not from the user (i.e. this is not a new turn).
            if (newState == CoplayState.WaitingForAI && _chatHistory.Messages.LastOrDefault()?.ChatMessageRole != ChatMessageRole.You)
            {
                try
                {
                    await ProcessToolOutputs();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading last thread run");
                    _stateManager.ReturnToPreviousState("Error loading last thread run");
                    _chatHistory.AddMessage("Sorry, this task failed.");
                }
            }
        }

        public async Task SendMessageToLLM(string message, Dictionary<string, string> context = null, string model = null, AssistantMode assistantMode = AssistantMode.Normal)
        {
            if (message.Length > Constants.MaxContextChars)
            {
                _logger.LogWarning("The context exceeds the maximum token limit. Please remove some items from the context.");
                return;
            }

            if (_chatHistory.MessageCount == 1)
            {
                var threadName = message.Length > 100 ? message.Substring(0, 100) + "..." : message;
                await _chatThreads.UpdateThread(CurrentThreadId, threadName, _assistantConfiguration.Mode);
            }

            _currentRequest = new PromptRequest
            {
                prompt = message,
                context = context,
                thread_id = CurrentThreadId,
                tool_outputs = new List<CoplayToolOutput>(),
                model = model,
                assistant_mode = assistantMode
            };

            await SaveState();
            await SendRequest(_currentRequest);
        }

        private async Task SaveState()
        {
            var json = JsonConvert.SerializeObject(_currentRequest);
            var directory = Path.GetDirectoryName(_applicationPaths.DefaultLogsFolderPath);
            if (!string.IsNullOrEmpty(directory))
                _fileSystem.Directory.CreateDirectory(directory);

            await _fileSystem.File.WriteAllTextAsync(_applicationPaths.AssistantThreadIdPath, json);
        }

        private void LoadState()
        {
            if (!_fileSystem.File.Exists(_applicationPaths.AssistantThreadIdPath))
            {
                _logger.LogInformation("No request found, returning empty.");
                _currentRequest = new PromptRequest();
                return;
            }
            var json = _fileSystem.File.ReadAllText(_applicationPaths.AssistantThreadIdPath);
            _currentRequest = JsonConvert.DeserializeObject<PromptRequest>(json);
        }

        private async Task ProcessToolOutputs()
        {            
            var lastMessage = _chatHistory.Messages.LastOrDefault();
            if (lastMessage == null) {
                _logger.LogInformation("No last message found, returning empty list.");
                return;
            }

            if (lastMessage.FunctionCalls.Any(f => !(f.HasExecuted || f.IsCancelled)))
            {
                return;
            }

            var toolCalls = lastMessage.FunctionCalls.Where(f => !f.OutputProcessed && (f.HasExecuted || f.IsCancelled)).ToList();
            if (toolCalls.Count == 0)
            {
                return;
            }

            var toolOutputs = (await Task.WhenAll(toolCalls.Select(async f => {
                var message = f.Result;
                // Handle screenshot responses
                string imageUrl = null;
                if (f.ResultType == nameof(GetImageResponse)) {
                    try {
                        imageUrl = await _contextHelpers.LoadFileToDataUriBase64(f.ImagePath);
                    } catch (Exception ex) {
                        _logger.LogError(ex, "Error deserializing screenshot response");
                    }
                }
                return new CoplayToolOutput() {
                    tool_call_id = f.ToolCallId,
                    tool_output = message,
                    image_url = imageUrl
                };
            }))).ToList();
            _logger.LogInformation("Tool outputs: " + toolOutputs.Count);

            // If we have tool outputs, add compilation error status to the last one
            if (toolOutputs.Count > 0)
            {
                // Get the compilation error status
                bool hasCompilationErrors = false;
#if UNITY_EDITOR
                hasCompilationErrors = EditorUtility.scriptCompilationFailed;
#endif
                
                // Add compilation error status to the last tool output
                var lastOutput = toolOutputs[toolOutputs.Count - 1];
                lastOutput.tool_output += $"\n\n__has_compilation_errors: {hasCompilationErrors.ToString().ToLower()}";
                
                if (hasCompilationErrors)
                {
                    _logger.LogWarning("Unity compilation errors detected - informing LLM in last tool output");
                }

                _currentRequest = new PromptRequest
                {
                    prompt = "",
                    context = _currentRequest.context,
                    thread_id = CurrentThreadId,
                    tool_outputs = toolOutputs,
                    model = _currentRequest.model,
                    assistant_mode = _currentRequest.assistant_mode
                };

                foreach (var toolCall in toolCalls)
                {
                    toolCall.OutputProcessed = true;
                }
                _chatHistory.UpdateMessage(lastMessage.Id);

                await SendRequest(_currentRequest);
            }
        }

        private async Task SendRequest(PromptRequest request)
        {
            // await _contextSyncController.SynchronizeSelectedContexts();
            PromptResponse promptResponse = null;
            try
            {
                _logger.LogInformation("assistant mode: " + request.assistant_mode);
                promptResponse = await Observable.FromAsync(cancellationToken => HandleStreamingRequest(request, cancellationToken))
                    .TakeUntil(_stateManager.CancelThinkingSignal)
                    .LastOrDefaultAsync()
                    .ToTask();
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Request cancelled");
                promptResponse = await HandleCancellation(promptResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending request");
                promptResponse = CreateErrorResponse(ex);
            }
            
            await ProcessResponse(promptResponse);
        }

        private async Task<PromptResponse> HandleStreamingRequest(PromptRequest request, CancellationToken cancellationToken)
        {
            var promptResponse = await _coplayApiClient.StreamSendMessageToLLM(request, cancellationToken);
            promptResponse = await HandleStreamedResponse(promptResponse, promptResponse.task_id, 0, cancellationToken);
            return promptResponse;
        }

        private IObservable<Unit> HandleThreadLoaded(CoplayThread thread)
        {
            return Observable.FromAsync(async token =>
            {
                var currentThreadId = CurrentThreadId;
                if (string.IsNullOrEmpty(currentThreadId))
                {
                    return;
                }

                var promptResponse = await HandleStreamedResponse(null, currentThreadId, 0, token);
                await ProcessResponse(promptResponse);
            })
            .TakeUntil(_stateManager.CancelThinkingSignal);
        }

        private async Task<PromptResponse> HandleStreamedResponse(PromptResponse prevResponse, string taskId, int lastUpdateId, CancellationToken cancellationToken)
        {
            var promptResponse = prevResponse;

            while (promptResponse == null || !promptResponse.finished)
            {
                promptResponse = await _coplayApiClient.GetStreamedMessage(taskId, lastUpdateId, cancellationToken);
                if (promptResponse == null)
                    break;

                lastUpdateId = promptResponse.update_id;

                if (_stateModel.CurrentState != CoplayState.WaitingForAI)
                {
                    _stateModel.SetState(CoplayState.WaitingForAI);
                }

                if (promptResponse.message_id != null)
                {
                    ProcessMessageUpdate(promptResponse, takeFirstToolCallOnly: true);
                }
                else if (promptResponse.type == "progress")
                {
                    _stateManager.ThinkingProgress = promptResponse.message;
                }
            }

            return promptResponse;
        }

        private void ProcessMessageUpdate(PromptResponse promptResponse, bool takeFirstToolCallOnly)
        {
            var functionCalls = ExtractFunctionCalls(promptResponse.tool_calls, promptResponse.message_id, takeFirstToolCallOnly);
            _chatHistory.UpdateMessage(promptResponse.message_id, promptResponse.message, functionCalls, replace: true, costUsd: promptResponse.cost);
        }

        private List<FunctionCall> ExtractFunctionCalls(List<ToolCall> toolCalls, string messageId, bool takeFirstOnly)
        {
            var functionCalls = new List<FunctionCall>();
            
            if (toolCalls.Count == 0)
                return functionCalls;

            var toolCallsToProcess = takeFirstOnly 
                ? toolCalls.Take(1) 
                : toolCalls;

            foreach (var toolCall in toolCallsToProcess)
            {
                if (!Constants.FunctionNamesToPrintInCodeBlocks.Contains(toolCall.name))
                    continue;

                var args = toolCall.args;
                if (!args.ContainsKey("path") || !args.ContainsKey("content"))
                    continue;

                var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, messageId);
                functionCall.Description = $"Generating {toolCall.name} content...";
                functionCalls.Add(functionCall);
            }
            
            return functionCalls;
        }

        private async Task<PromptResponse> HandleCancellation(PromptResponse promptResponse)
        {
            // Only attempt to cancel if we have a task_id (streaming mode)
            if (promptResponse != null && !string.IsNullOrEmpty(promptResponse.task_id))
            {
                await _coplayApiClient.CancelStream(promptResponse.task_id);
            }
            
            return new PromptResponse
            {
                type = "cancelled",
                thread_id = CurrentThreadId,
                message_id = null,
                message = "",
                finished = true,
                tool_calls = new List<ToolCall>()
            };
        }

        private PromptResponse CreateErrorResponse(Exception ex)
        {
            return new PromptResponse
            {
                type = "error",
                thread_id = CurrentThreadId,
                message_id = null,
                message = "Error sending request -- If the error persists start a new chat. Details: " + ex.Message,
                finished = true,
                tool_calls = new List<ToolCall>()
            };
        }

        private async Task ProcessResponse(PromptResponse promptResponse) {
            string message;
            if (_currentRequest == null || promptResponse == null) {
                return;
            }
            try {
                _currentRequest.prompt = "";
                await SaveState();

                if (promptResponse.tool_calls != null && promptResponse.tool_calls.Count > 0)
                {
                    var functionCalls = new List<FunctionCall>();
                    
                    foreach (var toolCall in promptResponse.tool_calls)
                    {
                        var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, promptResponse.message_id);
                        functionCalls.Add(functionCall);
                        _functionExecutionController.AddFunctionToQueue(functionCall);
                    }
                    // Update the message before changing state to keep the message order correct in case we're auto-executing functions.
                    _chatHistory.UpdateMessage(promptResponse.message_id, promptResponse.message, functionCalls, replace: true, costUsd: promptResponse.cost);
                    if (_functionExecutionController.HasUnExecutedFunctions()) {
                        _stateManager.SetState(CoplayState.WaitingForUserToExecuteFunction);
                    } else {
                        _stateManager.ReturnToPreviousState("Finished running assistant thread");
                    }
                }
                else if (promptResponse.type == "error")
                {
                    _logger.LogError("Error running assistant thread: " + promptResponse.message);
                    _chatHistory.UpdateMessage(promptResponse.message_id, promptResponse.message, replace: true, costUsd: promptResponse.cost);
                    _stateManager.ReturnToPreviousState("Error running assistant thread");
                }
                else
                {
                    if (!string.IsNullOrEmpty(promptResponse.message))
                    {
                        _chatHistory.UpdateMessage(promptResponse.message_id, promptResponse.message, replace: true, costUsd: promptResponse.cost);
                    }
                    if (_stateManager.CurrentState == CoplayState.WaitingForAI)
                    {
                        // We should only be in WaitingForAI if we get here
                        // There are some race conditions where we cancel a thread and still are in the process of processing the response.
                        _stateManager.ReturnToPreviousState("Finished running assistant thread");
                    }
                }

                message = promptResponse.message ?? "";

                // Check for auto top-up after LLM request completes
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _autoTopUpService.CheckAndHandleAutoTopUpAsync();
                    }
                    catch (Exception autoTopUpEx)
                    {
                        _logger.LogError(autoTopUpEx, "Error during auto top-up check");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing assistant response");
                _chatHistory.UpdateMessage(promptResponse.message_id, "Coplay: Error processing assistant response -- If the error persists start a new chat. Details: " + ex.Message, replace: true, costUsd: promptResponse.cost);
                _stateManager.ReturnToPreviousState("Error processing assistant response");
            }
        }

        public void ClearState()
        {
            _currentRequest = null;
            _fileSystem.File.Delete(_applicationPaths.AssistantThreadIdPath);
        }

        private void OnThreadChanged(CoplayThread thread)
        {
            _logger.LogInformation($"{Thread.CurrentThread.ManagedThreadId} OnThreadChanged");
            _threadContext.CurrentThreadId = CurrentThreadId;
        }
    }
}
