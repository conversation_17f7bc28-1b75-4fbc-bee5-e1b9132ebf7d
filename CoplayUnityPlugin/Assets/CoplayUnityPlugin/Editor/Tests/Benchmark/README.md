# Coplay AI Agent Benchmark System

This benchmark system tests Coplay's FileFunctions (WriteToFile and ReplaceInFile) capabilities using the same exercises that <PERSON><PERSON> uses for benchmarking. It provides comprehensive testing across multiple programming languages and AI models.

## Overview

The benchmark system:
- Downloads and uses <PERSON><PERSON>'s polyglot-benchmark repository (https://github.com/Aider-AI/polyglot-benchmark.git)
- Tests FileFunctions across Python, JavaScript, and Rust exercises
- Supports sequential testing of different AI models (GPT-4, Claude 4 Sonnet)
- Generates detailed reports comparing performance across models and languages
- Provides metrics similar to <PERSON><PERSON>'s benchmark for direct comparison

## Architecture

### Core Components

1. **BenchmarkExercise.cs** - Data models for exercises, results, and sessions
2. **BenchmarkExerciseManager.cs** - Downloads and manages polyglot-benchmark exercises
3. **LanguageTestRunners/** - Language-specific test execution
   - `LanguageTestRunner.cs` - Abstract base class
   - `PythonTestRunner.cs` - Python exercise execution
   - `JavaScriptTestRunner.cs` - JavaScript exercise execution  
   - `RustTestRunner.cs` - Rust exercise execution
4. **CoplayBenchmarkRunner.cs** - Main orchestrator for running benchmarks
5. **BenchmarkReporter.cs** - Report generation and analysis
6. **CoplayBenchmarkTests.cs** - Unity test integration

### Directory Structure

```
Assets/Tests/
├── BenchmarkData~/
│   └── polyglot-benchmark/     # Downloaded from Aider's repo
└── BenchmarkReports~/           # Generated reports
    ├── benchmark_gpt-4_*.json
    ├── benchmark_claude_*.json
    └── comparison_report_*.md
```

## Prerequisites

### Required Software

The benchmark system requires the following tools to be installed on your system:

- **Git** - For cloning the polyglot-benchmark repository
- **Python 3.x** - For running Python exercises
  - `pytest` will be automatically installed if not present
- **Node.js & npm** - For running JavaScript exercises
  - `jest` will be configured automatically
- **Rust & Cargo** - For running Rust exercises

### Unity Setup

- Unity 2021.3 or later
- Coplay Unity Plugin properly configured
- Access to Coplay backend API
- Valid API keys for GPT-4 and/or Claude models

## Usage

### Running Tests
- Comment the `[Ignore("For local testing")]` attribute in `CoplayBenchmarkTests.cs` to run the tests.
- You need to open the Coplay window and enable Auto-execute.
- Set the max requests to 99.

#### 1. Setup Test (Recommended First)
```csharp
// In Unity Test Runner, run:
CoplayBenchmarkTests.TestSetupPolyglotBenchmark
```
This will:
- Download the polyglot-benchmark repository
- Verify exercises are loaded correctly
- Check language availability

#### 2. Single Exercise Test
```csharp
// Run a quick test with one exercise:
CoplayBenchmarkTests.TestSingleExerciseBenchmark
```

#### 3. Full Benchmark Tests
```csharp
// Run full benchmark for GPT-4:
CoplayBenchmarkTests.RunFullBenchmarkGPT4

// Run full benchmark for Claude:
CoplayBenchmarkTests.RunFullBenchmarkClaude
```

### Programmatic Usage

```csharp
// Create benchmark components
var exerciseManager = new BenchmarkExerciseManager();
var reporter = new BenchmarkReporter();
var benchmarkRunner = new CoplayBenchmarkRunner(
    exerciseManager, 
    assistantController, 
    fileFunctions
);

// Run benchmark
var session = await benchmarkRunner.RunFullBenchmark(
    model: "gpt-4",
    maxExercises: 50,
    specificLanguages: new List<string> { "python", "javascript" }
);

// Save and generate reports
await reporter.SaveSessionReport(session);
var allSessions = reporter.LoadAllSessions();
await reporter.GenerateComparisonReport(allSessions);
```

## Configuration

### Language Selection

By default, the system supports:
- **Python** - Uses pytest for test execution
- **JavaScript** - Uses npm/jest for test execution
- **Rust** - Uses cargo test for test execution

To modify supported languages, update the `SupportedLanguages` array in `BenchmarkExerciseManager.cs`.

### Exercise Limits

For testing purposes, you can limit the number of exercises:

```csharp
// Run only 10 exercises total
var session = await benchmarkRunner.RunFullBenchmark("gpt-4", maxExercises: 10);

// Run only Python exercises
var session = await benchmarkRunner.RunFullBenchmark(
    "gpt-4", 
    specificLanguages: new List<string> { "python" }
);
```

### Timeouts

Default timeouts can be adjusted in `LanguageTestRunner.cs`:
- Test execution timeout: 180 seconds (3 minutes)
- Language availability check: 10 seconds

## Reports

### Session Reports (JSON)
Individual benchmark sessions are saved as JSON files containing:
- Exercise results and success rates
- Cost and duration metrics
- Error analysis and function usage
- Model and language breakdowns

### Comparison Reports (Markdown)
Comprehensive reports comparing multiple sessions:
- Overall summary statistics
- Model performance comparison
- Language-specific breakdowns
- Detailed failure analysis
- Performance metrics and trends

### Sample Report Structure

```markdown
# Coplay Benchmark Comparison Report

## Overall Summary
- Total Sessions: 2
- Total Exercises: 100
- Total Successful: 75 (75.00%)
- Total Cost: $2.45
- Total Duration: 1,234.5 seconds

## Model Comparison
| Model | Sessions | Exercises | Success Rate | Avg Cost/Exercise | Avg Duration/Exercise |
|-------|----------|-----------|--------------|-------------------|----------------------|
| gpt-4 | 1 | 50 | 80.00% | $0.0234 | 12.3s |
| claude-3-5-sonnet | 1 | 50 | 70.00% | $0.0256 | 15.7s |

## Language Breakdown
| Language | Exercises | Success Rate | Avg Syntax Errors | Avg Compilation Errors |
|----------|-----------|--------------|-------------------|------------------------|
| python | 35 | 85.71% | 0.2 | 0.2 |
| javascript | 35 | 71.43% | 0.4 | 0.4 |
| rust | 30 | 66.67% | 0.8 | 1.2 |
```

## Metrics Collected

### Success Metrics
- **Success Rate** - Percentage of exercises solved correctly
- **Test Pass Rate** - Percentage of unit tests that passed
- **Completion Rate** - Percentage of exercises attempted

### Performance Metrics
- **Duration** - Time taken per exercise and total
- **Cost** - API costs per exercise and total
- **Function Usage** - WriteToFile vs ReplaceInFile usage patterns

### Error Analysis
- **Syntax Errors** - Code syntax issues
- **Compilation Errors** - Language-specific compilation failures
- **Test Timeouts** - Exercises that exceeded time limits
- **Runtime Errors** - Execution failures

## Comparison with Aider

The benchmark system is designed to be directly comparable with Aider's results:

1. **Same Exercises** - Uses identical polyglot-benchmark repository
2. **Similar Metrics** - Tracks success rates, costs, and error types
3. **Compatible Format** - Results can be analyzed alongside Aider data
4. **Language Coverage** - Tests same programming languages (subset)

### Key Differences from Aider
- **Focus on FileFunctions** - Specifically tests WriteToFile/ReplaceInFile
- **Unity Integration** - Runs within Unity Test Framework
- **C# Implementation** - Native C# vs Python implementation
- **Coplay Backend** - Uses Coplay's API infrastructure

## Troubleshooting

### Common Issues

1. **Git not found**
   - Ensure Git is installed and in PATH
   - Check that `git --version` works in command line

2. **Language runtime missing**
   - Install Python 3.x, Node.js, or Rust as needed
   - Verify with `python --version`, `node --version`, `cargo --version`

3. **Permission errors**
   - Ensure Unity has write permissions to Assets/Tests/ directory
   - Check that temp directories can be created and deleted

4. **API errors**
   - Verify Coplay backend configuration
   - Check API keys and model availability
   - Ensure network connectivity

5. **Test timeouts**
   - Increase timeout values in LanguageTestRunner
   - Check system performance and available resources

### Debug Logging

Enable detailed logging by checking Unity Console during test execution. The system provides comprehensive logging for:
- Exercise loading and parsing
- Language availability checks
- Test execution progress
- Error details and stack traces

## Contributing

To extend the benchmark system:

1. **Add new languages** - Implement new LanguageTestRunner subclasses
2. **Add metrics** - Extend BenchmarkResult with additional fields
3. **Improve reports** - Enhance BenchmarkReporter with new analysis
4. **Add test categories** - Create specialized test suites for specific scenarios

## Performance Considerations

- **Disk Space** - Polyglot benchmark repo is ~100MB
- **Memory Usage** - Each exercise creates temporary files
- **Network Usage** - Downloads exercises and makes API calls
- **Time** - Full benchmark can take 30+ minutes depending on model and exercise count

For CI/CD integration, consider:
- Running subset of exercises for faster feedback
- Caching polyglot-benchmark repository
- Using parallel execution (future enhancement)
- Setting appropriate timeouts for build systems
