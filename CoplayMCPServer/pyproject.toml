[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "coplay-mcp-server"
dynamic = ["version"]
description = "A Model Context Protocol (MCP) server for Coplay, providing Unity integration capabilities"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Coplay Dev", email = "<EMAIL>"}
]
maintainers = [
    {name = "Coplay Dev", email = "<EMAIL>"}
]
keywords = ["mcp", "unity", "coplay", "model-context-protocol", "unity-editor"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Games/Entertainment",
]
dependencies = [
    "aiofiles>=24.1.0",
    "anyio>=4.10.0",
    "mcp[cli]>=1.12.4",
    "psutil>=7.0.0",
    "pydantic>=2.11.7",
    "watchdog>=6.0.0",
]

[project.urls]
Homepage = "https://coplay.dev/"
Repository = "https://github.com/CoplayDev/coplay-unity-plugin"
Documentation = "https://docs.coplay.dev/"
Issues = "https://github.com/CoplayDev/coplay-unity-plugin/issues"

[project.scripts]
coplay-mcp-server = "coplay_mcp_server.server:main"

[tool.hatch.version]
path = "coplay_mcp_server/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["coplay_mcp_server"]
