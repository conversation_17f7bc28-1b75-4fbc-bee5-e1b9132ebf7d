using NUnit.Framework;
using Moq;
using Coplay.Controllers.Functions;
using Coplay.Models;
using Coplay.Models.Assistants;
using System.Collections.Generic;
using System.Linq;

namespace Coplay.Tests
{
    [TestFixture]
    public class FunctionExecutionSafetyTests
    {
        [Test]
        public void MakeFunctionCallFromToolCall_FatalCommand_AutoRejectsAndCancels()
        {
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "rm -rf /" }, // Known fatal command
                    { "requires_approval", "false" }
                },
                description = "Execute dangerous command"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert - Focus on integration behavior, not command-specific logic
            Assert.IsTrue(functionCall.IsCancelled, "Fatal command should be auto-cancelled");
            Assert.AreEqual(FunctionState.Cancelled, functionCall.State, "State should be Cancelled");
            Assert.IsTrue(functionCall.OutputProcessed, "Output should be marked as processed");
            Assert.IsNotNull(functionCall.Result, "Result should contain safety message");
            Assert.IsTrue(functionCall.Result.Contains("Command blocked for safety"),
                "Result should explain why command was blocked");
        }

        [Test]
        public void MakeFunctionCallFromToolCall_SafeCommand_LLMHandlesApproval()
        {
            // Arrange
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "mv important.txt backup.txt" },
                    { "requires_approval", "false" }
                },
                description = "Move file"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsFalse(functionCall.IsCancelled, "Safe command should not be cancelled");
            Assert.IsFalse(functionCall.RequiresUserApproval, "LLM decides approval for safe commands");
            Assert.AreEqual(FunctionState.Queued, functionCall.State, "State should remain Queued");
        }

        [Test]
        public void MakeFunctionCallFromToolCall_SafeCommand_DoesNotRequireApproval()
        {
            // Arrange
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "ls -la" },
                    { "requires_approval", "false" }
                },
                description = "List files"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsFalse(functionCall.IsCancelled, "Safe command should not be cancelled");
            Assert.IsFalse(functionCall.RequiresUserApproval, "Safe command should not require approval");
            Assert.AreEqual(FunctionState.Queued, functionCall.State, "State should be Queued");
        }

        [Test]
        public void MakeFunctionCallFromToolCall_NonExecuteCommand_SafetyNotApplied()
        {
            // Arrange
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "write_to_file",
                args = new Dictionary<string, string>
                {
                    { "path", "test.txt" },
                    { "content", "hello world" }
                },
                description = "Write file"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsFalse(functionCall.IsCancelled, "Non-execute command should not be cancelled");
            Assert.IsFalse(functionCall.RequiresUserApproval, "Non-execute command should not require approval by default");
            Assert.AreEqual(FunctionState.Queued, functionCall.State);
        }

        [Test]
        public void MakeFunctionCallFromToolCall_EmptyCommand_HandledSafely()
        {
            // Arrange
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "" }
                },
                description = "Execute empty command"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsFalse(functionCall.IsCancelled, "Empty command should be treated as safe");
            Assert.IsFalse(functionCall.RequiresUserApproval, "Empty command should not require approval");
        }

        [Test]
        public void MakeFunctionCallFromToolCall_LLMRequiresApproval_OverridesSafetyForSafe()
        {
            // Arrange - LLM already flagged for approval via requires_approval flag
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "ls -la" }, // Safe command
                    { "requires_approval", "true" } // But backend wants approval
                },
                description = "Safe command but LLM wants approval"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsFalse(functionCall.IsCancelled, "Safe command should not be cancelled even with LLM approval");
            Assert.IsTrue(functionCall.RequiresUserApproval, "LLM approval flag should be respected");
        }

        [Test]
        public void MakeFunctionCallFromToolCall_LLMRequiresApprovalButFatalCommand_StillBlocked()
        {
            // Arrange - LLM wants approval but command is fatal
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "rm -rf /" }, // Fatal command
                    { "requires_approval", "true" } // LLM wants approval
                },
                description = "Fatal command with LLM approval"
            };

            // Act
            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");

            // Assert
            Assert.IsTrue(functionCall.IsCancelled, "Fatal command should be blocked regardless of LLM approval");
            Assert.AreEqual(FunctionState.Cancelled, functionCall.State);
        }

        [Test]
        public void MakeFunctionCallFromToolCall_AlreadyExecutedCommand_SafetyNotApplied()
        {
            // Arrange - Command already has output (executed)
            var toolCall = new ToolCall
            {
                id = "test-id",
                name = "execute_command",
                args = new Dictionary<string, string>
                {
                    { "command", "rm -rf /" } // Fatal command
                },
                description = "Already executed fatal command",
                output = "Command already executed" // This makes HasExecuted = true
            };

            var functionCall = FunctionExecutionController.MakeFunctionCallFromToolCall(toolCall, "message-123");
            Assert.IsTrue(functionCall.HasExecuted, "Command should be marked as executed");
            Assert.AreEqual(FunctionState.Executed, functionCall.State);
        }
    }
}
