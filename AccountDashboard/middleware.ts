import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { cookies } from "next/headers";

/**
 * Sanitizes header values to prevent injection attacks
 */
function sanitizeHeaderValue(value: string): string {
  if (!value) return "";

  return value
    .replace(/[\r\n]/g, '') // Remove newlines to prevent header injection
    .replace(/[<>]/g, '') // Remove angle brackets to prevent XSS
    .replace(/['"]/g, '') // Remove quotes to prevent injection
    .replace(/[;|&$`\\]/g, '') // Remove shell command injection characters
    .trim()
    .substring(0, 200); // Limit length
}

/**
 * Validates that a path doesn't contain traversal attempts
 */
function isValidPath(pathname: string): boolean {
  // Check for path traversal attempts
  const pathTraversalPattern = /(\.\.|%2e%2e|%2E%2E|\.%2e|%2e\.|\.\%2E|%2E\.)/i;
  if (pathTraversalPattern.test(pathname)) {
    return false;
  }

  // Check for null bytes and other suspicious characters
  if (pathname.includes('\0') || pathname.includes('\x00')) {
    return false;
  }

  return true;
}

/**
 * Validates that the user ID is in a safe format (UUID)
 */
function isValidUserId(userId: string): boolean {
  if (!userId || typeof userId !== 'string') return false;
  if (userId.length === 0 || userId.length > 100) return false;

  // Supabase uses standard UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  // This regex matches UUID v4 format with proper hyphen placement
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(userId);
}

// Define route patterns
const publicRoutes = [
  "/login",
  "/signup",
  "/forgot-password-request",
  "/reset-password",
  "/auth/callback",
  "/auth/device", // Allow device auth pages
];

const authRoutes = [
  "/login",
  "/signup",
  "/forgot-password-request",
  "/reset-password",
];

// Define API route security patterns
const publicApiRoutes = [
  "/api/auth/magic-link",
  "/api/auth/forgot-password-request",
  "/api/auth/confirm-signup",
  "/api/device/", // Device routes are public for device authentication
];

const protectedApiRoutes = [
  "/api/auth/profile",
  "/api/auth/update-profile",
  "/api/auth/update-password",
  "/api/auth/delete-account",
  "/api/auth/provider",
  "/api/profile/", // Allow profile routes
  "/api/subscription",
  "/api/create-checkout-session",
  "/api/create-billing-session",
];

function isPublicApiRoute(pathname: string): boolean {
  return publicApiRoutes.some(route =>
    pathname === route || pathname.startsWith(route)
  );
}

function isProtectedApiRoute(pathname: string): boolean {
  return protectedApiRoutes.some(route =>
    pathname === route || pathname.startsWith(route)
  );
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/static/") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // Validate path for security
  if (!isValidPath(pathname)) {
    console.warn("Path traversal attempt detected:", pathname);

    if (pathname.startsWith("/api/")) {
      return NextResponse.json(
        { error: "Invalid request path" },
        { status: 400 }
      );
    }

    return NextResponse.redirect(new URL("/login", request.url));
  }

  try {
    // Handle API routes with enhanced security
    if (pathname.startsWith("/api/")) {
      return await handleApiRoutes(request, pathname);
    }

    // Handle regular page routes
    return await handlePageRoutes(request, pathname);
  } catch (error) {
    console.error("Middleware error:", error);

    // For API routes, return JSON error
    if (pathname.startsWith("/api/")) {
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }

    // For page routes, redirect to login
    return NextResponse.redirect(new URL("/login", request.url));
  }
}

async function handleApiRoutes(request: NextRequest, pathname: string) {
  // Allow public API routes without authentication
  if (isPublicApiRoute(pathname)) {
    return NextResponse.next();
  }

  // Require authentication for protected API routes
  if (isProtectedApiRoute(pathname)) {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      // Handle refresh token errors specifically
      if (authError?.message?.includes('Refresh Token Not Found') ||
        authError?.message?.includes('Invalid Refresh Token')) {
        console.warn("Invalid refresh token detected in middleware, clearing session");

        // Attempt to clear the invalid session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.warn("Failed to sign out during refresh token cleanup in middleware:", signOutError);
        }

        return NextResponse.json(
          { error: "Session expired. Please log in again." },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: "Unauthorized access. Please log in." },
        { status: 401 }
      );
    }

    // Validate and sanitize user data before adding to headers
    const sanitizedUserId = sanitizeHeaderValue(user.id);
    const sanitizedUserEmail = sanitizeHeaderValue(user.email || "");

    // Additional validation for user ID format
    if (!isValidUserId(sanitizedUserId)) {
      console.warn("Invalid user ID format detected:", user.id);
      return NextResponse.json(
        { error: "Invalid user credentials" },
        { status: 401 }
      );
    }

    // Add sanitized user information to request headers for API routes to use
    const response = NextResponse.next();
    response.headers.set("x-user-id", sanitizedUserId);
    response.headers.set("x-user-email", sanitizedUserEmail);

    return response;
  }

  // For API routes not explicitly defined, deny access by default for security
  return NextResponse.json(
    { error: "API endpoint not found or access denied" },
    { status: 404 }
  );
}

async function handlePageRoutes(request: NextRequest, pathname: string) {
  // Create supabase client
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);

  // Use getUser() for secure authentication verification
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  const isAuthenticated = !!user && !authError;
  const isPublicRoute = publicRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );
  const isAuthRoute = authRoutes.includes(pathname);

  // Handle root route
  if (pathname === "/") {
    if (isAuthenticated) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    } else {
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  // Redirect authenticated users away from auth pages
  if (isAuthenticated && isAuthRoute) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Redirect unauthenticated users to login for protected routes
  if (!isAuthenticated && !isPublicRoute) {
    const loginUrl = new URL("/login", request.url);
    // Preserve the original URL as a redirect parameter
    loginUrl.searchParams.set("redirectTo", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Allow the request to continue
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
