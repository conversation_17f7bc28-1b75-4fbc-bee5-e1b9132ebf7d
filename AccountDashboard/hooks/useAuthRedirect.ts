"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { Session } from "@supabase/supabase-js";

interface UseAuthRedirectOptions {
  redirectTo?: string;
  redirectIfAuthenticated?: boolean;
  redirectIfNotAuthenticated?: boolean;
  unauthenticatedRedirectTo?: string;
}

interface UseAuthRedirectReturn {
  session: Session | null;
  loading: boolean;
  error: Error | null;
}

/**
 * Custom hook to handle authentication checking and redirection
 * @param options Configuration options for the hook
 * @returns Object containing session, loading state, and error
 */
export function useAuthRedirect(
  options: UseAuthRedirectOptions = {}
): UseAuthRedirectReturn {
  const {
    redirectTo = "/dashboard",
    redirectIfAuthenticated = true,
    redirectIfNotAuthenticated = false,
    unauthenticatedRedirectTo = "/login",
  } = options;

  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkSession = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if user is already authenticated using secure getUser()
        const {
          data: { user },
          error: authError,
        } = await supabase.auth.getUser();

        if (authError) {
          throw authError;
        }

        // Get session only if user is authenticated
        let currentSession = null;
        if (user) {
          const { data: { session } } = await supabase.auth.getSession();
          currentSession = session;
        }

        setSession(currentSession);

        // Handle redirects based on authentication state
        if (currentSession && redirectIfAuthenticated) {
          router.push(redirectTo);
        } else if (!currentSession && redirectIfNotAuthenticated) {
          router.push(unauthenticatedRedirectTo);
        }
      } catch (err) {
        setError(
          err instanceof Error
            ? err
            : new Error("Failed to check authentication")
        );
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      setSession(currentSession);

      // Handle redirects on auth state change
      if (currentSession && redirectIfAuthenticated) {
        router.push(redirectTo);
      } else if (!currentSession && redirectIfNotAuthenticated) {
        router.push(unauthenticatedRedirectTo);
      }
    });

    return () => subscription.unsubscribe();
  }, [
    redirectTo,
    redirectIfAuthenticated,
    redirectIfNotAuthenticated,
    unauthenticatedRedirectTo,
    router,
    supabase,
  ]);

  return {
    session,
    loading,
    error,
  };
}

/**
 * Hook specifically for pages that should redirect authenticated users to dashboard
 * Equivalent to the original server-side code
 */
export function useRedirectIfAuthenticated(redirectTo: string = "/dashboard") {
  return useAuthRedirect({
    redirectTo,
    redirectIfAuthenticated: true,
    redirectIfNotAuthenticated: false,
  });
}

/**
 * Hook for pages that require authentication
 */
export function useRequireAuth(unauthenticatedRedirectTo: string = "/login") {
  return useAuthRedirect({
    redirectIfAuthenticated: false,
    redirectIfNotAuthenticated: true,
    unauthenticatedRedirectTo,
  });
}
