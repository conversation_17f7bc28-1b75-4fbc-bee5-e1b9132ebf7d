using Newtonsoft.Json;

namespace Coplay.Common.CoplayApi.Models
{
    [System.Serializable]
    public class FileUploadResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("file_url")]
        public string FileUrl { get; set; }

        [JsonProperty("file_path")]
        public string FilePath { get; set; }

        [JsonProperty("error")]
        public string Error { get; set; }
    }
} 