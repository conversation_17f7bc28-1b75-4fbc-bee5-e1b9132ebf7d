I see a gentle slope and some mild dunes now but no cliff formations and the entire terrain appears to be above the wayer plain.
Sculpt the terrain to add dunes, slopes, or coastal features
create a terrain to represent a beach area as a starting zone for my 3rd person adventure game.
Can you make the terrain uneven so that it bounces randomly
Create a new scene and then add a terrain to it
You were supposed to add terrain to New scene too
Improve the terrain mechanic in my game which is for a MMORTS game. Use generated assets or whatever you need, please do ask questions for things that you want clarified.
Read the Assets/TerrainGenerator/Documentation.pdf and follow the steps so we can create a demo terrain in this scene. Also fix any compiler errors.
I need a pretty basic terrain generator that will also correlate to a grid building system in a RTS game. We need it to be very scaleable for a massive world with chunking/streaming terrain. We use server authoritive Naka<PERSON> backend using Go lang and Protobuf. Spawns should have 3 resources in their vicinity; oil, power, iron. These resources we can build a building on top that will generate resources to the player. Any follow up questions before you begin?
You didnt understand, the terrain has too many hills and such, not bouncy.. i need a more flat terrain and being able to preview where the spawn points were generated, i dont want to place spawn points myself remove these functions that we dont want
once the terrain is generated populate the terrain with the buildings prefab
once the terrain is generated generate realistic rodas and populate the buildings around the roads
create a terrain of X