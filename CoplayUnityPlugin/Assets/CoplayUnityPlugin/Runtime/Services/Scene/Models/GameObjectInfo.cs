using System.Collections.Generic;
using Newtonsoft.Json;

namespace Coplay.Services.Scene.Models
{
    /// <summary>
    /// Game object information
    /// </summary>
    public class GameObjectInfo
    {
        /// <summary>
        /// Gets or sets the path of the game object in the hierarchy
        /// </summary>
        [JsonProperty("path")]
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the components attached to the game object
        /// </summary>
        [JsonProperty("components", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<string> Components { get; set; } = null;

        /// <summary>
        /// Gets or sets the child game objects
        /// </summary>
        [JsonProperty("children", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public List<GameObjectInfo> Children { get; set; } = null;
    }
}
