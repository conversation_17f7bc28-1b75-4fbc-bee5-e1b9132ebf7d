using System;
using Serilog;
using Serilog.Configuration;

namespace Coplay.Common.Logging.Sinks.Unity
{
    public static class SerilogUnityConfigurationExtensions
    {
        public static LoggerConfiguration UnityLog(
            this LoggerSinkConfiguration loggerSinkConfiguration)
        {
            if (loggerSinkConfiguration == null) throw new ArgumentNullException(nameof(loggerSinkConfiguration));

            return loggerSinkConfiguration.Sink(new UnitySink());
        }
    }
}
