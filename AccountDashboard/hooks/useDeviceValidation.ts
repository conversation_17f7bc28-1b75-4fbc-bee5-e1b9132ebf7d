import { useEffect, useState } from "react";
import { getDevice } from "@/utils/device-handlers";

interface UseDeviceValidationProps {
  params: { deviceId: string };
}

interface UseDeviceValidationReturn {
  deviceId: string;
  deviceError: string | null;
  isValidatingDevice: boolean;
}

export function useDeviceValidation({
  params,
}: UseDeviceValidationProps): UseDeviceValidationReturn {
  const [deviceId, setDeviceId] = useState<string>("");
  const [deviceError, setDeviceError] = useState<string | null>(null);
  const [isValidatingDevice, setIsValidatingDevice] = useState(true);

  useEffect(() => {
    (async () => {
      const id = params.deviceId;
      setDeviceId(id);

      // Validate device
      setIsValidatingDevice(true);
      try {
        const result = await getDevice(id);
        setDeviceError(result?.error || null);
      } catch (error) {
        // Preserve error information for debugging
        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
        setDeviceError(errorMessage);
      }
      setIsValidatingDevice(false);
    })();
  }, [params.deviceId]);

  return {
    deviceId,
    deviceError,
    isValidatingDevice,
  };
}
