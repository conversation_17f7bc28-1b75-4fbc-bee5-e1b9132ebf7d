[tool.poetry]
name = "coplay-backend"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.11.6,<=3.11.13"
fastapi = ">=0.68.0"
uvicorn = ">=0.15.0"
google-cloud-logging = ">=3.11.3"
google-cloud-storage = "^3.0.0"
google-cloud-bigquery = "^3.0.0"
semver = "^3.0.2"
aiohttp = "^3.12.14"
tiktoken = "^0.8.0"
pytest-asyncio = "^0.25.0"
langchain = "^0.3.27"
langchain-community = "^0.3.26"
langchain-openai = "^0.3.30"
langchain-anthropic = "^0.3.17"
langchain-xai = "^0.2.1"
mitsuba = "3.5.1"
unidecode = "^1.3.8"
pillow = "^11.1.0"
anthropic = "^0.58.2"
transforms3d = "^0.4.2"
astor = "^0.8.1"
ipdb = "^0.13.13"
scipy = "^1.15.0"
jaxtyping = "^0.2.36"
imageio = "^2.36.1"
tqdm = "^4.67.1"
numpy = "1.26.4"
engine = {path = "third_party/scene-language", develop = true}
treelib = "^1.7.0"
turbopuffer = "^0.1.27"
litellm = "^1.70.0"
bpy = "4.4.0"
mathutils = "^3.3.0"
python-multipart = "^0.0.20"
alembic = "^1.14.1"
supabase = "^2.13.0"
pyjwt = "^2.10.1"
codeflash = "^0.10.0"
async-lru = "^2.0.5"
lru-dict = "^1.3.0"
jsonschema = "^4.23.0"
langgraph-checkpoint-postgres = "^2.0.21"
psycopg-binary = "^3.2.7"
stripe = "^12.1.0"
langchain-google-genai = "^2.1.8"
fastapi-events = "^0.12.2"
langsmith = "^0.4.1"
google-genai = "^1.26.0"
fal-client = "^0.7.0"
httpx = "^0.28.0"
httpx-aiohttp = "0.1.8"
aiocache = "^0.12.3"
langgraph = "^0.6.5"
langchain-litellm = "^0.2.2"
opentelemetry-sdk = "^1.36.0"
opentelemetry-api = "^1.36.0"
opentelemetry-exporter-gcp-trace = "^1.9.0"
opentelemetry-exporter-gcp-monitoring = "^1.9.0a0"
opentelemetry-propagator-gcp = "^1.9.0"
opentelemetry-instrumentation-fastapi = "^0.57b0"
opentelemetry-instrumentation-psycopg = "^0.57b0"
apscheduler = "^3.10.4"
pyvirtualdisplay = "^3.0"
granian = {extras = ["reload"], version = "^2.5.2"}

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-mock = "^3.14.0"

[tool.codeflash]
# All paths are relative to this pyproject.toml's directory.
module-root = "."
tests-root = "tests"
test-framework = "pytest"
ignore-paths = []
formatter-cmds = ["disabled"]


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
