DEBUG_API_URL=http://localhost:8080
# # Backend environment configuration for staging database
# # Coplay Staging/dev (ysmvkqusgixuljahepfu) - us-east-2
# # Run with: `python -m dotenv run python main.py`

# # Supabase Configuration
# SUPABASE_URL=https://ysmvkqusgixuljahepfu.supabase.co
# SUPABASE_PUBLIC_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzbXZrcXVzZ2l4dWxqYWhlcGZ1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4MDA3MDQsImV4cCI6MjA2MjM3NjcwNH0.734Ku9-O4N0eEBC8C2quLh7u_2YU0OklbSSxW7neGvI

# # Note: You'll need to get the service role key from Supabase dashboard
# # Go to: https://supabase.com/dashboard/project/ysmvkqusgixuljahepfu/settings/api
# # Copy the service_role key and set it here:
# SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzbXZrcXVzZ2l4dWxqYWhlcGZ1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjgwMDcwNCwiZXhwIjoyMDYyMzc2NzA0fQ.VOzfZGVDdwb81vS9fLOHcRpVCeBDFrQNcABU2UiNPyg

# # Database Connection (using Supavisor session mode for IPv4 compatibility)
# SUPABASE_POSTGRES_URL=postgresql://postgres.ysmvkqusgixuljahepfu:<EMAIL>:5432/postgres

# # Storage Buckets (you may need to adjust these for staging)
# SUPABASE_BUCKET=coplay-staging
# SUPABASE_DEPENDENCIES_BUCKET=coplay-dependencies-staging

# # Other environment variables will use defaults from constants.py
