// scripts/stream_test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
  vus: __ENV.VUS ? parseInt(__ENV.VUS) : 50,
  duration: __ENV.DURATION || '2m',
  thresholds: {
    http_req_failed: ['rate<0.01'],
    http_req_duration: ['p(95)<1200'], // ms
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8080';
const AUTH_TOKEN = __ENV.K6_AUTH_TOKEN;
const DEVICE_ID = __ENV.K6_DEVICE_ID || 'cpl-device-k6-load-test';
const MODEL = __ENV.K6_MODEL || 'gemini-2-5-flash';

export default function () {
  const headers = {
    'Content-Type': 'application/json',
    'x-device-id': DEVICE_ID,
  };

  if (AUTH_TOKEN) {
    headers['Authorization'] = `Bearer ${AUTH_TOKEN}`;
  }

  // First create a thread
  const createThreadPayload = JSON.stringify({
    assistant_mode: 'normal',
    name: `Load Test Thread VU${__VU}-${__ITER}`
  });

  const createThreadRes = http.post(`${BASE_URL}/assistant`, createThreadPayload, {
    headers: headers,
    timeout: '30s',
  });

  check(createThreadRes, {
    'thread creation ok': (r) => r.status >= 200 && r.status < 300,
  });

  if (createThreadRes.status < 200 || createThreadRes.status >= 300) {
    console.error(`❌ Thread creation failed with status ${createThreadRes.status}: ${createThreadRes.body}`);
    return;
  }

  const thread = JSON.parse(createThreadRes.body);
  const thread_id = thread.id;

  // Now send a message to the created thread
  const payload = JSON.stringify({
    prompt: 'Hello, Coplay! Please help me write a simple Unity script.',
    thread_id: thread_id,
    model: MODEL,
    assistant_mode: 'normal',
    tool_outputs: [],
    context: {
      project_root: '/tmp/k6-load-test-unity-project',
      system_details: JSON.stringify({
        unity_version: 'Unity 2023.3.0f1',
        platform: 'macOS',
        environment: 'k6 Load Test'
      })
    }
  });

  const res = http.post(`${BASE_URL}/assistant/stream`, payload, {
    headers: headers,
    timeout: '120s',
  });

  check(res, {
    'status ok': (r) => r.status >= 200 && r.status < 300,
    'has response body': (r) => r.body.length > 0,
  });

  // Debug: Log failed requests
  if (res.status >= 400) {
    console.error(`❌ Request failed with status ${res.status}: ${res.body}`);
  }

  // Think time between messages (3-10s as specified in README)
  sleep(Math.random() * 7 + 3);
}
