using UnityEditor;

namespace Coplay.Services
{
    /// <summary>
    /// Implementation of ICoplayWindowState that checks for open Coplay window instances
    /// </summary>
    public class CoplayWindowState : ICoplayWindowState
    {
        /// <summary>
        /// Gets a value indicating whether there are any open instances of the Coplay window
        /// </summary>
        public bool HasOpenInstances => EditorWindow.HasOpenInstances<Coplay>();
    }
}
