from lru import LRU

from .models import AssetGenerationT<PERSON>, CoplayTask, ImageGenerationTask


BACKGROUND_TASK_RESULTS: dict[str, CoplayTask] = LRU(size=100) # This determines the maximum number concurrent tasks/threads that can be running at once. Key is thread_id.
ASSET_GENERATION_JOBS: dict[str, AssetGenerationTask] = LRU(size=100)
IMAGE_GENERATION_TASKS: dict[str, ImageGenerationTask] = LRU(size=100)
