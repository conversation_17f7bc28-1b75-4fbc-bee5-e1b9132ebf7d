"use client";

import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Session } from "@supabase/supabase-js";

export function useResetPasswordAuth() {
  const [session, setSession] = useState<Session | null>(null);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // This will automatically pick up the access_token from the URL hash
    // and attempt to set a session if it's a valid recovery token.
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === "SIGNED_IN" && session) {
          setSession(session);
          // // You might want to remove the hash from the URL for cleaner UX
          // router.replace(router.url, undefined, { shallow: true });
        } else if (event === "SIGNED_OUT") {
          setSession(null);
        }
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [router, supabase.auth]);

  return { session };
}
