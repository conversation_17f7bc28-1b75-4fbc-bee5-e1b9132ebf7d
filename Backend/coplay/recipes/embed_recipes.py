"""
This script finds all .txt files in its directory, generates embeddings for their content,
and upserts them into the 'recipes' namespace in Turbopuffer.
"""
import os
import asyncio
import sys
import json
import argparse
from pathlib import Path

# Add the project root to the python path to allow for absolute imports.
# This allows the script to be run directly.
# The project root is 3 levels up from the script file.
# coplay/Backend/coplay/recipes/embed_recipes.py
sys.path.append(str(Path(__file__).resolve().parents[2]))

from coplay.turbopuffer import upsert_recipe_embeddings, get_all_vectors_in_namespace
from coplay.logger import logger

from prompt_utils import load_negative_prompts_only

async def main():
    """
    Finds and upserts recipe embeddings for the specified keyword.
    """
    parser = argparse.ArgumentParser(description="Embed recipe prompts for a specific keyword")
    parser.add_argument("keyword", help="The keyword to embed prompts for")
    args = parser.parse_args()
    
    keyword = args.keyword
    current_dir = Path(__file__).parent
    
    # Load negative prompts using the shared utility function
    recipe_texts = load_negative_prompts_only(keyword, current_dir)
    
    if not recipe_texts:
        logger.info(f"No recipe files with semantic text found for keyword '{keyword}' to embed.")
        return

    logger.info(f"Found {len(recipe_texts)} recipes to embed for keyword '{keyword}'. Upserting now...")
    
    await upsert_recipe_embeddings(input=recipe_texts, context_id=keyword)
    
    all_vectors = await get_all_vectors_in_namespace("recipes", context_id=keyword)
    
    logger.info(f"Successfully upserted {len(all_vectors)} recipe embeddings for keyword '{keyword}'.")

if __name__ == "__main__":
    asyncio.run(main())
