using System;
using Newtonsoft.Json;

namespace Coplay.Common.CoplayApi.Models
{
    /// <summary>
    /// Response model for USD credit balance information
    /// </summary>
    [Serializable]
    public class CreditBalanceResponse
    {
        /// <summary>
        /// The subscription credits remaining for the user (reset monthly)
        /// </summary>
        [JsonProperty("subscription_credits")]
        public float SubscriptionCredits { get; set; }
        
        /// <summary>
        /// The top-up credits remaining for the user (persistent)
        /// </summary>
        [JsonProperty("topup_credits")]
        public float TopupCredits { get; set; }
        
        /// <summary>
        /// The total USD credit balance remaining for the user
        /// </summary>
        [JsonProperty("total")]
        public float Total { get; set; }
    }
}
