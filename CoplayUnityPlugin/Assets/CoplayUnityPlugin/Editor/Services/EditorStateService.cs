using UnityEditor;

namespace Coplay.Services
{
    public class EditorStateService : IEditorStateService
    {
        public bool IsPlayingOrWillChangePlaymode => EditorApplication.isPlayingOrWillChangePlaymode;

        public bool IsPlaying
        {
            get => EditorApplication.isPlaying;
            set => EditorApplication.isPlaying = value;
        }

        public bool IsCompiling => EditorApplication.isCompiling;

        public bool IsUpdating => EditorApplication.isUpdating;
    }
}
