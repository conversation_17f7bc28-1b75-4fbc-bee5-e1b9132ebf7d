import os
import base64
from pathlib import Path
import re

from litellm import completion

from .models import AIModel
from .constants import OPENAI_KEY, GEMINI_API_KEY
from .model_to_video import create_rotating_video_subprocess
from .logger import logger
from .assistants.model_config import ModelConfig

## set ENV variables
os.environ["OPENAI_API_KEY"] = OPENAI_KEY
os.environ["ANTHROPIC_API_KEY"] = "your-cohere-key"
os.environ["GEMINI_API_KEY"] = GEMINI_API_KEY


def get_asset_description(asset_name: str) -> str:
    messages = [{
        "content": (
            f"Provide a brief short concise hypothetical description "
            f"of a 3d model named {asset_name}. "
            f"Do not include any other text. "
            f"The description should be a single sentence and not longer than the example shown below. "
            f"Do not use full sentences. "
            f"REMEMBER TO BE CONCISE. "
            f"You cannot ask for more details.\n\n"
            f"Example output: Small ornamental lantern for ambient lighting."
        ),
        "role": "user"
    }]

    # openai call
    response = completion(model="openai/gpt-4o-mini", messages=messages)

    # anthropic call
    # response = completion(model="anthropic/claude-3-sonnet-20240229", messages=messages)
    # print(response)
    return response["choices"][0]["message"]["content"]

def get_asset_description_from_model(fbx_path: str, model_name: str) -> str:
    video_filename = Path(fbx_path).name
    video_output_path = str(Path('generated_videos') / Path(video_filename).with_suffix('.mp4')) # NOTE: this might overwrite files if we have a lot of concurrent users
    create_rotating_video_subprocess(fbx_path, video_output_path)
    video_bytes = Path(video_output_path).read_bytes()
    encoded_data = base64.b64encode(video_bytes).decode("utf-8")
    
    system_prompt = """You are a helpful intelligent assistant that describes the 3d model in a video.
    The first frame of each video will contain a gizmo of arrows to indicate directions.
    Do not describe the gizmo in your description.

    If the model has an obvious direction, indicate which direction is forward for the model in a Unity friendly way. E.g. is the object Z-forward, or X-forward, etc.
    Otherwise, do not mention the direction, if all directions are roughly equivalent.
    If needed, try to describe the orientation of the model in a way that is easy to understand.

    Use the original file path as extra information to help you describe the object.

    Be concise.
    Only provide the text describing the object and nothing else.
    If the entire model has the pink/purple default color, then assume we don't have access to this extra information and do not include a description of the color or texture.
    If the entire model has the pink/purple default color, then rely more on the path information to describe the object.
    """
    user_prompt = f"""Look at this video.
        The first frame of the video will include a gizmo to indicate directions.
        The blue arrow is Z. The green arrow is Y. The red arrow is X.

        Given that the original file path of the model is, {model_name}, briefly describe the object you see rotated in the video.
        Do not mention the gizmo of arrows. I.e. you can ignore the gizmo in your description.
        
        If the model has an obvious direction, indicate which direction is forward for the model in a Unity friendly way. E.g. is the object Z-forward, or X-forward, etc.

        If the entire model has the pink/purple default color, then assume we don't have access to this extra information and do not include a description of the color or texture.
        If the entire model has the pink/purple default color, then rely more on the path information to describe the object.
        Be concise.
        Only provide the text describing the object and nothing else.

        Example description 1:
        "Small ornamental lantern. Up is X."

        Example description 2:
        "Small table with a vase on top. Up is X and forward is Z."

        Example description 3:
        "Rectangular block representing water."

        Example description 4:
        "Stylized, cartoon-like kitten with orange and brown tabby markings, large blue eyes, and a plump body. Z-forward."
        """
    messages=[
        {
            "role": "system",
            "content": system_prompt
        },
        {
            "role": "user",
            "content": [
                {"type": "text", "text": user_prompt},
                {
                    "type": "media",
                    "mime_type": "video/mp4",
                    "data": encoded_data,
                }
            ]
        }
    ]

    model_config = ModelConfig.create_for_model(AIModel.GEMINI_2_5_FLASH_LITE)
    response = model_config.llm.invoke(messages)
    description = response.text()
    os.remove(video_output_path)

    logger.info(f"Original description: {description}")
    # Gemini does not follow rules strictly enough so we need to clean up the output.
    description = description.strip('"').rstrip("\n")
    description = description.split('\n')[-1] # Only take the last line.
    description = re.sub(r'[\/\\][\w\-\.\/\\]+', '', description)  # Remove any path-like substrings
    parts = description.split(':')
    if len(parts) > 1 and parts[-1].strip() == '':
        description = parts[1] # If the model added a colon, then take everything after it.
    
    logger.info(f"Model description {video_filename}:")
    logger.info(description)

    return description