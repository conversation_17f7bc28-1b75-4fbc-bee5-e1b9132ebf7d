"""Tests for subscription credit balance operations."""

from __future__ import annotations

 
from datetime import datetime, timedelta
import os
import sys

import pytest
import types
import asyncio

# Ensure Backend and project roots are on the import path
backend_root = os.path.dirname(os.path.dirname(__file__))
project_root = os.path.dirname(backend_root)
sys.path.extend([backend_root, project_root])

from coplay.models import CreditBalance
from coplay.supabase_db import top_up_user_credits, renew_subscription_credits

# Stub out heavy modules before importing stripe router
sys.modules.setdefault("coplay.models", types.SimpleNamespace(TopUpRequest=object, TopUpResponse=object))
sys.modules.setdefault("coplay.routers.auth", types.SimpleNamespace(get_current_user=lambda: None))

from coplay.routers.stripe import create_subscription, SUBSCRIPTION_RENEWAL_CREDITS_USD


class MockTable:
    """Simple mock for a Supabase table."""
    def __init__(self, table_name: str, credits: dict[str, float]):
        self.table_name = table_name
        self.credits = credits

    def update(self, values: dict[str, float]):
        if self.table_name == "credits":
            if "topup_credits" in values:
                self.credits["topup_credits"] = float(values["topup_credits"])
            if "subscription_credits" in values:
                self.credits["subscription_credits"] = float(values["subscription_credits"])  # type: ignore[arg-type]
        return self

    def insert(self, values: dict):
        return self

    def eq(self, *_, **__):  # pragma: no cover - chain helper
        return self

    async def execute(self):  # pragma: no cover - asynchronous noop
        return None


class MockSupabaseClient:
    """Mock Supabase client returning :class:`MockTable` objects."""

    def __init__(self, credits: dict[str, float]):
        self.credits = credits

    def table(self, table_name: str) -> MockTable:
        return MockTable(table_name, self.credits)


@pytest.fixture
def credit_state() -> CreditBalance:
    """In-memory representation of a user's credit balances."""
    return CreditBalance(subscription_credits=0.0, topup_credits=0.0, total=0.0)


@pytest.fixture
def mock_supabase(credit_state: CreditBalance) -> MockSupabaseClient:
    return MockSupabaseClient(credit_state)


@pytest.fixture(autouse=True)
def patch_db(monkeypatch, mock_supabase: MockSupabaseClient, credit_state: CreditBalance):
    """Patch database access helpers to use in-memory state."""

    async def mock_get_admin_supabase_client():  # pragma: no cover - used in async context
        return mock_supabase

    async def mock_get_user_credit_balance(_user_id: str):
        return credit_state

    monkeypatch.setattr(
        "coplay.supabase_db.get_admin_supabase_client", mock_get_admin_supabase_client
    )
    monkeypatch.setattr(
        "coplay.supabase_db.get_user_credit_balance", mock_get_user_credit_balance
    )


def test_top_up_user_credits_increases_balance(credit_state: CreditBalance):
    credit_state.subscription_credits = 10.0
    credit_state.topup_credits = 2.0

    async def run():
        return await top_up_user_credits("user", 5.0, "txn")

    result = asyncio.run(run())

    assert result.subscription_credits == 10.0
    assert result.topup_credits == 7.0
    assert result.total == 17.0


def test_renew_subscription_credits_resets_balance(credit_state: CreditBalance):
    credit_state.subscription_credits = 5.0
    credit_state.topup_credits = 3.0

    async def run():
        return await renew_subscription_credits("user", 40.0, "sub")

    result = asyncio.run(run())

    assert result.subscription_credits == 40.0
    assert result.topup_credits == 3.0
    assert result.total == 43.0


def test_create_subscription_sets_initial_balance(
    mock_supabase: MockSupabaseClient, credit_state: CreditBalance, monkeypatch: pytest.MonkeyPatch
):
    start = datetime.utcnow()
    end = start + timedelta(days=30)

    # Capture the return value of renew_subscription_credits when called by create_subscription
    captured: list[CreditBalance] = []
    original_renew = renew_subscription_credits

    async def capture_renew(user_id: str, amount_usd: float, transaction_id: str):
        result = await original_renew(user_id, amount_usd, transaction_id)
        captured.append(result)
        return result

    monkeypatch.setattr("coplay.routers.stripe.renew_subscription_credits", capture_renew)

    async def run():
        return await create_subscription(
            mock_supabase,
            user_id="user",
            plan_id="plan",
            stripe_subscription_id="sub_123",
            status="active",
            current_period_start=start,
            current_period_end=end,
        )

    asyncio.run(run())

    assert len(captured) == 1
    result = captured[0]
    assert result.subscription_credits == SUBSCRIPTION_RENEWAL_CREDITS_USD
    assert result.topup_credits == 0.0
    assert result.total == SUBSCRIPTION_RENEWAL_CREDITS_USD

