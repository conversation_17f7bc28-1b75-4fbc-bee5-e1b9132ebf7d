# Environment files (contains sensitive tokens)
.env
.env.local
.env.*.local

# k6 test results and outputs
device_update.json
*.html
*.xml
results/
reports/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary files
tmp/
temp/
.tmp/

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
.sublime-*

# Node.js (if using any Node helpers)
node_modules/
package-lock.json
yarn.lock

# Python (for helpers/stream_metrics.py)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python virtual environments
venv/
ENV/
env/
.venv/
.ENV/
.env/

# Backup files
*.bak
*.backup
*.old

# System files
.fseventsd
.VolumeIcon.icns
