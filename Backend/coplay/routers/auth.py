from typing import Optional
from datetime import datetime, timedelta, timezone
from pydantic import BaseModel

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
import uuid
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2PasswordBearer
from supabase import AsyncClient
from supabase_auth.types import User
import httpx

from ..opentelemetry.helpers import CoplayAttributes, set_trace_attribute
from ..supabase_db import get_admin_supabase_client, get_user_supabase_client, connection_pool, get_user_credit_balance
from ..models import CreateDeviceResponse, CreditBalance, GetDeviceResponse, AnthropicKeyRequest
from ..logger import logger
from ..constants import SUPABASE_URL, SUPABASE_PUBLIC_KEY
from ..cache import CacheNames, cache

router = APIRouter(
    prefix="/auth",
    tags=["auth"],
)

templates = Jinja2Templates(directory="templates")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)

class TokenRefreshRequest(BaseModel):
    refresh_token: str

def set_user_attributes(user: User) -> None:
    set_trace_attribute(CoplayAttributes.USER_ID, user.id)
    set_trace_attribute(CoplayAttributes.USER_EMAIL, user.email)

async def get_current_user(token: str = Depends(oauth2_scheme), supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> User:
    # This validates the token
    try:
        cached_user = await cache.get(token, namespace=CacheNames.USERS)
        if cached_user:
            set_user_attributes(cached_user)
            return cached_user
        user = await supabase_client.auth.get_user(token)
        if not user:
            raise HTTPException(status_code=401, detail="Could not validate credentials")
        await cache.set(token, user.user, ttl=60 * 5, namespace=CacheNames.USERS) # 5 minutes
        set_user_attributes(user.user)
        return user.user
    except Exception as e:
        logger.error(f"Failed to validate token: {str(e)}")
        raise HTTPException(status_code=401, detail="Could not validate credentials")
    
async def get_current_user_optional(token: str = Depends(oauth2_scheme_optional), supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> Optional[User]:
    # This validates the token
    try:
        cached_user = await cache.get(token, namespace=CacheNames.USERS)
        if cached_user:
            return cached_user
        user = await supabase_client.auth.get_user(token)
        return user.user
    except Exception as e:
        return None

async def get_validated_device_id(request: Request, current_user: User = Depends(get_current_user), supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> str:
    device_id = request.headers.get("x-device-id")
    if not device_id:
        raise HTTPException(status_code=400, detail="Device ID is required")
    
    cached_device_id = await cache.get(f"{current_user.id}_{device_id}", namespace=CacheNames.DEVICES)
    if cached_device_id is not None:
        set_trace_attribute(CoplayAttributes.DEVICE_ID, cached_device_id)
        return cached_device_id
    
    result = await supabase_client.table('devices').select("id").eq('id', device_id).eq('user_id', current_user.id).execute()
    if not result.data or len(result.data) == 0:
        raise HTTPException(status_code=404, detail="Device not found")
    
    validated_device_id = result.data[0]['id']

    # Cache the device ID for 24 hours
    await cache.set(f"{current_user.id}_{device_id}", validated_device_id, ttl=60 * 60 * 24, namespace=CacheNames.DEVICES) # 24 hours
    set_trace_attribute(CoplayAttributes.DEVICE_ID, validated_device_id)
    
    return validated_device_id

async def validate_thread_belongs_to_user(thread_id: str, device_id: str):
    async with connection_pool.connection() as conn:
        result = await conn.execute("SELECT * FROM threads WHERE id = %s AND device_id = %s", (thread_id, device_id))
        if await result.fetchone() is None:
            raise HTTPException(status_code=404, detail="Thread not found")
        set_trace_attribute(CoplayAttributes.THREAD_ID, thread_id)


@router.get("/login")
async def serve_login_page(request: Request, device_id: str, supabase_client: AsyncClient=Depends(get_admin_supabase_client)):
    if not device_id:
        raise HTTPException(status_code=400, detail="Device ID is required")
    
    result = await supabase_client.table('devices').select("*").eq('id', device_id).execute()
    if not result.data or len(result.data) == 0:
        raise HTTPException(status_code=404, detail="Device not found")
    
    """Serve the static login page"""
    return templates.TemplateResponse(
        request=request, name="login.html", context={"device_id": device_id, "supabase_url": SUPABASE_URL, "supabase_public_key": SUPABASE_PUBLIC_KEY}
    )

@router.get("/device/{device_id}/callback")
async def callback(request: Request, device_id: str):
    """Endpoint to handle the callback from the auth provider."""
    return templates.TemplateResponse(
        request=request, name="login.html", context={"device_id": device_id, "supabase_url": SUPABASE_URL, "supabase_public_key": SUPABASE_PUBLIC_KEY}
    )

@router.post("/device/{device_id}/refresh")
async def refresh_tokens(request: TokenRefreshRequest, device_id: str, supabase_client: AsyncClient=Depends(get_admin_supabase_client)):
    """Endpoint to refresh the tokens for a device."""
    refresh_token = request.refresh_token
    
    # Verify the refresh token belongs to this device
    result = await supabase_client.table('devices').select("refresh_token").eq('id', device_id).execute()
    if not result.data or len(result.data) == 0:
        raise HTTPException(status_code=404, detail="Device not found")
    if result.data[0]['refresh_token'] != refresh_token:
        raise HTTPException(status_code=401, detail="Invalid refresh token for this device")
    
    # We need this to avoid overwriting the admin client session
    user_client = await get_user_supabase_client()
    
    try:
        auth_response = await user_client.auth.refresh_session(refresh_token)
        access_token = auth_response.session.access_token
        new_refresh_token = auth_response.session.refresh_token
    except Exception as e:
        logger.error(f"Failed to refresh tokens: {str(e)}")
        raise HTTPException(status_code=401, detail="Failed to refresh tokens")

    result = await supabase_client.table('devices').update({
        'access_token': access_token,
        'refresh_token': new_refresh_token
    }).eq('id', device_id).execute()

    return JSONResponse(content={
        "access_token": access_token,
        "refresh_token": new_refresh_token
    })

@router.post("/device")
async def create_device(supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> CreateDeviceResponse:
    """Endpoint to generate a temporary device ID."""
    device_id = f'cpl-device-{uuid.uuid4()}'
    
    # Insert the new device into Supabase
    try:
        await supabase_client.table('devices').insert({
            'id': device_id
        }).execute()
    except Exception as e:
        logger.error(f"Failed to create device: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create device")

    return CreateDeviceResponse(device_id=device_id)

@router.get("/device/{device_id}")
async def get_device(device_id: str, supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> GetDeviceResponse:
    """Endpoint to retrieve a device by its ID, expect the app to poll for updates"""
    try:
        result = await supabase_client.table('devices') \
            .select("access_token, refresh_token").eq('id', device_id).gt('updated_at', datetime.now(timezone.utc) - timedelta(minutes=1)).execute()
        if result.data and len(result.data) > 0:
            return GetDeviceResponse(**result.data[0])
        else:
            raise HTTPException(status_code=404, detail="Device not found")
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to retrieve device: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve device")

@router.post("/device/{device_id}")
async def update_device(request: Request, device_id: str, supabase_client: AsyncClient=Depends(get_admin_supabase_client)) -> dict:
    """Endpoint to update a device by its ID."""
    try:
        data = await request.json()
        # Make sure the access token is valid
        user = await supabase_client.auth.get_user(data.get('access_token'))
        if not user:
            raise HTTPException(status_code=400, detail="Invalid access token")
        
        # Make sure the device exists
        result = await supabase_client.table('devices').select("*").eq('id', device_id).execute()
        if not result.data or len(result.data) == 0:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Make sure the device is associated with the user or new to prevent device hijacking
        device_user_id = result.data[0].get('user_id')
        
        if device_user_id is not None and device_user_id != user.user.id:
            raise HTTPException(status_code=400, detail="Invalid access token, are you logging in with the correct account? Clear settings to reset.")

        # Update the device with the new user ID
        result = await supabase_client.table('devices').update({
            'updated_at': 'now()',
            'access_token': data.get('access_token'),
            'refresh_token': data.get('refresh_token'),
            'user_id': user.user.id
        }).eq('id', device_id).execute()
            
        return {"message": "Device updated successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to update device: {str(e)}")
        raise HTTPException(status_code=500, detail="An internal error occurred while updating the device")

@router.post("/anthropic-key")
async def validate_and_save_anthropic_key(
    request: AnthropicKeyRequest, 
    current_user: User = Depends(get_current_user),
    supabase_client: AsyncClient = Depends(get_admin_supabase_client)
) -> dict:
    """Endpoint to validate and save an Anthropic API key."""
    try:
        # Validate the API key by making a test request to Anthropic API
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "x-api-key": request.api_key,
                "anthropic-version": "2023-06-01",
                "content-type": "application/json"
            }
            
            # Make a minimal request to validate the key
            response = await client.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json={
                    "model": "claude-3-haiku-20240307",
                    "max_tokens": 10,
                    "messages": [{"role": "user", "content": "Hello"}]
                }
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to validate Anthropic API key: {response.text}")
                # Check if a record already exists for this user and delete it
                existing_key_result = await supabase_client.table('user_api_keys').select("user_id").eq('user_id', current_user.id).execute()
                if existing_key_result.data and len(existing_key_result.data) > 0:
                    await supabase_client.table('user_api_keys').delete().eq('user_id', current_user.id).execute()
                    await cache.delete(current_user.id, namespace=CacheNames.ANTHROPIC_KEYS)
                    logger.info(f"Deleted existing Anthropic key for user {current_user.id} due to invalid new key.")
                raise HTTPException(status_code=400, detail="Invalid Anthropic API key")
            
        # Save the API key to Supabase
        # First check if a record already exists for this user
        result = await supabase_client.table('user_api_keys').select("*").eq('user_id', current_user.id).execute()
        
        if result.data and len(result.data) > 0:
            # Update existing record
            await supabase_client.table('user_api_keys').update({
                'anthropic_key': request.api_key,
                'updated_at': 'now()'
            }).eq('user_id', current_user.id).execute()
        else:
            # Insert new record
            await supabase_client.table('user_api_keys').insert({
                'user_id': current_user.id,
                'anthropic_key': request.api_key
            }).execute()
        
        await cache.set(current_user.id, request.api_key, ttl=60 * 60 * 24, namespace=CacheNames.ANTHROPIC_KEYS) # 24 hours
            
        return {"message": "Anthropic API key validated and saved successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to validate and save Anthropic API key: {str(e)}")
        raise HTTPException(status_code=500, detail="An internal error occurred while processing the API key")

@router.delete("/anthropic-key")
async def delete_anthropic_key(
    current_user: User = Depends(get_current_user),
    supabase_client: AsyncClient = Depends(get_admin_supabase_client)
) -> dict:
    """Endpoint to delete an Anthropic API key."""
    try:
        # Check if a record exists for this user
        result = await supabase_client.table('user_api_keys').select("user_id").eq('user_id', current_user.id).execute()
        
        if result.data and len(result.data) > 0:
            # Delete the record
            await supabase_client.table('user_api_keys').delete().eq('user_id', current_user.id).execute()
            logger.info(f"Deleted Anthropic key for user {current_user.id}")
            await cache.delete(current_user.id, namespace=CacheNames.ANTHROPIC_KEYS)
            return {"message": "Anthropic API key deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="No Anthropic API key found for this user")
    except Exception as e:
        logger.error(f"Failed to delete Anthropic API key: {str(e)}")
        raise HTTPException(status_code=500, detail="An internal error occurred while deleting the API key")

@router.get("/user/credits")
async def get_user_credits(
    current_user: User = Depends(get_current_user),
) -> CreditBalance:
    """Endpoint to get the remaining USD credit balance for the current user."""
    try:
        credit_balance = await get_user_credit_balance(current_user.id)
        
        if credit_balance is None:
            raise HTTPException(status_code=404, detail="Credit balance not found")
            
        return credit_balance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to get credit balance: {str(e)}")
        raise HTTPException(status_code=500, detail="An internal error occurred while getting credit balance")

@router.get("/user/tokens")
async def get_user_tokens(
    current_user: User = Depends(get_current_user),
) -> dict:
    """Endpoint to get the remaining token credits for the current user. DEPRECATED: Use /user/credits instead."""
    try:
        from coplay.supabase_db import get_user_token_usage
        
        tokens_remaining = await get_user_token_usage(current_user.id)
        
        if tokens_remaining is None:
            raise HTTPException(status_code=404, detail="Token usage not found")
            
        return {"tokens_remaining": tokens_remaining}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to get token usage: {str(e)}")
        raise HTTPException(status_code=500, detail="An internal error occurred while getting token usage")

@router.get("/user/subscription")
async def get_user_subscription(
    current_user: User = Depends(get_current_user),
    supabase_client: AsyncClient = Depends(get_admin_supabase_client)
) -> dict:
    """Endpoint to get the user's subscription details."""
    try:
        # Get the user's active subscription
        subscription_result = await supabase_client.table('subscriptions')\
                               .select('id,plan_id,status,current_period_start,current_period_end,stripe_subscription_id')\
                               .eq('user_id', current_user.id)\
                               .eq('status', 'active')\
                               .execute()
        
        if not subscription_result.data or len(subscription_result.data) == 0:
            # No active subscription found
            return {
                "has_subscription": False,
                "subscription": None,
                "plan": None
            }
        
        subscription = subscription_result.data[0]
        plan_id = subscription['plan_id']
        
        # Get the plan details
        plan_result = await supabase_client.table('plans')\
                         .select('id,name,monthly_credits,price')\
                         .eq('id', plan_id)\
                         .execute()
        
        if not plan_result.data or len(plan_result.data) == 0:
            # Plan not found - unusual case but should be handled
            logger.error(f"Plan {plan_id} not found for subscription {subscription['id']}")
            return {
                "has_subscription": True,
                "subscription": subscription,
                "plan": None
            }
        
        plan = plan_result.data[0]
        
        return {
            "has_subscription": True,
            "subscription": subscription,
            "plan": plan
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error getting user subscription: {e}")
        raise HTTPException(status_code=500, detail="An internal error occurred while getting subscription details")
