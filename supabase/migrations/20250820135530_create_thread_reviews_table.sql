-- Create thread_reviews table for automatic thread review feature
CREATE TABLE IF NOT EXISTS thread_reviews (
    id TEXT PRIMARY KEY,
    previous_thread_id TEXT NOT NULL,
    new_thread_id TEXT NOT NULL,
    suggestion TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_previous_thread FOREIGN KEY (previous_thread_id) REFERENCES threads(id) ON DELETE CASCADE,
    CONSTRAINT fk_new_thread FOREIGN KEY (new_thread_id) REFERENCES threads(id) ON DELETE CASCADE
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_thread_reviews_new_thread_id ON thread_reviews(new_thread_id);
CREATE INDEX IF NOT EXISTS idx_thread_reviews_status ON thread_reviews(status);
CREATE INDEX IF NOT EXISTS idx_thread_reviews_created_at ON thread_reviews(created_at);
