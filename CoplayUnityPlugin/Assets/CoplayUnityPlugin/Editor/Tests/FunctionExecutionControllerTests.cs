using Coplay.Common.CoplayApi;
using Coplay.Controllers.Assistants;
using Coplay.Controllers.Functions;
using Coplay.Controllers.Systems;
using Coplay.Models.Configuration;
using Coplay.Services;
using Coplay.Services.Chat;
using Coplay.Common;
using Moq;
using NUnit.Framework;
using System.IO.Abstractions.TestingHelpers;
using Microsoft.Extensions.DependencyInjection;
using System.IO.Abstractions;
using Coplay.Models.ContextProviders;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Threading.Tasks;
using System.Collections;
using UnityEngine.TestTools;
using UnityEngine;
using System.Reactive.Linq;
using Coplay.Models.Assistants;
using Coplay.DependencyInjection;

public class FunctionExecutionControllerTests
{
    private FunctionExecutionController _functionExecutionController;
    private StateController _stateController;
    private readonly MockFileSystem _fileSystem = new();
    private ServiceProvider _serviceProvider;
    private Mock<ICoplayApiClient> _coplayApiClientMock;
    private Mock<IToolFunctionsProvider> _toolFunctionsProviderMock;
    private Mock<IEditorStateService> _editorStateServiceMock;
    private Mock<ICompilationService> _compilationServiceMock;
    private Mock<IChatThreads> _chatThreadsMock;
    private Mock<ICoplayWindowState> _coplayWindowStateMock;

    [SetUp]
    public void Setup()
    {
        _coplayApiClientMock = new Mock<ICoplayApiClient>();
        _toolFunctionsProviderMock = new Mock<IToolFunctionsProvider>();
        _editorStateServiceMock = new Mock<IEditorStateService>();
        _compilationServiceMock = new Mock<ICompilationService>();

        _chatThreadsMock = new Mock<IChatThreads>();
        _chatThreadsMock.SetupGet(x => x.ThreadLoadedObservable)
            .Returns(Observable.Return(new CoplayThread()));

        _coplayWindowStateMock = new Mock<ICoplayWindowState>();
        _coplayWindowStateMock.SetupGet(x => x.HasOpenInstances).Returns(true);

        _serviceProvider = new ServiceCollection()
            .RegisterCoplayServices()
            .AddSingleton<IApplicationPaths>(new ApplicationPaths("C:/CoplayTests"))
            .AddSingleton<IFileSystem>(_fileSystem)
            .AddSingleton(_coplayApiClientMock.Object)
            .AddSingleton(_toolFunctionsProviderMock.Object)
            .AddSingleton(_editorStateServiceMock.Object)
            .AddSingleton<IReadOnlyDictionary<string, IContextProvider>>(new Dictionary<string, IContextProvider>().ToImmutableDictionary())
            .AddSingleton(_compilationServiceMock.Object)
            .AddSingleton(_chatThreadsMock.Object)
            .AddSingleton(_coplayWindowStateMock.Object)
            .BuildServiceProvider();

        _stateController = _serviceProvider.GetRequiredService<StateController>();
        _functionExecutionController = _serviceProvider.GetRequiredService<FunctionExecutionController>();
    }

    [TearDown]
    public void TearDown()
    {
        _serviceProvider.Dispose();
    }

    [UnityTest]
    public IEnumerator TestExecuteAllInfiniteRecursion()
    {
        _stateController.SetState(CoplayState.ExecutingAllFunctions);

        while (_stateController.CurrentState != CoplayState.ExecutingAllFunctions)
        {
            // Wait for the state to change
            yield return null;
        }

        var startTime = Time.realtimeSinceStartup;
        var testTask = DoTest();
        while (!testTask.IsCompleted && Time.realtimeSinceStartup - startTime < 1f)
        {
            // Wait for the task to complete or timeout after 1 second
            yield return null;
        }

        Assert.IsTrue(testTask.IsCompleted, "The test task should complete within the timeout period.");
        Assert.IsTrue(testTask.IsCompletedSuccessfully, "The test task should complete successfully.");

        async Task DoTest()
        {
            await _functionExecutionController.ExecuteAllFunctions();
        }
    }
}
