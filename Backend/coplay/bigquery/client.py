import asyncio
from typing import Any, Dict, List
from typing import Any, Dict, List

from google.cloud import bigquery

from ..constants import GCP_PROJECT_ID
from ..logger import logger


# Global BigQuery client instance
_bigquery_client: bigquery.Client | None = None


def _get_bigquery_client() -> bigquery.Client:
    """Get or create BigQuery client instance"""
    global _bigquery_client
    if _bigquery_client is None:
        _bigquery_client = bigquery.Client(project=GCP_PROJECT_ID)
    return _bigquery_client


existing_tables = set()


async def ensure_dataset_and_table(dataset_id: str, table_id: str, schema: List[bigquery.SchemaField]):
    """Ensure the dataset and table exist - generalized for any table"""
    if f"{dataset_id}.{table_id}" not in existing_tables:
        await asyncio.to_thread(_ensure_dataset_and_table, dataset_id, table_id, schema)
        existing_tables.add(f"{dataset_id}.{table_id}")


def _ensure_dataset_and_table(dataset_id: str, table_id: str, schema: List[bigquery.SchemaField]):
    """Ensure the dataset and table exist - generalized for any table"""
    try:
        client = _get_bigquery_client()
        
        # Check if dataset exists, create if not
        dataset = bigquery.Dataset(f"{GCP_PROJECT_ID}.{dataset_id}")
        try:
            client.get_dataset(dataset)
        except Exception:
            # Dataset doesn't exist, create it
            dataset = client.create_dataset(dataset)
            logger.info(f"Created BigQuery dataset: {dataset_id}")
        
        # Check if table exists, create if not
        table_ref = client.dataset(dataset_id).table(table_id)
        try:
            client.get_table(table_ref)
        except Exception:
            # Table doesn't exist, create it
            table = bigquery.Table(table_ref, schema=schema)
            table = client.create_table(table)
            logger.info(f"Created BigQuery table: {table_id}")
            
    except Exception as e:
        logger.error(f"Error ensuring BigQuery dataset and table: {e}")
        raise


async def insert_rows_to_table(dataset_id: str, table_id: str, rows: List[Dict[str, Any]]) -> bool:
    """Generic function to insert rows to any BigQuery table"""
    try:
        client = _get_bigquery_client()
        table_ref = client.dataset(dataset_id).table(table_id)
        errors = await asyncio.to_thread(client.insert_rows_json, table_ref, rows)
        
        if errors:
            logger.error(f"BigQuery insert errors: {errors}")
            return False
        
        logger.info(f"Successfully inserted {len(rows)} rows to BigQuery table {dataset_id}.{table_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error inserting rows to BigQuery table {dataset_id}.{table_id}: {e}")
        return False

 