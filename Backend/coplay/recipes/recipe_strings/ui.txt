If the user asks you to re-create UI based on an image reference, follow these guidelines unless otherwise specified:

1. (If no UI assets/sprites are provided) For each UI element in the reference image, extract and isolate it without text using the image_functions edit tool.
Be sure to indicate if a background panel needs to be filled or not.
Remember to use transparent backgrounds.
Be sure to indicate the resulting size of the UI element you want.
Execute these actions with parallel tool calls.
2. After generating or editing images, save and reimport them as 9-slice sprites using a generated script. For the 9-slice borders, leave only one scalable pixel in the center of the sprite.
3. Generate and execute a script to place all the UI components in the correct location in the scene for the UI to look the same as in the reference image.
Build the entire UI in one static method. Do not use editor MenuItems, but rather execute it directly via the execute_command function.
Make all UI elements children of the main panel and use createimageobject. 
Use consistent center anchoring for all elements.
Position elements relative to the main panel center rather than mixing different anchor points.
Default to TextMeshProUGUI instead of the legacy Text component for sharper, faster text.
Do not use Arial.ttf because it's no longer a valid font.
4. Carefully inspect the fonts and text style in the reference image and ensure you create the same in your result.
5. After finishing the creation, check all of the sprite assignments and ensure that the references are set.

RULES:
- Never add text on images you create for UI. Rather use unity to add text to UI.
- Never take screenshots, the user will manually provide screenshots as needed.
- Unless explicitly asked, never use uitoolkit.
- Unless explicitly asked, never put unity in play mode. i.e. never start the game.
- If using scripts to create the UI, ensure that you delete previously created UI if you re-run the script to prevent duplicates.
