using System;
using Coplay.Common.CoplayApi;
using Coplay.Services.Chat;
using Serilog;
using Serilog.Configuration;

namespace Coplay.Common.Logging.Sinks.CoplayApi
{
    public static class SerilogCoplayApiConfigurationExtensions
    {
        public static LoggerConfiguration CoplayApi(
            this LoggerSinkConfiguration loggerSinkConfiguration,
            ICoplayApiClient coplayApiClient,
            ICoplayThreadContext threadContext)
        {
            if (loggerSinkConfiguration == null) throw new ArgumentNullException(nameof(loggerSinkConfiguration));
            if (coplayApiClient == null) throw new ArgumentNullException(nameof(coplayApiClient));
            if (threadContext == null) throw new ArgumentNullException(nameof(threadContext));

            return loggerSinkConfiguration.Sink(new CoplayApiSink(coplayApiClient, threadContext));
        }
    }
}
