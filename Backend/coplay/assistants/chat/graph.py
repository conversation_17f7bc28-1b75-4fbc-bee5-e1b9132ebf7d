import asyncio
import hashlib
import json
from time import perf_counter
import traceback

from langchain_core.messages import ToolMessage, RemoveMessage, ToolCall
from langchain.schema import HumanMessage, SystemMessage, BaseMessage, AIMessage
from langgraph.prebuilt import ToolNode
from langgraph.graph import START, END, StateGraph
from langgraph.types import StreamWriter, Command
from langgraph.checkpoint.memory import MemorySaver
from fastapi_events.dispatcher import dispatch
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_litellm import ChatLiteLLM

from .messages import add_cache_control_params, create_human_message_with_optional_images, get_last_context_hash, get_last_prompt, strip_old_context, strip_unsupported_blocks
from ..model_config import ModelConfig, is_claude_model, TIMEOUT_SECONDS
from ..context_management import prune_messages_to_fit, strip_error_messages
from ...events import CoplayEvent
from ...supabase_db import get_admin_supabase_client, get_user_anthropic_key
from ...storage import save_b64_to_supabase
from ...models import AIModel, CoplayRun, MessageType, AgentState, Prompt, ToolCallOutcome, ToolCallResult, ToolDescriptionOutput
from ...constants import AVERAGE_CHARS_PER_TOKEN, GEMINI_API_KEY, OPENAI_KEY
from ...prompts import (
    TOOL_DESCRIPTION_PROMPT,
    TOOL_DESCRIPTION_HUMAN_MESSAGE,
    CHAT_USER_MESSAGE_PROMPT,
    CONTEXT_FILTER_PROMPT,
    UPDATED_CONTEXT_PROMPT,
)
from ...logger import logger
from ...context.tools import SERVER_SIDE_TOOLS
from ...recipes.recipe_service import get_recipe
# from .utils import format_messages_for_logging
from ...usage_calculation import calculate_usage
from ...notifications import DiscordNotifier
from ...langsmith_url_service import get_langsmith_trace_url
from ...cache import cache, CacheNames


def _make_model_instances() -> dict[str, ModelConfig]:
    output = {}
    for model in AIModel:
        # Initialize for normal mode
        output[model.value] = ModelConfig.create_for_model(model)
    return output

# Initialize all models
MODEL_INSTANCES = _make_model_instances()

# These are shared across all models
CONTEXT_FILTER_LLM = ChatLiteLLM(
    model="gemini/gemini-2.0-flash",
    api_key=GEMINI_API_KEY,
    temperature=0,
)

TOOL_DESCRIPTION_LLM = ChatGoogleGenerativeAI(
    model="gemini-2.5-flash-lite",
    api_key=GEMINI_API_KEY,
    temperature=0
)

CONTEXT_SELECTION_LLM = ChatLiteLLM(
    model="gpt-4o", # Note: no genius reason for hardcoding this model. We need a model that is decent and not slow for selecting context.
    openai_api_key=OPENAI_KEY,
    temperature=1
)


async def _check_tool_outputs(state: AgentState, writer: StreamWriter) -> AgentState:
    writer({"coplay": "Processing action results"})
    # Tool outputs need to match exactly the tool calls that were made in the last AI message.
    # So we discard any tool outputs that are not in the last AI message.
    # We also assume tool calls were not executed if they are not in the tool outputs.
    # logger.info(f"messages at the start: {format_messages_for_logging(state['messages'][2:])}")

    messages = strip_error_messages(state["messages"])

    if len(messages) == 0:
        logger.warning("No messages found in check_tool_outputs")
        return state

    last_message: BaseMessage = messages[-1]
    if not isinstance(last_message, AIMessage):
        return state
    
    tool_calls = last_message.tool_calls
    tool_outputs = state["prompt"].tool_outputs

    tool_call_ids: dict[str, ToolCall] = {tc.get("id"): tc for tc in tool_calls}

    for output in tool_outputs:
        # Make sure the tool output matches the tool call.
        if output.tool_call_id not in tool_call_ids:
            logger.warning(f"Ignoring tool output {output.tool_call_id} because it is not in the last AI message")
            continue
        else:
            tool_call = tool_call_ids.pop(output.tool_call_id)
            state["messages"] += [ToolMessage(content=output.tool_output, artifact=None, tool_call_id=output.tool_call_id)]
            error = None
            if output.tool_output.startswith("Cancelled: "):
                outcome = ToolCallOutcome.CANCELLED
            elif output.tool_output.startswith("Error: "):
                outcome = ToolCallOutcome.ERROR
                error = output.tool_output
            else:
                outcome = ToolCallOutcome.SUCCESS
            
            dispatch(CoplayEvent.TOOL_CALL_RESULT, ToolCallResult(
                user_id=state["user"].id,
                thread_id=state["thread_id"],
                message_id=last_message.id,
                tool_call_id=output.tool_call_id,
                tool_name=tool_call.get("name"),
                outcome=outcome,
                error=error,
                model=state["current_model"],
                mode=state["assistant_mode"],
                system_details=state["prompt"].get_system_details(),
                coplay_version=state["coplay_version"],
            ))
    
    if len(tool_call_ids) > 0:
        # We have tool outputs missing
        logger.warning(f"Tool call ids {tool_call_ids} not found in tool call ids of outputs provided: {[output.tool_call_id for output in tool_outputs]}")
        # Assume tool calls were not executed.
        for tool_call_id in tool_call_ids:
            state["messages"] += [ToolMessage(content=f"Tool call {tool_call_id} was not executed.", artifact=None, tool_call_id=tool_call_id)]
            dispatch(CoplayEvent.TOOL_CALL_RESULT, ToolCallResult(
                user_id=state["user"].id,
                thread_id=state["thread_id"],
                message_id=last_message.id,
                tool_call_id=tool_call_id,
                tool_name=tool_call_ids[tool_call_id].get("name"),
                outcome=ToolCallOutcome.MISSING,
                error=None,
                model=state["current_model"],
                mode=state["assistant_mode"],
                system_details=state["prompt"].get_system_details(),
                coplay_version=state["coplay_version"],
            ))
    
    # If all cancelled, let's ask the user what else they would like to do
    if len(tool_outputs) > 0 and all(output.tool_output.startswith("Cancelled: ") for output in tool_outputs):
        logger.debug("All functions were cancelled by user")
        state["messages"] += [
            AIMessage(content=[{"type": "text", "text": "The user has cancelled all functions. Acknowledge this and ask what else you can help with."}])
        ]

    # logger.info(f"messages after check_tool_outputs: {format_messages_for_logging(state['messages'][2:])}")
    
    return state

async def _process_context(state: AgentState, writer: StreamWriter) -> AgentState:
    # TODO: simplify this significantly as the context is mostly processed by tool calls now.
    writer({"coplay": "Processing context"})
    supabase_client = await get_admin_supabase_client() # TODO: This is not used anywhere.
    current_prompt = state["prompt"]
    last_user_prompt = await get_last_prompt(state)
    if last_user_prompt is None or current_prompt.prompt != "":
        last_user_prompt = current_prompt

    # Get the model configuration for this request
    model_config = MODEL_INSTANCES[state["current_model"]]

    contexts = {}
    context_token_counts = {}  # Add this line to track token counts for each context
    extracted_image_urls = []
    
    # Process file attachments if available
    if last_user_prompt.context.get("file_attachments", "") != "":
        try:
            file_attachments = json.loads(last_user_prompt.context.get("file_attachments"))
            
            # Track which files have been processed in this thread
            thread_id = state["thread_id"]
            cached_processed_files = await cache.get(thread_id, namespace=CacheNames.PROCESSED_FILES, default={})
            
            # Process each file attachment
            # TODO; refactor this function in general, but this file attachment could move to separate function.
            processed_attachments = []
            for attachment in file_attachments:
                file_path = attachment["FilePath"]
                content = attachment["Content"]
                file_type = attachment["FileType"]
                
                # Compute a hash of the content to detect changes
                content_hash = hashlib.sha256(content.encode()).hexdigest()
                
                # Check if this file with this content has been processed before
                if file_path in cached_processed_files and cached_processed_files[file_path] == content_hash:
                    # Skip this file as it has been processed before with the same content
                    logger.info(f"Skipping already processed file: {file_path}")
                    continue
                
                # Store the hash for future reference
                cached_processed_files[file_path] = content_hash
                
                # Format the content based on file type
                formatted_content = ""
                if file_type == "text":
                    formatted_content = f"### File: {file_path}\n```text\n{content}\n```"
                elif file_type == "scene":
                    formatted_content = f"### Scene File: {file_path}\n```json\n{content}\n```"
                elif file_type == "prefab":
                    formatted_content = f"### Prefab File: {file_path}\n```json\n{content}\n```"
                elif file_type == "script":
                    formatted_content = f"### Script File (from Prefab): {file_path}\n```csharp\n{content}\n```"
                elif file_type == "asset":
                    formatted_content = f"### ScriptableObject Asset: {file_path}\n```json\n{content}\n```"
                elif file_type == "image":
                    formatted_content = f"### Image File: {file_path}"
                    try:
                        image_url = await save_b64_to_supabase(content, supabase_client)
                        formatted_content += f"\n![Image]({image_url})"
                        extracted_image_urls.append(image_url)
                    except Exception as e:
                        logger.error(f"Error saving image to supabase: {e}")
                else:
                    formatted_content = f"### {content}"
                
                processed_attachments.append(formatted_content)
                # TODO: instead of attaching the whole file, we should attach only the diff.
            
            await cache.set(thread_id, cached_processed_files, namespace=CacheNames.PROCESSED_FILES)
            
            if processed_attachments:
                attachment_content = "\n\n".join(processed_attachments) + "\n\n"
                contexts["Attachments"] = attachment_content
                context_token_counts["Attachments"] = model_config.count_tokens(text=attachment_content)
            
        except Exception as e:
            logger.error(f"Error processing file attachments: {e}")
    
    if last_user_prompt.context.get("user_action_logs", "") != "":
        action_logs_content = f'{last_user_prompt.context.get("user_action_logs")}\n\n'
        contexts["User Action Pipeline"] = action_logs_content # TODO: this should be added to the user prompt in an xml tag instead.
        context_token_counts["User Action Pipeline"] = model_config.count_tokens(text=action_logs_content)
    
    # Filtering step if context is too large
    total_context_length = sum([model_config.count_tokens(text=context) for context in contexts.values()])
    clip_context = False
    if total_context_length > model_config.max_context_tokens:
        writer({"coplay": "Filtering context"})
        # Filter contexts one by one until we're under the limit, starting with the largest ones
        for context_name, context in sorted(contexts.items(), key=lambda x: model_config.count_tokens(text=x[1]), reverse=True):
            filtered_context = await CONTEXT_FILTER_LLM.ainvoke(
                CONTEXT_FILTER_PROMPT.format(prompt=last_user_prompt, context=context, message_history=strip_old_context(state["messages"], keep_system_message=False)),
                response_format={"type": "json_object"},
                timeout=TIMEOUT_SECONDS
            )
            contexts[context_name] = filtered_context.content
            if sum([model_config.count_tokens(text=context) for context in contexts.values()]) < model_config.max_context_tokens:
                break
        else:
            # Clip context if still too large
            if sum([model_config.count_tokens(text=context) for context in contexts.values()]) > model_config.max_context_tokens:
                clip_context = True
                logger.warning("Context still too large after filtering")
                raise Exception("Context exceeds maximum token limit.") 
    
    context_content = '\n\n'.join([f'{description}:\n{context}\n\n' for description, context in contexts.items()])
    if clip_context:
        context_content = context_content[:model_config.max_context_tokens*AVERAGE_CHARS_PER_TOKEN]
    context_description = ', '.join([k.lower() for k in contexts.keys() if contexts[k] != ""]) or 'the context'

    # update the hash of the context
    context_hash_string = hashlib.sha256(context_content.encode()).hexdigest()
    last_hash = get_last_context_hash(state["messages"])
    context_hash_changed = context_hash_string != last_hash
    
    if len(current_prompt.tool_outputs) == 0 and current_prompt.prompt != "": # This is a human message rather than a tool output
        # TODO: I wonder if this could also be triggered by AI messages that don't have tool calls?
        # Clear the last tool call for this thread since the user has sent a new message
        await cache.delete(state["thread_id"], namespace=CacheNames.LAST_TOOLCALL)
        
        if current_prompt.context.get("environment_details"): # This will only be added for the first message. Check is on client side in promptBuilder.
            environment_details = f"<environment_details>\n{current_prompt.context['environment_details']}\n</environment_details>"
        else:
            environment_details = ""

        if current_prompt.context.get("mcp_servers"):
            mcp_servers = f"<mcp_servers>The following MCP servers are available:\n{current_prompt.context['mcp_servers']}\n</mcp_servers>"
        else:
            mcp_servers = ""

        if current_prompt.context.get("active_unity_hierarchy"):
            active_unity_hierarchy = f"<active_unity_hierarchy>\n{current_prompt.context['active_unity_hierarchy']}\n</active_unity_hierarchy>"
        else:
            active_unity_hierarchy = ""
        
        if current_prompt.context.get("editor_state"):
            editor_state = f"<editor_state>\n{current_prompt.context['editor_state']}\n</editor_state>"
        else:
            editor_state = ""
        
        # Append recipes
        recipe_str = "<recipes>\n" + await get_recipe(current_prompt.prompt, len(extracted_image_urls) > 0) + "\n</recipes>"
        # TODO: we should make it visible to the user what recipes are being attached.

        if not context_hash_changed:
            context_content = "" # Don't attach context if it hasn't changed.
        
        final_prompt_text = CHAT_USER_MESSAGE_PROMPT.format(
            context=context_content,
            prompt=current_prompt.prompt,
            environment_details=environment_details,
            mcp_servers=mcp_servers,
            active_unity_hierarchy=active_unity_hierarchy,
            editor_state=editor_state,
            recipe=recipe_str
        )

        human_message_kwargs = {
            "user_message": current_prompt.prompt,
            "prompt": current_prompt,
            "environment_details": environment_details,
            "active_unity_hierarchy": active_unity_hierarchy,
            "editor_state": editor_state,
            "recipe": recipe_str,
            "context_hash": context_hash_string
        }
        
        new_message = create_human_message_with_optional_images(
            text_content=final_prompt_text,
            image_urls=extracted_image_urls,
            tool_call_id=None,
            additional_kwargs=human_message_kwargs
        )
        state["messages"].append(new_message)
    elif context_hash_changed and context_content != "": # context_content can be empty if all context was removed, and in this case context_hash_changed is still true. Then we don't want to send a useless message to the thread.
        # I don't think this will be triggered if a tool makes a change to the context on the client because context is only sent with a user message.
        logger.info(f"Appending updated context message for thread {state['thread_id']}")
        updated_context_message = HumanMessage(
            content=UPDATED_CONTEXT_PROMPT.format(context=context_content, description=context_description).strip(),
            additional_kwargs=dict(type=MessageType.CONTEXT_UPDATE.value, prompt=current_prompt, context_hash=context_hash_string)
        )
        state["messages"].append(updated_context_message)

    return state

async def _manage_context_window(state: AgentState, writer: StreamWriter) -> AgentState:
    """
    Manage the context window by pruning messages to fit within the token budget.
    """
    model_config = MODEL_INSTANCES[state["current_model"]]
    unpruned_messages = [
        m.model_copy(deep=True)
        for m in strip_error_messages(state["messages"])
        if not m.additional_kwargs.get("was_pruned", False)
    ]
    # Strip on a separate copy solely for token counting
    messages_for_counting = [m.model_copy(deep=True) for m in unpruned_messages]
    current_input_tokens = model_config.count_tokens(
        messages=strip_unsupported_blocks(
            messages_for_counting,
            None
        )
    )
    # Preserve the unstripped messages for downstream processing; respond_to_user will strip as needed
    state["messages_to_send"] = unpruned_messages
    state["current_input_tokens"] = current_input_tokens
    if current_input_tokens > model_config.prune_threshold:
        logger.info(
            f"Input messages ({current_input_tokens} tokens) exceed prune threshold ({model_config.prune_threshold}), pruning context.")
        writer({"coplay": "Compressing large context"})
        prune_result = prune_messages_to_fit(state["messages"], model_config)
        state["messages_to_send"] = prune_result.messages_to_send
        state["messages"] = prune_result.updated_messages
        state["current_input_tokens"] = prune_result.token_count

    return state

async def _process_image_tool_output(state: AgentState, writer: StreamWriter) -> AgentState:
    """
    Process the image tool output.
    """
    current_prompt = state["prompt"]
    
    # Extract images from context if available
    if current_prompt.tool_outputs and current_prompt.tool_outputs[0].image_url:
        extracted_image_urls = []
        tool_call_id = current_prompt.tool_outputs[0].tool_call_id
        try:
            image_data_string = current_prompt.tool_outputs[0].image_url.strip() # Strip whitespace
            if image_data_string: # Check if the string is not empty after stripping
                supabase_client = await get_admin_supabase_client()
                extracted_image_urls = [await save_b64_to_supabase(image_data_string, supabase_client)] # Wrap the single image data string in a list
                logger.info(f"Extracted 1 image from tool output image_url")
                # Log the first 100 characters of the image data for debugging
                logger.info(f"Image data: {extracted_image_urls[0][:100]}...")
            else:
                logger.info("Tool output image_url was empty or contained only whitespace.")
                # extracted_image_urls remains empty
        except Exception as e:
            logger.error(f"Error processing image from tool output image_url: {e}. Content was: '{current_prompt.tool_outputs[0].image_url}'")
            # extracted_image_urls remains empty

        # This if statement should depend on the previous to prevent cases where the current_prompt.images is already populated before calling process_image_tool_output
        if len(extracted_image_urls) > 0: # Check if images were successfully processed and set on prompt
            placeholder_text = "Requested image" # Anthropic doesn't allow for empty content in a HumanMessage
            
            human_message_kwargs = {
                "user_message": placeholder_text,
                "prompt": current_prompt,
                "environment_details": current_prompt.context.get("environment_details", ""),
                "active_unity_hierarchy": current_prompt.context.get("active_unity_hierarchy", ""),
                "editor_state": current_prompt.context.get("editor_state", ""),
                "recipe": current_prompt.context.get("recipe", "")
            }

            new_message = create_human_message_with_optional_images(
                text_content=placeholder_text,
                image_urls=extracted_image_urls,
                tool_call_id=tool_call_id,
                additional_kwargs=human_message_kwargs
            )
            state["messages"].append(new_message)
        else:
            logger.info("No images found in tool output or image_url was empty/invalid.")
    
    return state
    
async def _respond_to_user(state: AgentState, writer: StreamWriter) -> AgentState:
    """
    Respond to the user with the AI's response.
    """
    model_config = MODEL_INSTANCES[state["current_model"]]
    # We send all messages that were not pruned
    # But this needs to be initialized in the first run
    # Strip unsupported blocks from the messages to send to the model
    messages_to_send = strip_unsupported_blocks(state["messages_to_send"], state["current_model"])

    if is_claude_model(state["current_model"]):
        messages_to_send = add_cache_control_params(messages_to_send)
                
    # Fetch the user's Anthropic API key if using a Claude model
    user_api_key = None
    # Only update token usage if the user is using the system API key (not their own)
    chargeable = True
    if is_claude_model(state["current_model"]):
        user_api_key = await get_user_anthropic_key(state["user"])
        if user_api_key:
            logger.info(f"Using user-provided Anthropic API key for user {state['user'].email}")
            # Create a new model instance with the user's API key
            model_config = ModelConfig.create_for_model(state["current_model"], user_api_key)
            chargeable = False

    writer({"coplay": "Generating response"})
    logger.debug(f"Sending LLM request")
    start_time = perf_counter()
    task = model_config \
        .with_tools(
            state["assistant_mode"],
            tool_context=state["prompt"].get_system_details(),
            server_side_tools=SERVER_SIDE_TOOLS,
            screenshots_enabled=state["prompt"].context.get("screenshots_enabled") == "true",
        ) \
        .ainvoke(messages_to_send)
    result = await asyncio.wait_for(task, timeout=TIMEOUT_SECONDS) # When streaming need this as the timeout is not respected by the model.
    end_time = perf_counter()
    latency = end_time - start_time
    logger.debug(f"LLM response completed in {latency:.2f}s")
    
    if result.text() == "" and len(result.tool_calls) == 0:
        # If the model returns an empty response and no tool calls, we need to add a tool call message to the response.
        result.content = "The model returned an empty response."
    
    result.additional_kwargs.update(dict(type="coplay_ai_message", _coplay_model=state["current_model"]))

    state["messages"].append(result)

    return state

# Note: Currently not in use, leaving here until we have better ideas for this graph
# async def _validate_tool_calls(state: AgentState, writer: StreamWriter) -> AgentState:
#     """
#     Validate the tool calls. Currently only validates replace_in_file.
#     """
#     writer({"coplay": "Validating AI actions"})
#     state["validation_failed"] = False
#     last_message: AIMessage = state["messages"][-1]
#     if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
#         return state

#     for tool_call in last_message.tool_calls:
#         if tool_call["name"] == "replace_in_file":
#             diff_block = tool_call['args'].get('diff')
            
#             if not diff_block:
#                 continue
                
#             is_different = True
#             try:
#                 # Normalize line endings and split into lines
#                 lines = diff_block.strip().replace('\r\n', '\n').replace("```", "").strip().split('\n')
                
#                 # Check for markers
#                 if lines[0] == '------- SEARCH' and lines[-1] == '+++++++ REPLACE':
#                     # Find separator
#                     separator_index = lines.index('=======')
                    
#                     # Extract blocks
#                     search_from_diff = '\n'.join(lines[1:separator_index])
#                     replace_from_diff = '\n'.join(lines[separator_index + 1:-1])
                    
#                     # Compare trimmed content
#                     if search_from_diff.strip() == replace_from_diff.strip():
#                         is_different = False
                
#             except (ValueError, IndexError):
#                 # If markers are not found or something is wrong with the structure,
#                 # assume it's a valid change to avoid blocking legitimate edits.
#                 pass
            
#             if not is_different:
#                 msg = f"The tool call {tool_call['name']} failed because the search and replace blocks are identical and need to be different."
#                 logger.warning(msg)
#                 # Add the error message directly to the state and set a flag to route back to model
#                 # Setting the toolmessage here does not work because it uses the same id as the last AI message for some reason.
#                 # For some reason it does it here, but not anywhere else in the code.
#                 # artifact = ToolMessage(content=msg, tool_call_id=tool_call['id'])
#                 # tmp_id = state["messages"][-1].id
#                 # new_id = tmp_id[:-1] + 'x'
#                 # state["messages"] += [ToolMessage(content=msg, artifact=None, tool_call_id=tool_call['id'], id=new_id)]
#                 # logger.info(f"latest msg id: {state['messages'][-1].id}")
#                 # state["validation_failed"] = True
#                 # state["validation_failed_message"] = msg
#                 # state["validation_failed_tool_call_id"] = tool_call['id']

#                 # I could not make the command thing work after hours of trying. some concurrentgraph update issue.
#                 # return Command(update={"messages": [ToolMessage(content=msg, artifact=None, tool_call_id=tool_call['id'])]}, goto="manage_context_window")
                
#                 # # Not poetic, but it works. Not ideal that we have to go back to the start of the graph. After this we check_tool_outputs again.
#                 # state["prompt"].tool_outputs = [ToolOutput(tool_call_id=tool_call['id'], tool_output=msg)]
#                 # state["validation_failed"] = True
#                 # Update this at the bottom also didn't work because for some reason langchain will force messages to have the same id if created in the same graph after invoking the ai, which then means when you restart the graph, the duplicates are removed.

#     return state

async def _check_for_duplicate_toolcall(state: AgentState):
    """
    Check if the last tool call is a duplicate.
    """
    last_message: AIMessage = state["messages"][-1]
    is_duplicate, previous_tool_outputs = await _has_duplicate_toolcall(state)
    
    # For Gemini-2.5-pro, add the previous result and invoke the model again
    if is_duplicate and "gemini-2-5-pro" in state["current_model"] and previous_tool_outputs:
        # Remove the previous message (the one with the duplicate tool call)
        messages = [RemoveMessage(id=last_message.id)]
        
        # We have to add a HumanMessage here instead of a ToolMessage because for this error state the gemini model does not seem to listen to even a more explicit ToolMessage.
        tmp_msg = f"This function has been executed. Here are the results:\n\n{json.dumps(previous_tool_outputs, indent=2)}"
        last_user_prompt = await get_last_prompt(state)

        if last_user_prompt is None:
            last_user_prompt = state["prompt"]

        messages.append(HumanMessage(
            content=tmp_msg,
            additional_kwargs={
                "type": MessageType.HUMAN.value,
                "hidden": True,
                "user_message": tmp_msg,
                "prompt": Prompt(
                    prompt=tmp_msg,
                    context=last_user_prompt.context,
                    thread_id=last_user_prompt.thread_id,
                    model=last_user_prompt.model,
                    assistant_mode=last_user_prompt.assistant_mode
                )
        }))
        
        return Command(update={"messages": messages}, goto="manage_context_window")
    else:
        return state

async def _has_duplicate_toolcall(state: AgentState) -> tuple[bool, list[dict]]:
    """
    Check if the current toolcall is a duplicate of the previous one.

    Gemini-2.5-pro has a bug where it will return the same tool call multiple times and ignore the function result.
    """
    last_message: AIMessage = state["messages"][-1]
    thread_id = state["thread_id"]

    if not hasattr(last_message, 'tool_calls') or len(last_message.tool_calls) == 0:
        return False, []
    
    # Create a string representation of the current toolcall for comparison
    current_toolcall_str = json.dumps([
        {
            "name": tool_call["name"],
            "args": tool_call["args"]
        } 
        for tool_call in last_message.tool_calls
    ], sort_keys=True)
    
    # Check if this is a duplicate of the previous toolcall
    last_toolcall = await cache.get(thread_id, namespace=CacheNames.LAST_TOOLCALL)
    if last_toolcall and last_toolcall == current_toolcall_str:
        error_message = f"Duplicate toolcall detected in thread {thread_id}"
        logger.error(error_message)
        
        user_email = state["user"].email
        if not any(excluded.lower() in user_email.lower() for excluded in DiscordNotifier.EXCLUDED_USERS):
            # Get LangSmith trace URL for this thread
            try:
                langsmith_trace_url = await get_langsmith_trace_url(thread_id)
                trace_info = f"\n[Langsmith run](<{langsmith_trace_url}>)" if langsmith_trace_url else ""
            except Exception as e:
                logger.warning(f"Failed to get LangSmith trace URL for duplicate toolcall error: {e}")
                trace_info = ""
            
            await DiscordNotifier(DiscordNotifier.WEBHOOK_URL_ERRORS).notify(
                f"Duplicate toolcall detected\nUser: {user_email}\nThread: {state['thread_id']}\nModel: {state['current_model']}\nToolcall: {current_toolcall_str}{trace_info}"
            )
        
        previous_tool_outputs = [{
            "tool_name": state["messages"][-1].tool_calls[0]["name"], # Since the last call is a duplicate, we can use the name from it.
            "result": state["messages"][-2].content # The result is the content of the message before the duplicate tool call.
        }]
        return True, previous_tool_outputs
    
    await cache.set(thread_id, current_toolcall_str, namespace=CacheNames.LAST_TOOLCALL)
    return False, []

async def _generate_tool_description(state: AgentState) -> AgentState:
    last_message: AIMessage = state["messages"][-1]
    last_message.additional_kwargs["tool_descriptions"] = {}

    prompt = TOOL_DESCRIPTION_HUMAN_MESSAGE.format(tool_calls=json.dumps(last_message.tool_calls))
    descriptions_result: ToolDescriptionOutput | None = None

    start_time = perf_counter()
    try:
        descriptions_result = await TOOL_DESCRIPTION_LLM.with_structured_output(ToolDescriptionOutput).ainvoke([SystemMessage(content=TOOL_DESCRIPTION_PROMPT), HumanMessage(content=prompt)])
    except Exception as e:
        logger.error(f"Failed to generate tool descriptions: {str(e)}")
        logger.error(traceback.format_exc())
    
    if descriptions_result is not None:
        descriptions = {d.tool_call_id: d.tool_description for d in descriptions_result.tool_descriptions}
    else:
        descriptions = {}

    end_time = perf_counter()
    latency = end_time - start_time
    logger.info(f"Model latency for {TOOL_DESCRIPTION_LLM.model} (generate_tool_description): {latency:.2f}s")
    
    for tool_call in last_message.tool_calls:
        # TODO: Remove None values from the arguments, it's not handled correctly by the client
        tool_call["args"] = {k: v for k, v in tool_call["args"].items() if v is not None}
        last_message.additional_kwargs["tool_descriptions"][tool_call["id"]] = descriptions.get(tool_call["id"], f"{tool_call['name']}")

    return state

def _route_after_validation(state: AgentState) -> str:
    """
    This is no longer used, but we tried it.
    Route after validation - if validation failed, go back to model response.
    """
    logger.info(f"validation failed: {state['validation_failed']}")
    if state["validation_failed"]:
        # Clear the flag and route back to model response
        state["validation_failed"] = False
        return "check_tool_outputs"
    else:
        logger.info("validation passed")
        return "check_for_duplicate_toolcall"

def _route_tool_call(state: AgentState) -> str:
    """
    Route the tool call depending on whether it's a server-side tool or not.
    """
    if isinstance(state["messages"][-1], AIMessage) and state["messages"][-1].tool_calls:
        tool_call = state["messages"][-1].tool_calls[0]
        if tool_call["name"] in [t.name for t in SERVER_SIDE_TOOLS]:
            return "server_side_tools"
        
        return "generate_tool_description"
    return END

def _create_graph() -> StateGraph:        
    workflow = StateGraph(state_schema=AgentState)

    # TODO: do as little as possible in the graph, and move as much as possible to normal python code.
    workflow.add_node("check_tool_outputs", _check_tool_outputs)
    workflow.add_node("manage_context_window", _manage_context_window)
    workflow.add_node("respond_to_user", _respond_to_user)
    workflow.add_node("check_for_duplicate_toolcall", _check_for_duplicate_toolcall)
    workflow.add_node("process_context", _process_context)
    workflow.add_node("process_image_tool_output", _process_image_tool_output)
    workflow.add_node("server_side_tools", ToolNode(SERVER_SIDE_TOOLS))
    workflow.add_node("generate_tool_description", _generate_tool_description)

    workflow.add_edge(START, "check_tool_outputs")
    workflow.add_edge("check_tool_outputs", "process_image_tool_output")
    workflow.add_edge("process_image_tool_output", "process_context")
    workflow.add_edge("process_context", "manage_context_window")
    workflow.add_edge("manage_context_window", "respond_to_user")
    workflow.add_edge("respond_to_user", "check_for_duplicate_toolcall")
    workflow.add_conditional_edges("check_for_duplicate_toolcall", _route_tool_call)
    workflow.add_edge("server_side_tools", "manage_context_window")
    workflow.add_edge("generate_tool_description", END)
    
    return workflow

MEMORY = MemorySaver()
WORKFLOW = _create_graph().compile(checkpointer=MEMORY)