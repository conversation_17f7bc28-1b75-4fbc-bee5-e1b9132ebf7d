using System;
using System.Threading.Tasks;
using Coplay.Common;
using Coplay.Common.CoplayApi;
using Coplay.Common.CoplayApi.Models;
using Coplay.Controllers.Systems;
using UnityEngine;

namespace Coplay.Services
{
    /// <summary>
    /// Service to handle automatic credit top-up functionality for subscribed users
    /// </summary>
    public class AutoTopUpService
    {
        private readonly ICoplayApiClient _apiClient;
        private readonly SettingsController _settings;
        private bool _isProcessingAutoTopUp = false;

        public AutoTopUpService(ICoplayApiClient apiClient, SettingsController settings)
        {
            _apiClient = apiClient;
            _settings = settings;
        }

        /// <summary>
        /// Checks if auto top-up should be triggered and handles it
        /// This should be called after each LLM request completes
        /// </summary>
        public async Task CheckAndHandleAutoTopUpAsync()
        {
            // TODO: if settings were in our database, we could move all this code to happen before/after LLM requests on the backend.
            try
            {
                // Don't process if already processing
                if (_isProcessingAutoTopUp)
                    return;

                // Only check for subscribed users with auto top-up enabled
                if (!_settings.HasActiveSubscription || !_settings.AutoTopUpEnabled)
                    return;

                var creditBalance = await _apiClient.GetCreditBalanceAsync();
                _settings.TotalCredit = creditBalance.Total;
                _settings.SubscriptionCredits = creditBalance.SubscriptionCredits;
                _settings.TopupCredits = creditBalance.TopupCredits;

                // Only top up if total balance is below $1
                if (creditBalance.Total >= 1.0f)
                    return;

                await TriggerAutoTopUpAsync();
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to check auto top-up", ex);
            }
        }

        /// <summary>
        /// Triggers the auto top-up process for subscribed users
        /// </summary>
        private async Task TriggerAutoTopUpAsync()
        {
            if (_isProcessingAutoTopUp)
                return;

            try
            {
                _isProcessingAutoTopUp = true;

                var topUpAmount = _settings.AutoTopUpAmount;
                
                CoplayLogger.Log($"Triggering auto top-up for ${topUpAmount:F2} (subscribed user)");

                // Try direct API call first
                var success = await TopUpHelper.TryDirectTopUpAsync(_apiClient, _settings, topUpAmount);
                if (success)
                {
                    CoplayLogger.Log("Auto top-up completed successfully via direct API");
                    return;
                }
                else
                {
                    CoplayLogger.LogWarning("Direct API top-up failed, falling back to dashboard");
                }

                // Fallback: Open dashboard for manual top-up
                var billingUrl = $"{Constants.AccountDashboardUrl}/dashboard?action=auto_topup&amount={topUpAmount:F2}";
                Application.OpenURL(billingUrl);
                
                CoplayLogger.Log("Auto top-up: Opened dashboard for manual completion");

                // Add a small delay to prevent rapid successive triggers
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to trigger auto top-up", ex);
            }
            finally
            {
                _isProcessingAutoTopUp = false;
            }
        }

    }
}
