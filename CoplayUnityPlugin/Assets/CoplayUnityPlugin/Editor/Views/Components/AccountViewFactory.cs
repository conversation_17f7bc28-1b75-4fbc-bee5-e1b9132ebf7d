using Coplay.Controllers.Systems;
using Coplay.Views.Components;
using UnityEngine.UIElements;
using Coplay.Common.CoplayApi;
using Coplay.Common.CoplayApi.Models;
using Coplay.Controllers.Subscription;
using System;
using UnityEngine;
using System.Threading.Tasks;
using Coplay.Common;
using UnityEditor;
using System.Reactive.Disposables;
using Coplay.Common.Rx;
using Coplay.Services;
using Coplay.Services.LogCollection;

namespace Coplay.Views.Windows
{
    /// <summary>
    /// Factory for creating and managing the metrics view in the Coplay chat window.
    /// </summary>
    public class AccountViewFactory : IDisposable
    {
        private readonly ICoplayApiClient _coplayApiClient;
        private readonly SettingsController _settings;
        private readonly LogCollectionService _logCollectionService;
        private readonly CompositeDisposable _disposables = new CompositeDisposable();
        
        // Store references to credit labels for updates
        private Label _subscriptionCreditsLabel;
        private Label _topupCreditsLabel;

        public AccountViewFactory(
            ICoplayApiClient coplayApiClient,
            SettingsController settings,
            LogCollectionService logCollectionService)
        {
            _coplayApiClient = coplayApiClient;
            _settings = settings;
            _logCollectionService = logCollectionService;
        }

        /// <summary>
        /// Sets up the metrics view in the chat window.
        /// </summary>
        /// <param name="ui">The root visual element of the chat window.</param>
        /// <param name="onAccountToggled">Callback to invoke when account drawer is toggled.</param>
        public void Setup(VisualElement ui, Action onAccountToggled = null)
        {
            var metricsView = ui.Q<VisualElement>("account_panel");

            var userContainer = new VisualElement();
            userContainer.style.flexDirection = FlexDirection.Row;
            userContainer.style.alignItems = Align.Center;
            userContainer.style.justifyContent = Justify.SpaceBetween;

            var userLabel = new TextField
            {
                value = _settings.CoplayUser,
                label = "Signed in as",
                isReadOnly = true
            };
            userLabel.style.flexShrink = 1;
            userContainer.Add(userLabel);

            var clearButton = new Button
            {
                text = "Sign Out"
            };
            clearButton.style.alignSelf = Align.FlexStart;
            clearButton.clicked += async () =>
            {
                _settings.ClearSettings();
            };
            userContainer.Add(clearButton);

            metricsView.Add(userContainer);

            metricsView.Add(Utils.CreateSeparator(marginTop: 10, marginBottom: 10));

            CreateCreditsView(metricsView);

            metricsView.Add(Utils.CreateSeparator(marginTop: 10, marginBottom: 10));

            var sendLogsContainer = new VisualElement();
            sendLogsContainer.style.flexDirection = FlexDirection.Row;
            sendLogsContainer.style.alignItems = Align.Center;
            sendLogsContainer.style.marginBottom = 10;
            
            var sendLogsLabel = new Label("Send logs to support");
            sendLogsLabel.style.flexShrink = 1;
            sendLogsLabel.style.marginRight = 10;
            sendLogsContainer.Add(sendLogsLabel);
            
            var sendLogsButton = new Button
            {
                text = "Send Logs",
                tooltip = "Send logs to support"
            };
            sendLogsButton.style.width = 100;
            sendLogsButton.style.height = 20;
            sendLogsButton.style.alignSelf = Align.Center;
            sendLogsButton.style.marginTop = 5;
            sendLogsButton.clicked += async () =>
            {
                sendLogsButton.text = "Sending...";
                sendLogsButton.SetEnabled(false);
                try
                {
                    await _logCollectionService.CollectAndUploadLogsAsync();
                }
                finally
                {
                    sendLogsButton.text = "Send Logs";
                    sendLogsButton.SetEnabled(true);
                }
            };
            sendLogsContainer.Add(sendLogsButton);
            
            metricsView.Add(sendLogsContainer);

            var discordContainer = new VisualElement();
            discordContainer.style.flexDirection = FlexDirection.Row;
            discordContainer.style.alignItems = Align.Center;
            
            var discordLabel = new Label("Get help on Discord");
            discordLabel.style.flexShrink = 1;
            discordContainer.Add(discordLabel);
            
            var discordButton = new Button
            {
                text = "", // No text, using background image
                tooltip = "Go To Discord"
            };
            discordButton.style.marginLeft = 10;
            discordButton.style.marginRight = 10;
            discordButton.AddToClassList("settings-discord-button");
            discordButton.clicked += () =>
            {
                Application.OpenURL("https://discord.gg/Xs364T7D9T");
            };
            
            discordContainer.Add(discordButton);

            var emailLabel = new Label("or via email at <a href=\"mailto:<EMAIL>\"><EMAIL></a>");
            emailLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            discordContainer.Add(emailLabel);

            metricsView.Add(discordContainer);

            // Add footer with Done button
            var footerContainer = new VisualElement();
            footerContainer.style.flexDirection = FlexDirection.Row;
            footerContainer.style.justifyContent = Justify.Center;
            footerContainer.style.marginTop = 20;

            var doneButton = new Button();
            doneButton.text = "Done";
            doneButton.style.width = 100;
            doneButton.style.height = 20;
            doneButton.style.alignSelf = Align.Center;
            doneButton.style.marginTop = 5;

            doneButton.clicked += () =>
            {
                metricsView.style.display = DisplayStyle.None;
            };

            footerContainer.Add(doneButton);
            metricsView.Add(footerContainer);

            // Setup metrics button click handler
            var metricsButton = ui.Q<Button>("account");
            metricsButton.clicked += () =>
            {
                onAccountToggled?.Invoke();
                // Update metrics when opening the panel, the display is either flex or none
                if (metricsView.style.display == DisplayStyle.Flex)
                {
                    _ = UpdateUsageMetrics();
                }
            };
        }

        private void CreateCreditsView(VisualElement metricsView)
        {
            // Create usage metrics UI
            var usageMetricsContainer = new VisualElement();
            usageMetricsContainer.style.flexShrink = 1;
            usageMetricsContainer.style.minWidth = 200;
            usageMetricsContainer.style.maxWidth = new StyleLength(StyleKeyword.Auto);

            // Helper function to create metric cards
            var card = new VisualElement();
            card.style.backgroundColor = EditorGUIUtility.isProSkin ? new Color(0.22f, 0.22f, 0.22f) : new Color(0.9f, 0.9f, 0.9f);
            card.style.flexShrink = 1;
            card.style.flexGrow = 1;

            // Container for all credit information
            var creditInfoContainer = new VisualElement();
            creditInfoContainer.style.marginTop = 8;

            // Subscription credits row in white with manage button below
            var subscriptionContainer = new VisualElement();
            subscriptionContainer.style.flexDirection = FlexDirection.Row;
            subscriptionContainer.style.alignItems = Align.Center;

            var subscriptionLabel = new Label("Subscription Credits:");
            subscriptionLabel.style.minWidth = 120;

            var subscriptionValueLabel = new Label();
            // subscriptionValueLabel.AddToClassList("font-large");
            subscriptionValueLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            subscriptionValueLabel.style.flexGrow = 1;
            subscriptionValueLabel.style.marginRight = 10;
            
            // Store reference for updates
            _subscriptionCreditsLabel = subscriptionValueLabel;

            // Add Manage Subscription button
            var manageSubscriptionButton = new Button
            {
                text = "Manage Billing",
                tooltip = "Open your Coplay subscription dashboard to manage your subscription"
            };
            manageSubscriptionButton.style.width = 130;
            manageSubscriptionButton.style.height = 20;
            // manageSubscriptionButton.style.backgroundColor = new Color(0.2f, 0.6f, 0.9f);
            manageSubscriptionButton.style.color = Color.white;
            manageSubscriptionButton.clicked += () =>
            {
                // Open the main dashboard where users can manage their subscription
                Application.OpenURL(Constants.AccountDashboardUrl + "/dashboard");
            };

            subscriptionContainer.Add(subscriptionLabel);
            subscriptionContainer.Add(subscriptionValueLabel);
            subscriptionContainer.Add(manageSubscriptionButton);

            creditInfoContainer.Add(subscriptionContainer);
            card.Add(creditInfoContainer);

            // Add cards to container
            usageMetricsContainer.Add(card);
            metricsView.Add(usageMetricsContainer);

            var subscriptionStatusLabel = new Label();
            // subscriptionStatusLabel.style.marginTop = 5;
            subscriptionStatusLabel.style.marginBottom = 5;
            UpdateSubscriptionStatus(subscriptionStatusLabel);
            metricsView.Add(subscriptionStatusLabel);

            _settings.SubscriptionChanged.ObserveOnEditorMainThread()
                .Subscribe(_ => UpdateSubscriptionStatus(subscriptionStatusLabel))
                .AddTo(_disposables);

            // Add Top-Up Settings section
            CreateTopUpSection(metricsView, _settings.HasActiveSubscription, subscriptionValueLabel);

        }

        // Function to update usage metrics display
        private async Task UpdateUsageMetrics()
            {
                // Update credit info
                try
                {
                    if (_subscriptionCreditsLabel != null) _subscriptionCreditsLabel.text = "Loading...";
                    if (_topupCreditsLabel != null) _topupCreditsLabel.text = "Loading...";
                    
                    // Get the detailed credit balance using the unified method
                    await UpdateCreditBalanceDisplay(_subscriptionCreditsLabel, _topupCreditsLabel);
                }
                catch (Exception ex)
                {
                    CoplayLogger.LogError("Failed to get credit usage", ex);
                    if (_subscriptionCreditsLabel != null) _subscriptionCreditsLabel.text = "Error";
                    if (_topupCreditsLabel != null) _topupCreditsLabel.text = "Error";
                }
            }

        /// <summary>
        /// Updates the subscription status label with green active text (restored original functionality)
        /// </summary>
        private void UpdateSubscriptionStatus(Label statusLabel)
        {
            bool hasSubscription = _settings.HasActiveSubscription;
            DateTime? expiryDate = _settings.SubscriptionExpiryDate;

            if (hasSubscription && expiryDate.HasValue)
            {
                statusLabel.text = "Active subscription until " +
                    expiryDate.Value.ToLocalTime().ToString("MMM dd, yyyy");
                statusLabel.style.color = new Color(0.2f, 0.6f, 0.3f); // Green

                // Add remaining days info
                TimeSpan remaining = expiryDate.Value - DateTime.UtcNow;
                if (remaining.TotalDays > 0)
                {
                    statusLabel.text += " (" + Math.Ceiling(remaining.TotalDays) + " days remaining)";
                }
            }
            else if (hasSubscription) // Has subscription but no expiry date
            {
                statusLabel.text = "Active subscription";
                statusLabel.style.color = new Color(0.2f, 0.6f, 0.3f); // Green
            }
            else // No active subscription
            {
                statusLabel.text = "No active subscription";
                statusLabel.style.color = new Color(0.7f, 0.7f, 0.7f); // Gray
            }
        }

        /// <summary>
        /// Creates the Top-Up Settings section
        /// </summary>
        private void CreateTopUpSection(VisualElement parentContainer, bool hasActiveSubscription, Label subscriptionCreditsLabel)
        {
            // Create Top-Up Settings section
            var topUpSection = new VisualElement();
            parentContainer.Add(topUpSection);

            // Add top-up credits display
            var topupCreditsContainer = new VisualElement();
            topupCreditsContainer.style.flexDirection = FlexDirection.Row;
            topupCreditsContainer.style.alignItems = Align.Center;
            topupCreditsContainer.style.marginTop = 10;
            // Gray out the section if no active subscription
            if (!hasActiveSubscription)
            {
                topupCreditsContainer.style.opacity = 0.5f;
            }

            var topupCreditsLabel = new Label("Top-up Credits:");
            topupCreditsLabel.style.minWidth = 120;
            // topupCreditsLabel.style.color = new Color(0.7f, 0.7f, 0.7f);

            var topupCreditsValueLabel = new Label();
            // topupCreditsValueLabel.AddToClassList("font-large");
            topupCreditsValueLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            topupCreditsValueLabel.style.flexGrow = 1;
            
            // Store reference for updates
            _topupCreditsLabel = topupCreditsValueLabel;

            topupCreditsContainer.Add(topupCreditsLabel);
            topupCreditsContainer.Add(topupCreditsValueLabel);
            var topupCreditsDescription = new Label("Top-up credits are used when you run out of subscription credits within the subscription period.");
            topupCreditsDescription.style.color = new Color(0.5f, 0.5f, 0.5f);
            topupCreditsDescription.style.fontSize = 8;
            topupCreditsDescription.style.marginBottom = 5;
            
            topUpSection.Add(topupCreditsContainer);
            topUpSection.Add(topupCreditsDescription);

            // Initial load of topup credits
            _ = UpdateCreditBalanceDisplay(subscriptionCreditsLabel, topupCreditsValueLabel);

            // Create horizontal container for input and buttons
            var topUpControlsContainer = new VisualElement();
            topUpControlsContainer.style.flexDirection = FlexDirection.Row;
            topUpControlsContainer.style.alignItems = Align.Center;
            topUpControlsContainer.style.marginBottom = 10;
            topUpSection.Add(topUpControlsContainer);

            // Error message label (initially hidden) - declare before it's used
            var errorLabel = new Label();
            errorLabel.style.color = new Color(0.8f, 0.2f, 0.2f); // Red
            errorLabel.style.display = DisplayStyle.None;
            errorLabel.style.marginBottom = 10;
            topUpSection.Add(errorLabel);

            // Dollar value input field
            var dollarInput = new TextField();
            dollarInput.style.width = 60;
            dollarInput.style.height = 20;
            dollarInput.style.unityTextAlign = TextAnchor.MiddleCenter;
            dollarInput.style.marginRight = 10;
            dollarInput.value = _settings.AutoTopUpAmount.ToString("F2");
            dollarInput.SetEnabled(hasActiveSubscription);
            topUpControlsContainer.Add(dollarInput);

            // Top Up Now button
            var topUpButton = new Button
            {
                text = "Top Up Now",
                tooltip = "Add credits to your account"
            };
            topUpButton.style.width = 100;
            topUpButton.style.height = 20;
            topUpButton.style.backgroundColor = new Color(0.2f, 0.6f, 0.3f);
            topUpButton.style.color = Color.white;
            topUpButton.style.marginRight = 10;
            topUpButton.clicked += async () =>
            {
                if (hasActiveSubscription && ValidateTopUpAmount(dollarInput.value, out float amount))
                {
                    await HandleTopUpRequest(amount, topUpButton, errorLabel, subscriptionCreditsLabel, topupCreditsValueLabel);
                }
            };
            topUpButton.SetEnabled(hasActiveSubscription);
            topUpControlsContainer.Add(topUpButton);

            // Auto top-up checkbox
            var autoTopUpContainer = new VisualElement();
            autoTopUpContainer.style.flexDirection = FlexDirection.Row;
            autoTopUpContainer.style.alignItems = Align.Center;
            topUpSection.Add(autoTopUpContainer);

            var autoTopUpToggle = new Toggle();
            autoTopUpToggle.value = _settings.AutoTopUpEnabled;
            autoTopUpToggle.style.marginRight = 5;
            autoTopUpToggle.SetEnabled(hasActiveSubscription);
            autoTopUpContainer.Add(autoTopUpToggle);

            var autoTopUpLabel = new Label("Automatically top-up for the value above when your credits fall below $1.");
            autoTopUpLabel.style.flexWrap = Wrap.Wrap;
            autoTopUpContainer.Add(autoTopUpLabel);

            // Connect auto top-up toggle to settings
            autoTopUpToggle.RegisterValueChangedCallback(evt =>
            {
                if (hasActiveSubscription)
                {
                    _settings.AutoTopUpEnabled = evt.newValue;
                    if (ValidateTopUpAmount(dollarInput.value, out float amount))
                    {
                        _settings.AutoTopUpAmount = amount;
                    }
                    CoplayLogger.Log($"Auto top-up {(evt.newValue ? "enabled" : "disabled")} for ${_settings.AutoTopUpAmount:F2}");
                }
            });

            // Add validation to the input field
            dollarInput.RegisterValueChangedCallback(evt =>
            {
                if (hasActiveSubscription)
                {
                    if (!ValidateTopUpAmount(evt.newValue, out float amount))
                    {
                        errorLabel.text = "Minimum top-up amount is $3.00";
                        errorLabel.style.display = DisplayStyle.Flex;
                        topUpButton.SetEnabled(false);
                    }
                    else
                    {
                        errorLabel.style.display = DisplayStyle.None;
                        topUpButton.SetEnabled(true);
                        // Update settings with new amount
                        _settings.AutoTopUpAmount = amount;
                    }
                }
            });
            
            // Add subscription required message if no active subscription
            if (!hasActiveSubscription)
            {
                var subscriptionRequiredLabel = new Label("A subscription is needed to top up.");
                subscriptionRequiredLabel.style.color = new Color(0.6f, 0.6f, 0.6f); // Gray
                subscriptionRequiredLabel.style.unityFontStyleAndWeight = FontStyle.Italic;
                subscriptionRequiredLabel.style.marginTop = 10;
                subscriptionRequiredLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
                topUpSection.Add(subscriptionRequiredLabel);
            }
        }

        /// <summary>
        /// Validates the top-up amount
        /// </summary>
        private bool ValidateTopUpAmount(string input, out float amount)
        {
            amount = 0f;
            
            // Remove dollar sign if present
            string cleanInput = input.Replace("$", "").Trim();
            
            if (float.TryParse(cleanInput, out amount))
            {
                return amount >= 3.0f;
                // return amount >= 0.51f; // For testinkg
            }
            
            return false;
        }

        /// <summary>
        /// Handles the top-up request using the backend API endpoint
        /// Since we don't have stored payment methods in Unity, we'll open the dashboard
        /// but also attempt to use the API if we have the necessary information
        /// </summary>
        private async Task HandleTopUpRequest(float amount, Button topUpButton, Label errorLabel, Label subscriptionCreditsLabel = null, Label topupCreditsLabel = null)
        {
            try
            {
                // Update button state
                var originalText = topUpButton.text;
                topUpButton.text = "Processing...";
                topUpButton.SetEnabled(false);

                CoplayLogger.Log($"Processing top-up request for ${amount:F2}");

                // Try direct API call first
                var success = await TopUpHelper.TryDirectTopUpAsync(_coplayApiClient, _settings, amount);
                if (success)
                {
                    // Update the credit balance display immediately
                    if (subscriptionCreditsLabel != null || topupCreditsLabel != null)
                    {
                        try
                        {
                            await UpdateCreditBalanceDisplay(subscriptionCreditsLabel, topupCreditsLabel);
                            CoplayLogger.Log("Credit balance display updated after successful topup");
                        }
                        catch (Exception ex)
                        {
                            CoplayLogger.LogError("Failed to update metrics display after topup", ex);
                        }
                    }

                    // Show success message
                    errorLabel.text = $"Successfully topped up ${amount:F2}!";
                    errorLabel.style.color = new Color(0.2f, 0.6f, 0.3f); // Green
                    errorLabel.style.display = DisplayStyle.Flex;

                    // Hide the message after a few seconds
                    await Task.Delay(3000);
                    errorLabel.style.display = DisplayStyle.None;
                    return;
                }
                else
                {
                    CoplayLogger.LogWarning("Direct API top-up failed, falling back to dashboard");
                }

                // Fallback: Open dashboard for manual top-up
                string billingUrl = $"{Constants.AccountDashboardUrl}/dashboard?action=topup&amount={amount:F2}";
                Application.OpenURL(billingUrl);

                // Show informative message
                errorLabel.text = $"Opened dashboard to complete ${amount:F2} top-up";
                errorLabel.style.color = new Color(0.2f, 0.6f, 0.3f); // Green
                errorLabel.style.display = DisplayStyle.Flex;

                // Hide the message after a few seconds
                await Task.Delay(3000);
                errorLabel.style.display = DisplayStyle.None;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to handle top-up request: {ex.Message}", ex);
                errorLabel.text = "Failed to open dashboard. Please try again.";
                errorLabel.style.color = new Color(0.8f, 0.2f, 0.2f); // Red
                errorLabel.style.display = DisplayStyle.Flex;
            }
            finally
            {
                // Restore button state
                topUpButton.text = "Top Up Now";
                topUpButton.SetEnabled(true);
            }
        }

        /// <summary>
        /// Updates the credit balance display for subscription and top-up labels
        /// </summary>
        private async Task UpdateCreditBalanceDisplay(Label subscriptionLabel = null, Label topupLabel = null)
        {
            try
            {
                // Set loading state for all provided labels
                if (subscriptionLabel != null) subscriptionLabel.text = "Loading...";
                if (topupLabel != null) topupLabel.text = "Loading...";

                // Get the detailed credit balance from the API
                var creditBalance = await _coplayApiClient.GetCreditBalanceAsync();
                _settings.TotalCredit = creditBalance.Total;
                _settings.SubscriptionCredits = creditBalance.SubscriptionCredits;
                _settings.TopupCredits = creditBalance.TopupCredits;

                // Update individual labels with the detailed breakdown
                if (subscriptionLabel != null) subscriptionLabel.text = string.Format("${0:F2}", creditBalance.SubscriptionCredits);
                if (topupLabel != null) topupLabel.text = string.Format("${0:F2}", creditBalance.TopupCredits);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to get credit balance", ex);
                if (subscriptionLabel != null) subscriptionLabel.text = "Error";
                if (topupLabel != null) topupLabel.text = "Error";
            }
        }

        /// <summary>
        /// Disposes of the resources used by the MetricsViewFactory.
        /// </summary>
        public void Dispose()
        {
            _disposables?.Dispose();
        }
    }
}
