from fal_client import <PERSON><PERSON><PERSON><PERSON>
from fal_client.client import InProgress, Queued

from ..constants import FAL_KEY
from ..models import AssetGenerationProvider, AssetGenerationRequest, AssetGenerationResult, AssetGenerationTaskStatus
from ..logger import logger
from ..jobs import ASSET_GENERATION_JOBS


async def process_fal_generation(task_id: str, request: AssetGenerationRequest):
    """Process asset generation using FAL (Hunyuan 3D) for image-to-3D"""
    
    # Validate required fields for FAL
    if not request.input_image_url:
        raise ValueError("input_image_url is required for FAL generation")
    
    # Initialize FAL client
    client = AsyncClient(key=FAL_KEY)
    
    # Prepare request data for FAL API
    fal_request = {
        "input_image_url": request.input_image_url,
        "num_inference_steps": request.num_inference_steps,
        "guidance_scale": request.guidance_scale,
        "octree_resolution": request.octree_resolution,
        "textured_mesh": request.textured_mesh
    }
    
    # Add seed if provided
    if request.seed is not None:
        fal_request["seed"] = request.seed
    
    logger.info(f"Submitting request to FAL API")
    
    # Update status to indicate API submission
    ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
        task_id=task_id,
        status="processing",
        progress=30,
        message="Submitting to FAL API...",
        finished=False
    )
    
    # Submit to FAL API using the async method
    result = await client.subscribe(
        "fal-ai/hunyuan3d-v21",
        arguments=fal_request,
        with_logs=True,
        on_queue_update=lambda update: _handle_queue_update(task_id, update)
    )
    
    logger.info(f"FAL generation completed for task {task_id}: {result}")
    
    # Extract result data from the FAL response
    result_data = AssetGenerationResult(
        model_glb_url=result.get("model_glb", {}).get("url") if result.get("model_glb") else None,
        provider=AssetGenerationProvider.HUNYUAN3D21
    )
    
    # Update with final result
    ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
        task_id=task_id,
        status="completed",
        progress=100,
        message="Asset generation completed successfully",
        result=result_data,
        finished=True
    )

def _handle_queue_update(task_id: str, update):
    """Handle queue updates from FAL API"""
    try:
        # Handle different types of FAL updates
        if isinstance(update, InProgress):            
            # Check if it's an InProgress update with logs
            if len(update.logs) > 0:
                # Update status
                message = update.logs[-1].get("message", "Generation in progress...")
            else:
                message = "Generation in progress..."
            
            logger.debug (f"Task {task_id} update: {message}")
            
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="processing",
                progress=50,
                message=message,
                finished=False
            )
        elif isinstance(update, Queued):
            logger.info(f"Task {task_id} queued")
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="processing",
                progress=20,
                message="Queued for processing...",
                finished=False
            )
                
    except Exception as e:
        logger.error(f"Error handling queue update for task {task_id}: {str(e)}")