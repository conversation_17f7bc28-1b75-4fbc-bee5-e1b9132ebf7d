using System.Collections.Generic;
using Newtonsoft.Json;

namespace Coplay.Services.Scene.Models
{
    /// <summary>
    /// Result of a hierarchy list query, containing scenes with their game objects and optional message
    /// </summary>
    public class HierarchyListResult
    {
        /// <summary>
        /// Gets or sets the scenes in the result
        /// </summary>
        [JsonProperty("scenes")]
        public List<SceneInfo> Scenes { get; set; } = new();

        /// <summary>
        /// Gets or sets an optional message about the result
        /// </summary>
        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;
    }
}
