using System;

namespace Coplay.Common.Exceptions
{
    /// <summary>
    /// Exception thrown when a thread cannot be found (HTTP 404) in the Coplay backend.
    /// </summary>
    public class ThreadNotFoundException : Exception
    {
        public string ThreadId { get; }

        public ThreadNotFoundException(string threadId)
            : base($"Thread with ID '{threadId}' was not found on the server.")
        {
            ThreadId = threadId;
        }

        public ThreadNotFoundException(string threadId, Exception innerException)
            : base($"Thread with ID '{threadId}' was not found on the server.", innerException)
        {
            ThreadId = threadId;
        }
    }
}
