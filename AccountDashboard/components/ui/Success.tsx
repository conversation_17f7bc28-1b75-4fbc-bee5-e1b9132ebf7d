"use client";

import { useRouter } from "next/navigation";

interface SuccessProps {
  title?: string;
  message?: string;
  onAction?: () => void;
  actionText?: string;
  showHomeButton?: boolean;
}

export default function Success({
  title = "Success",
  message = "Operation completed successfully.",
  onAction,
  actionText = "Continue",
  showHomeButton = true,
}: SuccessProps) {
  const router = useRouter();

  const handleHome = () => {
    router.push("/");
  };

  const handleAction = () => {
    if (onAction) {
      onAction();
    } else {
      router.push("/dashboard");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 w-full max-w-md">
        <div className="text-center mb-6">
          {/* Success Icon */}
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 mx-auto mb-4">
            <svg
              className="w-8 h-8 text-green-600 dark:text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>

          <h1 className="text-2xl font-semibold mb-2 text-gray-900 dark:text-white">
            {title}
          </h1>
          <p className="text-gray-500 dark:text-gray-400">{message}</p>

          <div className="mt-8 flex space-x-4 justify-center">
            <button
              onClick={handleAction}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"
            >
              {actionText}
            </button>
            {showHomeButton && (
              <button
                onClick={handleHome}
                className="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors duration-200"
              >
                Go to Home
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
