"use client";

import { useSearchParams, useRouter } from "next/navigation";
import BaseStatusCard from "../cards/BaseStatusCard";
import { useEffect } from "react";
import { trackGoogleAdsConversion } from "@/lib/ads";

export default function DeviceSuccessContent() {
  const searchParams = useSearchParams();
  const deviceId = searchParams.get("deviceId");
  const router = useRouter();

  const handleHome = () => {
    router.push("/");
  };

  // Track Google Ads conversion
  useEffect(() => {
    trackGoogleAdsConversion({
      sendTo: 'AW-17529375712/DEVICE_SUCCESS'
    })
  }, [])

  const additionalContent = (
    <>
      <p className="text-gray-500 dark:text-gray-400 mt-2">
        You can close this window now and return to Unity.
      </p>
      {deviceId && (
        <p className="text-xs text-gray-400 mt-4">Device ID: {deviceId}</p>
      )}
    </>
  );

  return (
    <BaseStatusCard
      status="success"
      useCustomStyling={true}
      content={{
        title: "Login successful",
        description: "Your device has been connected to your account.",
        additionalContent,
        actionButtons: [
          {
            label: "Go to Home",
            onClick: handleHome,
            className:
              "px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors duration-200",
          },
        ],
      }}
    />
  );
}
