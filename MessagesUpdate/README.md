# Messages Update Tool

This tool allows administrators to overwrite message history in threads when users end up in a bad state.

## Purpose

Sometimes users may encounter corrupted or problematic message states that require manual intervention. This utility provides a safe way to replace the entire message history for a specific thread with a known good state.

## Usage

1. **Configure the script**: Edit `update_messages.py` and set:
   - `SUPABASE_URL`: Your Supabase instance URL 
   - `SUPABASE_SERVICE_ROLE_KEY`: Admin service key with write permissions
   - `TABLE_NAME`: Target table (typically `"threads"`)
   - `THREAD_ID`: The specific thread ID to update
   - `JSON_FILE`: Path to the JSON file containing the replacement messages

2. **Prepare the message data**: Place your replacement messages in `messages.json` (or the file specified in `JSON_FILE`)

3. **Run the update**:
   ```bash
   python update_messages.py
   ```

## Important Notes

- This tool **overwrites** the entire `messages` column for the specified thread
- It only updates existing threads - it will not create new ones
- Requires admin/service role permissions
- Always backup existing data before running
- For local development, use `http://127.0.0.1:54321` as the Supabase URL

## Troubleshooting

- **404 errors**: Ensure the thread ID exists and table name doesn't include schema prefix
- **Permission errors**: Verify the service role key has proper write permissions
- **Connection errors**: Check that Supabase is running and accessible at the configured URL