name: Publish MCP Server to PyPI

on:
  workflow_dispatch:
    inputs:
      confirm_production:
        description: 'Type "CONFIRM" to publish to production PyPI'
        required: true
        type: string

jobs:
  validate-input:
    runs-on: ubuntu-latest
    steps:
    - name: Validate confirmation
      run: |
        if [ "${{ github.event.inputs.confirm_production }}" != "CONFIRM" ]; then
          echo "❌ Production deployment not confirmed. You must type 'CONFIRM' to proceed."
          exit 1
        fi
        echo "✅ Production deployment confirmed"

  publish-pypi:
    needs: validate-input
    uses: ./.github/workflows/publish-mcp-server-common.yml
    with:
      repository_url: 'https://upload.pypi.org/legacy/'
      environment_name: 'pypi'
      working_directory: 'CoplayMCPServer'
    secrets:
      PYPI_API_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
