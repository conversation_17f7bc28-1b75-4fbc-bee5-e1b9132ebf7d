This folder contains negative prompts for each recipe keyword.

Steps to create the negative prompts:

1. Do a bigquery with the keyword:
```
SELECT DISTINCT
  prompt
FROM
  `langsmith_logs.curated_runs`
WHERE
  LOWER(prompt) LIKE '%terrain%'
```

2. Copy the results to a file in this folder with `keyword.txt`
3. Clean each line and remove lines that are positive prompts. I.e. prompts that should trigger the recipe being added to the prompt.
Each prompt should be on a new line.
4. The prompts removed should be placed in the equivalent positive prompt file along with any other positive prompts for that keyword you want to test.