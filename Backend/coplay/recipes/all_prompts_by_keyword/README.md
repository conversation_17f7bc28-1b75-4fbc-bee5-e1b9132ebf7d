Pro tip: after creating the json file from the bigquery dump, use OpenAI agent mode to classify the bulk of the prompts, and then manually review after that.
Useful if there are more than 20 prompts to review. video: https://www.loom.com/share/c42038ee54dc44aabffd119038224b33?sid=bc12fdd3-ae20-4f71-b89e-c0bbd891b3ba

The files in here list all prompts and a negative field.
If the negative field is true, it will be used in our negative prompt list.
If true, it will be used in our positive prompt list.
If negative is empty/null, it will be ignored. The reason for this is because we don't want to force classifying all the prompts. About 20 is enough, but the more negative prompts we have, the better. Thus we use agent mode to do bulk negative prompt classification.