"""
Unit tests for model-specific transformations.
"""

import pytest

from coplay.assistants.model_transformations import normalize_zai_tool_call_args


class TestNormalizeZaiToolCallArgs:
    """Test the normalize_zai_tool_call_args function."""

    def test_normalize_create_gameobject_position_array(self):
        """Test normalization of create_gameobject position parameter."""
        args = {
            "position": [1, 2, 3],
            "name": "TestObject",
            "other_param": "unchanged"
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "position": "1,2,3",
            "name": "TestObject",
            "other_param": "unchanged"
        }
        assert result == expected

    def test_normalize_create_gameobject_size_array(self):
        """Test normalization of create_gameobject size parameter."""
        args = {
            "size": [10.5, 20.0, 30.25],
            "name": "TestObject"
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "size": "10.5,20.0,30.25",
            "name": "TestObject"
        }
        assert result == expected

    def test_normalize_create_gameobject_both_params(self):
        """Test normalization of both position and size parameters."""
        args = {
            "position": [1, 2, 3],
            "size": [4, 5, 6],
            "name": "TestObject"
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "position": "1,2,3",
            "size": "4,5,6",
            "name": "TestObject"
        }
        assert result == expected

    def test_normalize_set_transform_all_params(self):
        """Test normalization of set_transform position, rotation, and scale."""
        args = {
            "position": [1.0, 2.0, 3.0],
            "rotation": [0, 90, 0],
            "scale": [2, 2, 2],
            "target": "TestObject"
        }

        result = normalize_zai_tool_call_args(args, "set_transform")

        expected = {
            "position": "1.0,2.0,3.0",
            "rotation": "0,90,0",
            "scale": "2,2,2",
            "target": "TestObject"
        }
        assert result == expected

    def test_normalize_string_params_unchanged(self):
        """Test that string parameters are left unchanged."""
        args = {
            "position": "1,2,3",  # Already a string
            "name": "TestObject"
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "position": "1,2,3",
            "name": "TestObject"
        }
        assert result == expected

    def test_normalize_missing_params_unchanged(self):
        """Test that missing parameters don't cause issues."""
        args = {
            "name": "TestObject"
            # No position or size parameters
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "name": "TestObject"
        }
        assert result == expected

    def test_normalize_all_supported_tools(self):
        """Test that all supported tools can be normalized."""
        test_cases = [
            ("create_gameobject", ["position", "size"]),
            ("set_transform", ["position", "rotation", "scale"]),
            ("add_asset_to_scene", ["position"]),
            ("create_terrain", ["position"]),
            ("duplicate_gameobject", ["position"]),
            ("snap_to_bounds", ["offset"]),
            ("snap_to_vertex", ["referencePoint"]),
            ("snap_to_surface", [
             "raycastOriginDirection", "raycastOriginOffset"]),
            ("snap_to_grid", ["gridOrigin"]),
        ]

        for tool_name, params in test_cases:
            args = {param: [1, 2, 3] for param in params}
            args["other_param"] = "unchanged"

            result = normalize_zai_tool_call_args(args, tool_name)

            for param in params:
                assert result[param] == "1,2,3", f"Failed for tool {tool_name}, param {param}"
            assert result["other_param"] == "unchanged"

    def test_normalize_mixed_array_types(self):
        """Test normalization with mixed numeric types in arrays."""
        args = {
            "position": [1, 2.5, 3],  # Mixed int and float
            "name": "TestObject"
        }

        result = normalize_zai_tool_call_args(args, "create_gameobject")

        expected = {
            "position": "1,2.5,3",
            "name": "TestObject"
        }
        assert result == expected


if __name__ == "__main__":
    pytest.main([__file__])
