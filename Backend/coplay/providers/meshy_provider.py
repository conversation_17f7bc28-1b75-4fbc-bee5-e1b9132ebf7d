import asyncio
import httpx
import time

from ..models import AssetGenerationProvider, AssetGenerationRequest, AssetGenerationResult, AssetGenerationTaskStatus
from ..logger import logger
from ..constants import MESHY_KEY
from ..jobs import ASSET_GENERATION_JOBS

TIMEOUT_SECONDS = 300
MESHY_HEADERS = {
    "Authorization": f"Bearer {MESHY_KEY}",
    "Content-Type": "application/json"
}

async def process_meshy_generation(task_id: str, request: AssetGenerationRequest):
    """Process asset generation using Meshy for text-to-3D or image-to-3D"""
    # Determine if this is text-to-3D or image-to-3D
    is_image_to_3d = bool(request.input_image_url)
    is_text_to_3d = bool(request.prompt)
    is_texture_generation = bool(request.model_url)
    
    if is_image_to_3d:
        await _process_meshy_image_to_3d(task_id, request)
    elif is_texture_generation:
        result_data = await _process_meshy_retexturing(task_id, request.model_url, request, with_progress=True)
        ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Texture generation completed successfully",
            result=result_data,
            finished=True
        )
    elif is_text_to_3d:
        await _process_meshy_text_to_3d(task_id, request)
    else:
        raise ValueError("Either input_image_url (for image-to-3D) or prompt (for text-to-3D) is required for Meshy generation")

async def _process_meshy_image_to_3d(task_id: str, request: AssetGenerationRequest):
    """Process image-to-3D generation using Meshy API"""
    
    async with httpx.AsyncClient() as client:
        # Prepare image-to-3D request
        image_request = {
            "image_url": request.input_image_url,
            "ai_model": 'meshy-4' if request.provider == AssetGenerationProvider.MESHY4 else 'meshy-5',
            "topology": request.topology,
            "target_polycount": request.target_polycount,
            "symmetry_mode": request.symmetry_mode,
            "should_remesh": request.should_remesh,
            "should_texture": request.should_texture,
            "enable_pbr": request.enable_pbr,
            "moderation": request.moderation
        }
        
        # Add optional texture guidance
        if request.texture_prompt:
            image_request["texture_prompt"] = request.texture_prompt
        elif request.texture_image_url:
            image_request["texture_image_url"] = request.texture_image_url
        
        logger.info(f"Creating Meshy image-to-3D task {task_id}: {image_request}")
        
        # Submit image-to-3D generation
        response = await client.post(
            "https://api.meshy.ai/openapi/v1/image-to-3d",
            json=image_request,
            headers=MESHY_HEADERS
        )
        response.raise_for_status()
        
        result = response.json()
        meshy_task_id = result.get("result")
        
        if not meshy_task_id:
            raise ValueError("No task ID returned from Meshy image-to-3D generation")
        
        logger.info(f"Meshy image-to-3D task created: {meshy_task_id}")
        
        # Poll for completion
        start_time = time.time()
        
        while True:
            # Check for timeout
            if time.time() - start_time > TIMEOUT_SECONDS:
                raise TimeoutError(f"Meshy image-to-3D generation timed out after {TIMEOUT_SECONDS} seconds")
            
            await asyncio.sleep(2)  # Wait 2 seconds between polls
            
            status_response = await client.get(
                f"https://api.meshy.ai/openapi/v1/image-to-3d/{meshy_task_id}",
                headers=MESHY_HEADERS
            )
            status_response.raise_for_status()
            
            status_data = status_response.json()
            progress = status_data.get("progress", 0)
            status = status_data.get("status", "UNKNOWN")
            
            # Update progress
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="processing",
                progress=progress,
                message=f"Generating 3D model from image... Status: {status} ({progress}%)",
                finished=False
            )
            
            if status == "SUCCEEDED":
                logger.info(f"Meshy image-to-3D completed for task {task_id}")
                break
            elif status == "FAILED":
                error_message = status_data.get("task_error", {}).get("message", "Unknown error")
                raise ValueError(f"Meshy image-to-3D generation failed: {error_message}")

        # Extract result
        model_urls = status_data.get("model_urls", {})
        
        result_data = AssetGenerationResult(
            model_glb_url=model_urls.get("glb"),
            thumbnail_url=status_data.get("thumbnail_url"),
            provider=request.provider
        )
        
        # Update with final result
        ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Image-to-3D generation completed successfully",
            result=result_data,
            finished=True
        )

async def _process_meshy_text_to_3d(task_id: str, request: AssetGenerationRequest):
    """Process text-to-3D generation using Meshy API"""
    
    async with httpx.AsyncClient() as client:
        # Step 1: Create preview model
        preview_request = {
            "mode": "preview",
            "prompt": request.prompt,
            "art_style": request.art_style,
            "negative_prompt": request.negative_prompt
        }
        
        logger.info(f"Creating Meshy preview for task {task_id}: {preview_request}")
        
        # Submit preview generation
        response = await client.post(
            "https://api.meshy.ai/openapi/v2/text-to-3d",
            json=preview_request,
            headers=MESHY_HEADERS
        )
        response.raise_for_status()
        
        preview_result = response.json()
        preview_task_id = preview_result.get("result")
        
        if not preview_task_id:
            raise ValueError("No task ID returned from Meshy preview generation")
        
        logger.info(f"Meshy preview task created: {preview_task_id}")
        
        # Poll for preview completion
        start_time = time.time()
        
        while True:
            # Check for timeout
            if time.time() - start_time > TIMEOUT_SECONDS:
                raise TimeoutError(f"Meshy preview generation timed out after {TIMEOUT_SECONDS} seconds")
            
            await asyncio.sleep(2)  # Wait 2 seconds between polls
            
            status_response = await client.get(
                f"https://api.meshy.ai/openapi/v2/text-to-3d/{preview_task_id}",
                headers=MESHY_HEADERS
            )
            status_response.raise_for_status()
            
            status_data = status_response.json()
            progress = status_data.get("progress", 0)
            status = status_data.get("status", "UNKNOWN")
            
            # Update progress
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="processing",
                progress=progress // 2,
                message=f"Generating preview model... Status: {status} ({progress}%)",
                finished=False
            )
            
            if status == "SUCCEEDED":
                logger.info(f"Meshy preview completed for task {task_id}")
                break
            elif status == "FAILED":
                raise ValueError(f"Meshy preview generation failed: {status_data}")
        
        # If refinement is disabled, return preview result
        if not request.enable_refinement:
            model_urls = status_data.get("model_urls", {})
            
            result_data = AssetGenerationResult(
                model_glb_url=model_urls.get("glb"),
                thumbnail_url=status_data.get("thumbnail_url"),
                provider=request.provider,
                preview_task_id=preview_task_id
            )
            
            # Update with final result (no refinement)
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="completed",
                progress=100,
                message="Text-to-3D generation completed successfully (preview only)",
                result=result_data,
                finished=True
            )
            return
        else:
            model_url = status_data.get("model_urls", {}).get("glb")
            # Await both refinement and retexturing tasks concurrently
            refinement_task = asyncio.create_task(_process_meshy_refinement(task_id, preview_task_id, request))
            retexturing_task = asyncio.create_task(_process_meshy_retexturing(task_id, model_url, request))

            # Wait for both tasks to complete or fail, then extract the first valid result
            done, pending = await asyncio.wait(
                [refinement_task, retexturing_task],
                return_when=asyncio.ALL_COMPLETED
            )
            results = []
            for task in [refinement_task, retexturing_task]:
                try:
                    result = task.result()
                    if result is not None:
                        results.append(result)
                except Exception:
                    pass
            if not results:
                # If both failed, raise the first available exception
                retexture_exception = retexturing_task.exception()
                refinement_exception = refinement_task.exception()
                
                if retexture_exception:
                    raise retexture_exception
                elif refinement_exception:
                    raise refinement_exception
                else:
                    # Both tasks completed successfully but returned no valid results
                    raise ValueError("Both refinement and retexturing tasks completed successfully but returned no valid results")
            
            result_data = results[0]
        
        # Update with final result
        ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Text-to-3D generation completed successfully",
            result=result_data,
            finished=True
        )

async def _process_meshy_refinement(task_id: str, preview_task_id: str, request: AssetGenerationRequest) -> AssetGenerationResult:
    """Process refinement of a Meshy model"""
    
    async with httpx.AsyncClient() as client:
        # Step 2: Refine the model if enabled
        refine_request = {
            "mode": "refine",
            "preview_task_id": preview_task_id
        }
        
        logger.info(f"Starting Meshy refinement for task {task_id}")
        
        # Submit refinement
        refine_response = await client.post(
            f"https://api.meshy.ai/openapi/v2/text-to-3d",
            json=refine_request,
            headers=MESHY_HEADERS
        )
        refine_response.raise_for_status()
        
        refine_result = refine_response.json()
        refine_task_id = refine_result.get("result")
        
        if not refine_task_id:
            raise ValueError("No task ID returned from Meshy refinement")

        # Poll for refinement completion
        start_time = time.time()
        
        while True:
            # Check for timeout
            if time.time() - start_time > TIMEOUT_SECONDS:
                raise TimeoutError(f"Meshy refinement timed out after {TIMEOUT_SECONDS} seconds")
            await asyncio.sleep(2)  # Wait 2 seconds between polls
            
            refine_status_response = await client.get(
                f"https://api.meshy.ai/openapi/v2/text-to-3d/{refine_task_id}",
                headers=MESHY_HEADERS
            )
            refine_status_response.raise_for_status()
            
            refine_status_data = refine_status_response.json()
            progress = refine_status_data.get("progress", 0)
            status = refine_status_data.get("status", "UNKNOWN")
            
            # Update progress
            ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                task_id=task_id,
                status="processing",
                progress=50 + (progress // 2),  # Second half of progress
                message=f"Refining model... Status: {status} ({progress}%)",
                finished=False
            )
            
            if status == "SUCCEEDED":
                logger.info(f"Meshy refinement completed for task {task_id}")
                break
            elif status == "FAILED":
                raise ValueError(f"Meshy refinement failed: {refine_status_data}")
        
        # Extract refined result
        model_urls = refine_status_data.get("model_urls", {})
        
        return AssetGenerationResult(
            model_glb_url=model_urls.get("glb"),
            thumbnail_url=refine_status_data.get("thumbnail_url"),
            provider=request.provider,
            preview_task_id=preview_task_id
        )
        
async def _process_meshy_retexturing(task_id: str, model_glb_url: str, request: AssetGenerationRequest, with_progress: bool = False) -> AssetGenerationResult:
    """Process retexturing of a Meshy model"""
    
    async with httpx.AsyncClient() as client:
        # Step 2: Refine the model if enabled
        retexture_request = {
            "model_url": model_glb_url,
            "ai_model": 'meshy-4' if request.provider == AssetGenerationProvider.MESHY4 else 'meshy-5',
            "art_style": request.art_style
        }

        if request.prompt:
            retexture_request["text_style_prompt"] = request.prompt
        elif request.texture_prompt:
            retexture_request["text_style_prompt"] = request.texture_prompt
        elif request.texture_image_url:
            retexture_request["image_style_url"] = request.texture_image_url
        
        logger.info(f"Starting Meshy retexturing for task {task_id}")
        
        # Submit refinement
        retexture_response = await client.post(
            f"https://api.meshy.ai/openapi/v1/retexture",
            json=retexture_request,
            headers=MESHY_HEADERS
        )
        retexture_response.raise_for_status()
        
        retexture_result = retexture_response.json()
        retexture_task_id = retexture_result.get("result")
        
        if not retexture_task_id:
            raise ValueError("No task ID returned from Meshy retexture")

        # Poll for retexture completion
        start_time = time.time()
        
        while True:
            # Check for timeout
            if time.time() - start_time > TIMEOUT_SECONDS:
                raise TimeoutError(f"Meshy retexture timed out after {TIMEOUT_SECONDS} seconds")
            await asyncio.sleep(2)  # Wait 2 seconds between polls
            
            retexture_status_response = await client.get(
                f"https://api.meshy.ai/openapi/v1/retexture/{retexture_task_id}",
                headers=MESHY_HEADERS
            )
            retexture_status_response.raise_for_status()
            
            retexture_status_data = retexture_status_response.json()
            progress = retexture_status_data.get("progress", 0)
            status = retexture_status_data.get("status", "UNKNOWN")
            
            # Update progress
            if with_progress:
                ASSET_GENERATION_JOBS[task_id].status = AssetGenerationTaskStatus(
                    task_id=task_id,
                    status="processing",
                    progress=progress,
                    message=f"Retexturing model... Status: {status} ({progress}%)",
                    finished=False
                )
            
            if status == "SUCCEEDED":
                logger.info(f"Meshy retexture completed for task {task_id}")
                break
            elif status == "FAILED":
                raise ValueError(f"Meshy retexture failed: {retexture_status_data}")
        
        # Extract refined result
        model_urls = retexture_status_data.get("model_urls", {})
        texture_urls = retexture_status_data.get("texture_urls", [{}])[0]
        
        return AssetGenerationResult(
            model_glb_url=model_urls.get("glb"),
            texture_url=texture_urls.get("base_color"),
            thumbnail_url=retexture_status_data.get("thumbnail_url"),
            provider=request.provider,
        )