using System;
using System.Collections.Generic;

namespace Coplay.Models.Orchestrator
{
    public class OrchestratorModel
    {
        public string SessionId { get; set; }
        public DateTime StartTime { get; set; }
        public bool IsProcessing { get; set; }
        public bool HasChildThreadStarted { get; set; }
        public string CurrentThreadId { get; set; }

        public OrchestratorModel()
        {
            SessionId = Guid.NewGuid().ToString();
            StartTime = DateTime.Now;
            IsProcessing = false;
            HasChildThreadStarted = false;
            CurrentThreadId = null;
        }
    }
}
