import { createClient } from "@/lib/supabase/client";
import { isValidEmail } from "@/utils/validation";
import { type ForgotPasswordFormData } from "@/schemas/auth";
import { Profile } from "@/types";
import { Session } from "@supabase/supabase-js";
import toast from "react-hot-toast";

// Types and interfaces
export interface AuthProvider {
  provider: string;
  isOAuth: boolean;
  displayName: string;
}

export type OAuthProvider = "google" | "github" | "discord";

// Type guard to validate AuthProvider structure
function isValidAuthProvider(data: any): data is AuthProvider {
  return (
    typeof data === "object" &&
    data !== null &&
    typeof data.provider === "string" &&
    typeof data.isOAuth === "boolean" &&
    typeof data.displayName === "string"
  );
}

// ==========================================
// PASSWORD AUTHENTICATION FUNCTIONS
// ==========================================

/**
 * Send password reset link to the provided email
 */
export async function sendPasswordResetLink(email: string): Promise<void> {
  const response: Response = await fetch("/api/auth/forgot-password-request", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: email.trim(),
    }),
  });

  const responseData = await response.json();

  if (!response.ok) {
    throw new Error(
      responseData.error || "Error sending password reset email"
    );
  }
}

/**
 * Update user password with current and new password
 */
export async function updatePassword(
  currentPassword: string,
  newPassword: string
): Promise<void> {
  const response = await fetch("/api/auth/update-password", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ currentPassword, newPassword }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Failed to update password");
  }
}

/**
 * Reset password using recovery token (used in reset password flow)
 */
export async function resetPassword(password: string): Promise<void> {
  const response = await fetch("/api/auth/update-password", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      password,
    }),
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || "Failed to reset password");
  }
}

/**
 * Validate password requirements
 */
export function validatePassword(password: string): { 
  isValid: boolean; 
  error?: string; 
} {
  if (!password || password.length < 8) {
    return {
      isValid: false,
      error: "Password must be at least 8 characters long",
    };
  }

  return { isValid: true };
}

/**
 * Validate password confirmation matches
 */
export function validatePasswordConfirmation(
  password: string,
  confirmPassword: string
): { isValid: boolean; error?: string } {
  if (password !== confirmPassword) {
    return {
      isValid: false,
      error: "Passwords don't match",
    };
  }

  return { isValid: true };
}

// ==========================================
// OAUTH AUTHENTICATION FUNCTIONS
// ==========================================

/**
 * Get the authentication provider for the current user
 */
export async function getUserAuthProvider(): Promise<AuthProvider> {
  try {
    const response = await fetch("/api/auth/provider");

    if (!response.ok) {
      console.error("Error fetching user auth provider:", response.statusText);
      // Default to email provider if we can't determine
      return {
        provider: "email",
        isOAuth: false,
        displayName: "Email",
      };
    }

    const data = await response.json();

    // Validate the response data structure
    if (!isValidAuthProvider(data)) {
      console.error("Invalid response structure from auth provider API:", data);
      return {
        provider: "email",
        isOAuth: false,
        displayName: "Email",
      };
    }

    return data;
  } catch (error) {
    console.error("Error in getUserAuthProvider:", error);
    return {
      provider: "email",
      isOAuth: false,
      displayName: "Email",
    };
  }
}

/**
 * Handle OAuth login with the specified provider
 */
export async function handleOAuthLogin(
  provider: OAuthProvider,
  deviceId?: string | null,
  setLoading?: (loading: boolean) => void
) {
  const supabase = createClient();

  const getRedirectUrl = () => {
    return `${window.location.origin}/auth/callback${
      deviceId ? `?deviceId=${deviceId}` : ""
    }`;
  };

  try {
    setLoading?.(true);
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: getRedirectUrl(),
      },
    });
    if (error) throw error;
  } catch (error) {
    console.error(error);
    const providerName = provider === "github" ? "GitHub": provider.charAt(0).toUpperCase() + provider.slice(1);
    toast.error(`${providerName} login failed. Please try again.`);
    setLoading?.(false);
  }
}

// ==========================================
// MAGIC LINK AUTHENTICATION FUNCTIONS
// ==========================================

/**
 * Send magic link to the provided email
 */
export async function sendMagicLinkEmail(
  email: string,
  redirectUrl: string
): Promise<void> {
  // Client-side validation
  if (!email) {
    throw new Error("Email is required");
  }

  if (!isValidEmail(email.trim())) {
    throw new Error("Please enter a valid email address");
  }

  const response = await fetch("/api/auth/magic-link", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: email.trim(),
      redirectUrl,
    }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.error || "Error sending magic link");
  }
}

/**
 * Generate redirect URL for magic link authentication
 */
export function generateMagicLinkRedirectUrl(deviceId?: string | null): string {
  return `${window.location.origin}/auth/callback${
    deviceId ? `?deviceId=${deviceId}` : ""
  }`;
}

// ==========================================
// TOKEN AUTHENTICATION FUNCTIONS
// ==========================================

/**
 * Parse URL hash parameters for authentication tokens
 */
export function parseUrlHashParams(): {
  accessToken: string | null;
  refreshToken: string | null;
  type: string | null;
} {
  const hash = window.location.hash;
  
  if (!hash) {
    return { accessToken: null, refreshToken: null, type: null };
  }

  const params = new URLSearchParams(hash.substring(1));
  
  return {
    accessToken: params.get("access_token"),
    refreshToken: params.get("refresh_token"),
    type: params.get("type"),
  };
}

/**
 * Verify and set recovery token session
 */
export async function verifyRecoveryToken(): Promise<void> {
  const { accessToken, refreshToken, type } = parseUrlHashParams();

  if (!accessToken || type !== "recovery") {
    throw new Error("Invalid recovery token");
  }

  const supabase = createClient();
  
  const { error } = await supabase.auth.setSession({
    access_token: accessToken,
    refresh_token: refreshToken || "",
  });

  if (error) {
    throw new Error("Failed to set session. Please try again.");
  }
}

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

/**
 * Generate redirect URL for authentication callbacks
 * (shared utility for OAuth and Magic Link)
 */
export function generateAuthRedirectUrl(deviceId?: string | null): string {
  return `${window.location.origin}/auth/callback${
    deviceId ? `?deviceId=${deviceId}` : ""
  }`;
}

// ==========================================
// SESSION MANAGEMENT FUNCTIONS
// ==========================================

export interface SidebarAuthData {
  session: Session | null;
  userData: Profile | null;
}

/**
 * Get current session from Supabase
 */
export async function getCurrentSession(): Promise<Session | null> {
  const supabase = createClient();
  
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error("Session error:", error);
      return null;
    }
    
    return session;
  } catch (error) {
    console.error("Failed to get session:", error);
    return null;
  }
}

/**
 * Get current user from Supabase
 */
export async function getCurrentUser() {
  const supabase = createClient();
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error("User error:", error);
      return null;
    }
    
    return user;
  } catch (error) {
    console.error("Failed to get user:", error);
    return null;
  }
}

/**
 * Get user profile data from the API
 */
export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const response = await fetch(`/api/profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error("Profile fetch error:", response.status, response.statusText);
      return null;
    }

    const profileData = await response.json();
    return profileData;
  } catch (error) {
    console.error("Failed to fetch profile:", error);
    return null;
  }
}

/**
 * Get current user session and profile data
 * Refactored to use modular, reusable functions
 */
export async function getAuthData(): Promise<SidebarAuthData> {
  try {
    // Get current user first to validate authentication
    const user = await getCurrentUser();
    
    if (!user) {
      return { session: null, userData: null };
    }

    // Get session and profile data in parallel for better performance
    const [session, userData] = await Promise.all([
      getCurrentSession(),
      getUserProfile(user.id)
    ]);

    return {
      session,
      userData,
    };
  } catch (error) {
    console.error("Error in getAuthData:", error);
    return { session: null, userData: null };
  }
}

/**
 * Set up auth state change listener
 */
export function onAuthStateChange(callback: (session: Session | null) => void) {
  const supabase = createClient();
  return supabase.auth.onAuthStateChange((event, session) => {
    callback(session);
  });
}

// ==========================================
// NAVIGATION UTILITIES
// ==========================================

/**
 * Check if current path is an authentication page
 */
export function isAuthPage(pathname: string | null): boolean {
  if (!pathname) return false;
  
  return (
    pathname.startsWith("/login") ||
    pathname.startsWith("/signup") ||
    pathname.startsWith("/auth") ||
    pathname.startsWith("/forgot-password") ||
    pathname.startsWith("/reset-password")
  );
}

import type { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

/**
 * Navigate to home page
 */
export function navigateToHome(router: AppRouterInstance) {
  router.push("/");
}
