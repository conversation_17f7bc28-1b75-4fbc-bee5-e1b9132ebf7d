# Coplay Backend Load Testing

This directory contains comprehensive load testing tools for the Coplay backend API using k6, specifically designed to test the AI-powered Unity assistant endpoints.

## 🎯 **What This Does**

The load testing suite validates the performance and reliability of Coplay's core AI features:
- **Streaming AI responses** (`/assistant/stream`) - Real-time Unity script generation
- **Non-streaming AI responses** (`/assistant`) - Batch Unity script generation
- **Image generation** (`/image/generate`) - AI-powered image creation
- **Thread management** - Multi-conversation handling
- **Authentication & device management** - User session validation

## 🚀 **Quick Start**

### Prerequisites
```bash
# Install k6
brew install k6

# Start Coplay backend
cd ../Backend
poetry run uvicorn main:app --port 8080 --reload
```

### Basic Usage
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
# K6_AUTH_TOKEN=your-supabase-jwt-token
# K6_DEVICE_ID=cpl-device-your-device-id

# Run streaming load test
./run_k6.py stream_test.js

# Run non-streaming load test  
./run_k6.py non_stream_test.js

# Run image generation load test
./run_k6.py image_test.js
```

## 📁 **Directory Structure**

```
LoadTests/
├── scripts/              # k6 test scripts
│   ├── stream_test.js     # Streaming AI responses
│   ├── non_stream_test.js # Non-streaming AI responses
│   └── image_test.js      # Image generation
├── run_k6.py             # Test runner with environment support
├── .env.example          # Environment configuration template
└── .gitignore            # Git ignore rules
```

## ⚙️ **Configuration**

### Environment Variables (`.env`)
```bash
# Backend Configuration
BASE_URL=http://localhost:8080         # Backend server URL
K6_AUTH_TOKEN=your-jwt-token          # Supabase authentication token
K6_DEVICE_ID=cpl-device-your-id       # Device ID for requests

# AI Model Configuration  
K6_MODEL=gemini-2-5-flash             # AI model to use for tests

# Load Test Parameters
VUS=5                                 # Number of virtual users
DURATION=10s                          # Test duration
```

### Test Scripts Configuration
Each script has built-in performance thresholds:

**Note**: VUS is the number of virtual users, and duration is the length of the test. The rate of requests is dynamic i.e. when one a VU's request is completed, it will start another request.

**Stream Test (`stream_test.js`)**
- p(95) response time < 1200ms
- Failure rate < 1%
- 5 VUs for 10 seconds (default)

**Non-Stream Test (`non_stream_test.js`)**  
- p(95) response time < 800ms
- Failure rate < 1%
- 30 VUs for 2 minutes (default)

**Image Test (`image_test.js`)**
- p(95) response time < 5000ms
- Failure rate < 2%
- 10 VUs for 30 seconds (default)

## 🧪 **Running Tests**

### Device Management
The runner automatically handles device pairing:
```bash
# Device is automatically paired with your user account
./run_k6.py stream_test.js

# Device pairing ensures authenticated requests work properly
```

### Custom Parameters
```bash
# Override default settings
./run_k6.py stream_test.js VUS=10 DURATION=30s

# Use different model
K6_MODEL=claude-3-5-sonnet ./run_k6.py stream_test.js
```

## 📊 **Understanding Results**

### ✅ **Key Success Indicators**
- **0% failure rate** - All requests successful
- **Low p(95) latency** - 95% of requests complete quickly
- **All thresholds passed** - Performance meets targets
- **Clean backend logs** - No errors or warnings

### 📈 **Critical Metrics to Watch**

#### **Response Times**
```
http_req_duration: avg=32.74ms p(90)=71.66ms p(95)=78.66ms
```
- **avg**: Average response time across all requests
- **p(90)**: 90% of requests completed faster than this
- **p(95)**: 95% of requests completed faster than this

**Good Values:**
- **Streaming API**: avg <100ms, p(95) <1200ms  
- **Non-streaming API**: avg <50ms, p(95) <800ms
- **Image API**: avg <2000ms, p(95) <5000ms

#### **Error Rates**
```
http_req_failed: 0.00% (0 out of 20)
```
- **Target**: <1% for streaming, <1% for non-streaming, <2% for images
- **0.00%** = Perfect (no failed requests)

#### **Throughput**
```
http_reqs: 20 (1.289354/s)
iterations: 10 (0.644677/s)
```
- **http_reqs/s**: Total HTTP requests per second
- **iterations/s**: Complete test cycles per second
- Each iteration = 2 HTTP calls (thread creation + message)

#### **AI-Specific Metrics**
```
Model time-to-first-token: 1.34s
```
- Time until AI starts responding (streaming only)
- **Good**: <2s, **Excellent**: <1.5s

### 🚨 **Warning Signs**
- **>1% failure rate** - Backend errors or timeouts
- **p(95) > threshold** - Performance degradation
- **Long iteration times** - AI processing delays
- **Backend errors in logs** - Authentication or processing issues

### 📋 **Sample Results Analysis**
```
✅ EXCELLENT Performance:
- Failure rate: 0.00% (target: <1%)
- p(95) latency: 78.66ms (target: <1200ms)  
- Time-to-first-token: 1.34s (target: <2s)
- All checks passed: ✓ thread creation ✓ status ok ✓ has response body

🎯 Backend Health:
- Token usage: ~1012 payable tokens per request
- Cache efficiency: 18,033 cache hits
- No processing errors
- LangSmith tracking active
```

## 🛠️ **Troubleshooting**

### Common Issues

**Authentication Errors (401)**
```bash
# Check your JWT token is valid and not expired
# Update K6_AUTH_TOKEN in .env file
```

**Device Not Found (404)**
```bash
# Ensure device exists and is paired with your account
# Check K6_DEVICE_ID in .env file
```

**High Response Times**
- Check backend server resources
- Verify database connections
- Monitor AI model performance

**Thread Creation Failures**
- Verify authentication token
- Check device ID validity
- Review backend logs for errors

### Debugging
```bash
# Enable verbose k6 output
k6 run --verbose scripts/stream_test.js

# Check backend logs for detailed error information
# Review device_update.json for pairing errors
```

## 🎯 **Best Practices**

1. **Start Small**: Begin with low VU counts and short durations
2. **Monitor Resources**: Watch backend CPU, memory, and database load
3. **Gradual Scaling**: Increase load incrementally to find limits
4. **Realistic Scenarios**: Use representative prompts and usage patterns
5. **Regular Testing**: Run load tests as part of your deployment pipeline

---

**Need Help?** Check the backend logs, review your `.env` configuration, and ensure your Supabase authentication token is valid and has proper permissions.
