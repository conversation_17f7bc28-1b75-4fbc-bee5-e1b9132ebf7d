import asyncio
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import os
import sys
import time

script_dir = os.path.dirname(os.path.abspath(__file__))
scene_lang_dir = os.path.join(script_dir, "..", "third_party", "scene-language", "scripts")
scene_lang_dir = os.path.abspath(scene_lang_dir)
sys.path.insert(0, scene_lang_dir)  # Insert at beginning of path
sys.path.insert(0, os.path.join(scene_lang_dir, "prompts"))  # Also add prompts directory

# Now import the required modules
from mi_helper import shape_fn
from calc_utils import compute_shape_size_wrapper

from utils.convert_fbx_to_ply import run_blender_conversion

from coplay.supabase_db import connection_pool, get_admin_supabase_client
from coplay.storage import upload_file_to_supabase
from coplay.logger import logger
from coplay.asset_helpers import get_asset_description_from_model
from coplay.opentelemetry.helpers import set_trace_attribute, CoplayAttributes



async def process_uploaded_model(temp_file_path: str, model_file_path: str, user_id: str, content_hash: str, device_id: str):
    """
    Process a newly uploaded FBX file by doing two independent steps concurrently:
      1. Generate an asset description from the model file.
      2. Convert the FBX file to a PLY file and compute its shape sizes.
    Each task updates and commits its results to the DB as soon as it completes.
    """
    set_trace_attribute(CoplayAttributes.USER_ID, user_id)
    set_trace_attribute(CoplayAttributes.DEVICE_ID, device_id)

    async with connection_pool.connection() as conn:
        result = await conn.execute(
            "SELECT id FROM user_attached_models WHERE path = %s AND device_id = %s AND content_hash = %s",
            (model_file_path, device_id, content_hash)
        )
        if await result.fetchone() is not None:
            logger.info(f"Model {model_file_path} already exists in the database, skipping processing")
            return
                
    total_start_time = time.time()
    logger.info(f"Starting to process uploaded model: {model_file_path}")

    model_name = Path(model_file_path).stem.replace(' ', '_')

    # Run both tasks concurrently.
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor(max_workers=2) as executor:
        future_desc = loop.run_in_executor(executor, get_asset_description, temp_file_path, model_name)
        future_conv = loop.run_in_executor(executor, convert_to_ply_and_get_size, temp_file_path, model_name)
        # Wait for both tasks to complete.
        desc = await future_desc
        size, ply_file_path = await future_conv

    total_duration = time.time() - total_start_time

    if desc is None or size is None or ply_file_path is None:
        logger.error(f"Error processing {model_name}: {desc}, {size}, {ply_file_path}")
        return

    supabase_client = await get_admin_supabase_client()
    ply_file_path = await upload_file_to_supabase(ply_file_path, user_id, f"{device_id}/user-attached-models/{content_hash}-{model_name}.ply", supabase_client)

    async with connection_pool.connection() as conn:
        await conn.execute(
            "INSERT INTO user_attached_models (path, device_id, description, size, ply_path, content_hash) VALUES (%s, %s, %s, %s, %s, %s)",
            (model_file_path, device_id, desc, size, ply_file_path, content_hash)
        )
    
    logger.info(f"Processed {model_name} and uploaded to Supabase: Total processing time: {total_duration:.2f} seconds")

def get_asset_description(file_path: str, model_name: str):
    desc_start_time = time.time()
    try:
        desc = get_asset_description_from_model(file_path, model_name)
        desc_duration = time.time() - desc_start_time
        logger.info(f"Asset description generation took: {desc_duration:.2f} seconds")

        return desc
    except Exception as e:
        import traceback
        traceback.print_exc()  # This prints the full stack trace.
        logger.error(f"Error processing asset description for {file_path}: {e}")

# Define the conversion and shape computation step.
def convert_to_ply_and_get_size(file_path: str, model_name: str):
    try:
        # Run the Blender conversion.
        blender_start_time = time.time()
        ply_file_path = run_blender_conversion(file_path)
        blender_duration = time.time() - blender_start_time
        logger.info(f"Blender conversion took: {blender_duration:.2f} seconds")
        
        # Compute the shape and sizes.
        shape_start_time = time.time()
        shape = shape_fn(name=ply_file_path, scale=1.0)
        sizes = compute_shape_size_wrapper(shape)
        shape_duration = time.time() - shape_start_time
        logger.info(f"Shape size computation took: {shape_duration:.2f} seconds")
        
        size_x, size_y, size_z = sizes
        return (f"{size_x:.3f}x{size_y:.3f}x{size_z:.3f}", ply_file_path)
    except Exception as e:
        logger.error(f"Error in conversion and shape task for {model_name}: {e}")