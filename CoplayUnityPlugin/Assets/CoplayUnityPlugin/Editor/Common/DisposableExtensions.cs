using System;
using System.Reactive.Disposables;

namespace Coplay.Common
{
    public static class DisposableExtensions
    {
        /// <summary>
        /// Adds the disposable to the collection and returns it.
        /// </summary>
        public static T AddTo<T>(this T disposable, CompositeDisposable disposables) where T : IDisposable
        {
            disposables.Add(disposable);
            return disposable;
        }
    }
}
