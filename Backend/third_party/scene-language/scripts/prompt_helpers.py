import os
from pathlib import Path
from engine.constants import (
    ENGINE_MODE,
    PROMPT_MODE,
)
import re  # Make sure to import re

root = Path(__file__).parent

from coplay.models import UserAttachedModels

class HeaderManager:
    def __init__(self):
        self.attached_fbx_files = []
        
    def configure_inject_custom_objects(self, attached_fbx_files: list[UserAttachedModels]):
        self.attached_fbx_files = attached_fbx_files or []

    def read_header(self, engine_mode: str = ENGINE_MODE, prompt_mode: str = PROMPT_MODE) -> str:
        if engine_mode == "mi_from_minecraft":
            return ""  # we assume this is for rendering only
        if engine_mode == "mi":
            print(f"[INFO] engine_mode=mi, but use engine_mode=exposed for prompting")
            engine_mode = "exposed"
            
        header_dir = max(
            (root / "outputs/stubgen").glob(f"*-{engine_mode}-{prompt_mode}"),
            key=os.path.getmtime,
        )
        
        # header_file = header_dir / ("header_objects.pyi" if len(self.attached_fbx_files) > 0 else "header.pyi")
        header_file = header_dir / ("header_mixed.pyi" if len(self.attached_fbx_files) > 0 else "header.pyi")
        
        with open(header_file.as_posix(), "r") as f:
            s = f.read()

        if len(self.attached_fbx_files) > 0:
            s = self._inject_primitive_shapes(s)

        return s

    def _inject_primitive_shapes(self, header_string: str) -> str:
        """Injects primitive shape names and descriptions from database into header string."""
        objects = []
        descriptions = []
        for model in self.attached_fbx_files:
            objects.append(model.path)
            descriptions.append(model.description + f" [size: {model.size}]")
        
        quoted_objects = [f"'{obj}'" for obj in objects]
        # literal_str = f"Literal[{', '.join(quoted_objects)}]"
        base_shapes = "'cube', 'sphere', 'cylinder'"
        user_objects = ', '.join(quoted_objects)
        literal_str = f"Literal[{base_shapes}{', ' + user_objects if user_objects else ''}]"
        
        # Create the description string
        desc_lines = []
        for obj, desc in zip(objects, descriptions):
            desc_lines.append(f"        - '{obj}': {desc}")
        desc_str = '\n'.join(desc_lines)
        
        # Replace the Literal declaration in header_string using regex to allow any content between the brackets.
        new_header = re.sub(
            r"PrimitiveShape\s*=\s*Literal\[[^\]]*\]",
            f"PrimitiveShape = {literal_str}",
            header_string
        )

        print("objects: ")
        for obj in objects:
            print(obj)
        
        # new_header = new_header.replace(
        #     "name: PrimitiveShape - One of the predefined primitive shapes: \n        shape_kwargs:",
        #     f"name: PrimitiveShape - One of the predefined primitive shapes: \n{desc_str}\n        shape_kwargs:"
        # )
        
        # Replace the old regex with a more specific one that stops at 'cube'
        pattern = r"(name: PrimitiveShape - One of the predefined primitive shapes:\s*\n)(?:        - '[^']+'[^\n]*\n)*        - 'cube'"
        replacement = f"\\1{desc_str}\n        - 'cube'"
        new_header = re.sub(pattern, replacement, new_header)

        # --- NEW: Inject Example calls under "Example:" ---
        # We expect that `new_header` contains a line with "Example:".
        # Find that line and then insert one or two example lines after it.
        lines = new_header.splitlines()
        for idx, line in enumerate(lines):
            if "Examples:" in line:
                if len(objects) >= 2:
                    example_lines = [
                        "        - `primitive_call('{}', shape_kwargs={{scale=(1, 2, 1)}})`".format(objects[0]),
                        "        - `primitive_call('{}', shape_kwargs={{scale=(0.5, 0.5, 0.5)}})`".format(objects[1]),
                    ]
                elif len(objects) == 1:
                    example_lines = [
                        "        - `primitive_call('{}', shape_kwargs={{scale=(1, 2, 1)}})`".format(objects[0]),
                    ]
                else:
                    example_lines = []
                # Insert the example lines immediately after the "Example:" line.
                lines = lines[:idx+1] + example_lines + lines[idx+1:]
                break  # only inject once
        
        new_header = "\n".join(lines)
        # print("New header: ", new_header)
        return new_header

# Create a global instance
header_manager = HeaderManager()