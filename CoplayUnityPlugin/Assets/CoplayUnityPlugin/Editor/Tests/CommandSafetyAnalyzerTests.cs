using NUnit.Framework;
using Coplay.Common;
using System.Linq;

namespace Coplay.Tests
{
    [TestFixture]
    public class CommandSafetyAnalyzerTests
    {
        [Test]
        [TestCase("ls -la", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("cat file.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("pwd", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("echo hello", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("find . -name '*.cs'", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("grep pattern file.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("ps aux", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("df -h", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("git status", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("mv file1.txt file2.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("cp file1.txt file2.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("make build", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("vim file.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("rm -f file.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("rm -rf Assets/temp/", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("rm --recursive --force Assets/temp/", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("del myfile.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("chmod +x script.sh", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("chmod 755 myfile", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("chown user:group file.txt", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("kill 12345", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("killall myprocess", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("npm install", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("pip install package", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("brew install tool", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("tar -xzf archive.tar.gz", CommandSafetyAnalyzer.CommandRisk.Safe)]
        [TestCase("zip -r archive.zip folder/", CommandSafetyAnalyzer.CommandRisk.Safe)]
        public void AnalyzeCommand_StandardCommands_ReturnsSafe(string command, CommandSafetyAnalyzer.CommandRisk expectedRisk)
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);

            Assert.AreEqual(expectedRisk, analysis.Risk, $"Command '{command}' should be classified as {expectedRisk}");
            Assert.AreEqual(command, analysis.Command);
            Assert.IsNotEmpty(analysis.Reasons);
        }

        [Test]
        [TestCase("rm -rf /", CommandSafetyAnalyzer.CommandRisk.Fatal)] // Root deletion
        [TestCase("rm -rf /*", CommandSafetyAnalyzer.CommandRisk.Fatal)] // Root wildcard
        [TestCase("del /s \\*", CommandSafetyAnalyzer.CommandRisk.Fatal)] // Windows system deletion
        [TestCase("rd /s \\*", CommandSafetyAnalyzer.CommandRisk.Fatal)] // Windows remove directory
        [TestCase("del /q temp\\*", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("shutdown -h now", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("reboot", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("halt", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("poweroff", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("restart", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("sudo rm -rf /", CommandSafetyAnalyzer.CommandRisk.Fatal)] // Sudo root deletion
        [TestCase("sudo format /dev/sda", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("dd if=/dev/zero of=/dev/sda", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("dd if=/dev/zero of=/dev/nvme0n1", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("format C:", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("mkfs.ext4 /dev/sda1", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("fdisk /dev/sda", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("shred /dev/sda", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("wipe /dev/sda", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("passwd", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("useradd newuser", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        [TestCase("userdel user", CommandSafetyAnalyzer.CommandRisk.Fatal)]
        public void AnalyzeCommand_DangerousCommands_ReturnsFatal(string command, CommandSafetyAnalyzer.CommandRisk expectedRisk)
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);

            Assert.AreEqual(expectedRisk, analysis.Risk, $"Dangerous command '{command}' should be classified as Fatal");
            Assert.AreEqual(command, analysis.Command);
            Assert.IsTrue(analysis.Reasons.Count > 0, "Fatal commands should have explanatory reasons");
        }

        [Test]
        [TestCase("")]
        [TestCase("   ")]
        [TestCase("\t\n")]
        public void AnalyzeCommand_EmptyOrWhitespaceCommands_ReturnsSafe(string command)
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);

            Assert.AreEqual(CommandSafetyAnalyzer.CommandRisk.Safe, analysis.Risk);
            Assert.IsTrue(analysis.Reasons.Any(r => r.Contains("Empty") || r.Contains("No command")));
        }

        [Test]
        public void AnalyzeCommand_NullCommand_ReturnsSafe()
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(null);

            Assert.AreEqual(CommandSafetyAnalyzer.CommandRisk.Safe, analysis.Risk);
            Assert.IsNotEmpty(analysis.Reasons);
        }

        [Test]
        [TestCase("unknown_command_xyz")]
        [TestCase("custom_script.sh")]
        [TestCase("./myprogram")]
        public void AnalyzeCommand_UnknownCommands_ReturnsSafe(string command)
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);

            Assert.AreEqual(CommandSafetyAnalyzer.CommandRisk.Safe, analysis.Risk);
            Assert.IsTrue(analysis.Reasons.Any(r => r.Contains("classified as safe")));
        }

        [Test]
        [TestCase("ls 'file with spaces.txt'")]
        [TestCase("echo \"Hello World\"")]
        [TestCase("grep 'pattern with spaces' file.txt")]
        [TestCase("find . -name \"*.cs\"")]
        public void AnalyzeCommand_QuotedArguments_ParsedCorrectly(string command)
        {
            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);

            Assert.AreNotEqual(CommandSafetyAnalyzer.CommandRisk.Fatal, analysis.Risk,
                "Quoted safe commands should not be classified as fatal");
            Assert.IsNotEmpty(analysis.Reasons);
        }

        [Test]
        [TestCase("rm", new[] { "rm" })]
        [TestCase("ls -la", new[] { "ls", "-la" })]
        [TestCase("echo \"hello world\"", new[] { "echo", "hello world" })]
        [TestCase("find . -name '*.txt'", new[] { "find", ".", "-name", "*.txt" })]
        [TestCase("  git   status  ", new[] { "git", "status" })]
        public void TokenizeCommand_VariousInputs_ReturnsExpectedTokens(string command, string[] expectedTokens)
        {
            var tokens = CommandSafetyAnalyzer.TokenizeCommand(command);

            Assert.AreEqual(expectedTokens.Length, tokens.Length, "Token count should match");
            for (int i = 0; i < expectedTokens.Length; i++)
            {
                Assert.AreEqual(expectedTokens[i], tokens[i], $"Token {i} should match");
            }
        }

        [Test]
        [TestCase("")]
        [TestCase("   ")]
        [TestCase(null)]
        public void TokenizeCommand_EmptyInput_ReturnsEmptyArray(string command)
        {
            var tokens = CommandSafetyAnalyzer.TokenizeCommand(command);
            Assert.AreEqual(0, tokens.Length);
        }

        [Test]
        [TestCase("echo 'unclosed quote", new[] { "echo", "unclosed quote" }, TestName = "Single quote unclosed")]
        [TestCase("echo \"unclosed quote", new[] { "echo", "unclosed quote" }, TestName = "Double quote unclosed")]
        [TestCase("rm 'file with spaces.txt", new[] { "rm", "file with spaces.txt" }, TestName = "Unmatched quote with spaces")]
        public void TokenizeCommand_UnmatchedQuotes_TreatsRestAsOneToken(string command, string[] expectedTokens)
        {
            var tokens = CommandSafetyAnalyzer.TokenizeCommand(command);

            Assert.AreEqual(expectedTokens.Length, tokens.Length, "Token count should match");
            for (int i = 0; i < expectedTokens.Length; i++)
            {
                Assert.AreEqual(expectedTokens[i], tokens[i], $"Token {i} should match expected value");
            }
        }

        [Test]
        [TestCase(CommandSafetyAnalyzer.CommandRisk.Safe, "Command is safe - approval handled by LLM")]
        [TestCase(CommandSafetyAnalyzer.CommandRisk.Fatal, "Command is dangerous and blocked for safety")]
        public void GetRiskDescription_AllRiskLevels_ReturnsExpectedDescriptions(
            CommandSafetyAnalyzer.CommandRisk risk, string expectedDescription)
        {
            var description = CommandSafetyAnalyzer.GetRiskDescription(risk);
            Assert.AreEqual(expectedDescription, description);
        }

        [Test]
        public void AnalyzeCommand_ComplexCommand_AnalyzedCorrectly()
        {
            string complexCommand = "sudo rm -rf / && echo done";

            var analysis = CommandSafetyAnalyzer.AnalyzeCommand(complexCommand);
            Assert.AreEqual(CommandSafetyAnalyzer.CommandRisk.Fatal, analysis.Risk,
                "Complex command with dangerous elements should be fatal");
            Assert.IsTrue(analysis.Reasons.Any(r => r.Contains("sudo") || r.Contains("pattern")));
        }

        [Test]
        public void AnalyzeCommand_CaseSensitivity_WorksCorrectly()
        {
            // Test case insensitive command detection
            var testCases = new[]
            {
                ("RM file.txt", CommandSafetyAnalyzer.CommandRisk.Safe), // rm is now safe
                ("Shutdown", CommandSafetyAnalyzer.CommandRisk.Fatal),
                ("FORMAT C:", CommandSafetyAnalyzer.CommandRisk.Fatal),
                ("LS -la", CommandSafetyAnalyzer.CommandRisk.Safe),
                ("Git status", CommandSafetyAnalyzer.CommandRisk.Safe)
            };

            foreach (var (command, expectedRisk) in testCases)
            {
                var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);
                Assert.AreEqual(expectedRisk, analysis.Risk,
                    $"Case insensitive detection failed for '{command}'");
            }
        }

        [Test]
        public void AnalyzeCommand_PatternMatching_WorksCorrectly()
        {
            var patternTests = new[]
            {
                ("rm -rf /home", CommandSafetyAnalyzer.CommandRisk.Fatal), // Harmful scoped deletion
                ("rm -fr /home", CommandSafetyAnalyzer.CommandRisk.Fatal), // Harmful scoped deletion
                ("rm -rf /", CommandSafetyAnalyzer.CommandRisk.Fatal), // Harmful root deletion
                ("chmod 777 file", CommandSafetyAnalyzer.CommandRisk.Safe), // chmod is now safe
                ("chmod 755 file", CommandSafetyAnalyzer.CommandRisk.Safe), // chmod is now safe
            };

            foreach (var (command, expectedRisk) in patternTests)
            {
                var analysis = CommandSafetyAnalyzer.AnalyzeCommand(command);
                Assert.AreEqual(expectedRisk, analysis.Risk,
                    $"Pattern matching failed for '{command}'");
            }
        }
    }
}
