"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/toast";
import {
  forgotPasswordSchema,
  resetPasswordSchema,
  type ForgotPasswordFormData,
  type ResetPasswordFormData,
} from "@/schemas/auth";
import {
  sendPasswordResetLink,
  verifyRecoveryToken,
  parseUrlHashParams,
  resetPassword,
} from "@/services/authService";
import { useAsyncOperation } from "@/utils/formHelpers";

export type PasswordResetPhase = "request" | "verification" | "reset";

export function usePasswordReset(initialPhase: PasswordResetPhase = "request") {
  const router = useRouter();
  const toast = useToast();
  const [currentPhase, setCurrentPhase] = useState<PasswordResetPhase>(initialPhase);

  // Token verification state
  const [tokenStatus, setTokenStatus] = useState<
    "idle" | "verifying" | "verified" | "error"
  >("idle");

  // Forgot password functionality
  const { execute: executeSendResetLink, status: sendLinkStatus, isLoading: isSendingLink, resetStatus: resetSendLinkStatus } = useAsyncOperation(
    async (data: ForgotPasswordFormData) => {
      await sendPasswordResetLink(data.email);
    },
    () => {
      toast.success("Password reset link sent. Please check your email.");
    },
    (error) => {
      console.error("Forgot password error:", error);
      toast.error(error.message || "Failed to send reset link. Please try again.");
    }
  );

  // Reset password form
  const resetPasswordForm = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const { execute: executeResetPassword, status: resetPasswordStatus, isLoading: isResettingPassword } = useAsyncOperation(
    async (data: ResetPasswordFormData) => {
      await resetPassword(data.password);
      router.push("/login");
    },
    () => {
      toast.success(
        "Password reset successfully. You can now log in with your new password."
      );
    },
    (error) => {
      console.error("Password reset error:", error);
      toast.error(error.message || "Failed to reset password. Please try again.");
    }
  );

  // Token verification effect (runs when phase is "verification")
  useEffect(() => {
    if (currentPhase !== "verification") return;

    const verifyToken = async () => {

      try {
        const { accessToken } = parseUrlHashParams();

        if (!accessToken) {
          console.error("No hash found in URL");
          toast.error("Invalid or expired recovery link.");
          setTokenStatus("error");
          return;
        }
        setTokenStatus("verifying");
        await verifyRecoveryToken();
        setTokenStatus("verified");
        setCurrentPhase("reset");
      } catch (error) {
        console.error("Token verification failed:", error);
        const errorMessage = error instanceof Error ? error.message : "Invalid recovery token.";
        toast.error(errorMessage);
        setTokenStatus("error");
      }
    };

    verifyToken();
  }, [currentPhase, toast]);

  // Forgot password form (for request phase)
  const forgotPasswordForm = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const sendResetLink = async (data: ForgotPasswordFormData) => {
    await executeSendResetLink(data);
  };

  const resetPasswordSubmit = async (data: ResetPasswordFormData) => {
    await executeResetPassword(data);
  };

  return {
    // Current phase and navigation
    currentPhase,
    setCurrentPhase,

    // Forgot password (request phase)
    forgotPasswordForm,
    sendResetLink,
    sendLinkStatus,
    isSendingLink,
    resetSendLinkStatus,
    isSendLinkSuccess: sendLinkStatus === "success",
    isSendLinkError: sendLinkStatus === "error",

    // Token verification phase
    tokenStatus,
    isTokenVerifying: tokenStatus === "verifying",
    isTokenVerified: tokenStatus === "verified",
    isTokenError: tokenStatus === "error",

    // Reset password form phase
    resetPasswordForm,
    resetPasswordSubmit,
    resetPasswordStatus,
    isResettingPassword,
    isResetSuccess: resetPasswordStatus === "success",
    isResetError: resetPasswordStatus === "error",
  };
}
