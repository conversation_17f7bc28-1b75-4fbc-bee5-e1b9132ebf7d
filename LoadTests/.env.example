# Coplay Load Testing Environment Variables
# Copy this to .env and fill in your actual values

# Backend Configuration
BASE_URL=http://localhost:8080

# Authentication (get this from your frontend app)
K6_AUTH_TOKEN=your_bearer_token_here

# Device ID (required by backend - see instructions below)
K6_DEVICE_ID=cpl-device-44cadb49-36e3-4775-b639-56e557506996

# Test Configuration
VUS=5
DURATION=10s

# Model Configuration (for assistant endpoints)
K6_MODEL=gemini-2-5-flash

# Optional: Advanced Configuration
# ITERATIONS=100
# STAGES=5s:10,10s:20,5s:0

# INSTRUCTIONS:
# 1. Get K6_AUTH_TOKEN: Log into Coplay frontend, open dev tools, copy Bearer token from any request
# 2. Get K6_DEVICE_ID: Either:
#    a) Use existing device ID from your frontend (check network requests for 'x-device-id' header)
#    b) Create new device: POST to /auth/device endpoint (see README for details)
#    c) Use default 'cpl-device-44cadb49-36e3-4775-b639-56e557506996' (you may need to register this device first)
