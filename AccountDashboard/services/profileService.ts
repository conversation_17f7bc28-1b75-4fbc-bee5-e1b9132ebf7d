export interface Profile {
  id: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

import type { User } from "@supabase/supabase-js";
import type { AuthProvider } from "./authService";

export interface ProfileData {
  user: User;
  profile: Profile | null;
  authProvider: AuthProvider;
}

export interface ProfileUpdatePayload {
  name?: string;
  email?: string;
}

/**
 * Fetch user profile data from the API
 */
export async function fetchProfileData(): Promise<ProfileData> {
  const response = await fetch("/api/auth/profile");

  if (!response.ok) {
    throw new Error("Failed to fetch profile data");
  }

  const data = await response.json();
  return data;
}

/**
 * Update user profile with the provided payload
 */
export async function updateUserProfile(payload: ProfileUpdatePayload): Promise<void> {
  if (Object.keys(payload).length === 0) {
    throw new Error("No changes to save");
  }

  const response = await fetch("/api/auth/update-profile", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Failed to update profile");
  }
}

/**
 * Validate profile form data
 */
export function validateProfileForm(
  name: string,
  email: string,
  isOAuth: boolean
): boolean {
  return name.trim() !== "" && (email.trim() !== "" || isOAuth);
}

/**
 * Build update payload with only changed fields
 */
export function buildProfileUpdatePayload(
  currentName: string,
  currentEmail: string,
  initialName: string,
  initialEmail: string,
  isOAuth: boolean
): ProfileUpdatePayload {
  const payload: ProfileUpdatePayload = {};

  if (currentName !== initialName) {
    payload.name = currentName.trim();
  }

  // Email addresses are case-insensitive per RFC 5321
  if (currentEmail.toLowerCase() !== initialEmail.toLowerCase() && !isOAuth) {
    payload.email = currentEmail.trim();
  }

  return payload;
}

/**
 * Check what changes have been made to the profile
 */
export function getProfileChanges(
  currentName: string,
  currentEmail: string,
  initialName: string,
  initialEmail: string,
  isOAuth: boolean
): {
  hasChanges: boolean;
  nameChanged: boolean;
  emailChanged: boolean;
} {
  const nameChanged =
    currentName.trim() !== initialName.trim();
  const emailChanged =
    currentEmail.trim().toLowerCase() !==
    initialEmail.trim().toLowerCase() && !isOAuth;

  return {
    hasChanges: nameChanged || emailChanged,
    nameChanged,
    emailChanged,
  };
}
