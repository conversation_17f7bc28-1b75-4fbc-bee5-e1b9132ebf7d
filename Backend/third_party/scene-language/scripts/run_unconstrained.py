from pathlib import Path
from run_utils import run, SYSTEM_RULES, read_example
from engine.utils.argparse_utils import setup_save_dir
import argparse
from validate_scene import SceneValidator
from prompt_helpers import header_manager

from coplay.models import UserAttachedModels

root = Path(__file__).parent


def get_user_prompt(scene_description, latest_program: str, previous_error_msg: str):
    # This means it is the first iteration, it will start by producing some basic objects
    if not latest_program:
        return '''Here are some examples of how to use `helper.py`:
```python
{example}
```
IMPORTANT: THE FUNCTIONS ABOVE ARE JUST EXAMPLES, YOU CANNOT USE THEM IN YOUR PROGRAM!
{previous_error_msg}

Now, write a similar program for the given description of a scene:    
```python
from helper import *

"""
{scene_description}
"""
```
'''.format(scene_description=scene_description, example=read_example(), previous_error_msg=previous_error_msg)
    else:
        return '''In previous rounds, you have already implemented a scene using `helper.py`:
```python
{latest_program}
```

Your task is to read over the above, and WITHOUT changing or modifying the existing program, either:

1. Compose the existing functions into new object groupings or new scenes.
2. Write brand new functions that DON'T EXIST yet in the library.
3. There should only be one root node in the scene.

We will then add your new functions to the previous program and iteratively build the scene. Remember that the end goal is to make the following scene: {scene_description}
{previous_error_msg}

```python
# TODO: Add your new functions here!
```
'''.format(scene_description=scene_description, latest_program=latest_program, previous_error_msg=previous_error_msg)

def get_parser():
    parser = argparse.ArgumentParser()
    parser.add_argument('--depth', default=5, type=int, help='depth')
    parser.add_argument('--log-dir', type=str, default=(root / 'outputs' / Path(__file__).stem).as_posix())
    
    # Add mutually exclusive group for task arguments
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--task', type=str, help='scene description')
    group.add_argument('--task-json', type=str, help='path to JSON file containing scene description')
    return parser

def run_unconstrained(scene_description: str, save_dir: Path, depth: int, status_callback=None, attached_fbx_files: list[UserAttachedModels] = None, model: str = "claude-3-7-sonnet"):
    print(f"[INFO] Scene description: {scene_description}")

    header_manager.configure_inject_custom_objects(attached_fbx_files)
    
    system_prompt = '''
    You are a code completion model and can only write python functions wrapped within ```python```.

    You are provided with the following `helper.py` which defines the given functions and definitions:
    ```python
    {header}
    ```

    {rules}
    You should be precise and creative.
    '''.format(header=header_manager.read_header(), rules=SYSTEM_RULES)

    existing_scenes = [d for d in save_dir.glob('scene*') if d.is_dir()]
    next_scene_num = len(existing_scenes) + 1
    name = f"scene{next_scene_num}"
    save_subdir = save_dir / name
    save_subdir.mkdir(exist_ok=True)

    scene_validator = SceneValidator()

    print(f"[INFO] Running unconstrained generation with depth={depth}")

    programs = []
    latest_program = None
    for idx in range(depth):
        if status_callback:
            status_callback(idx, depth, "Starting iteration")
            
        print(f"[INFO] depth={idx}/{depth}")
        depth_dir_name = f"depth_{idx}"
        depth_save_subdir = save_subdir / depth_dir_name
        depth_save_subdir.mkdir(exist_ok=True)

        if status_callback:
            status_callback(idx, depth, "Generating prompt")
        
        # NOTE: there are too many random errors that can happen.
        # More robust validation would be to run ask an LLM if it will work in parallel with the next generation request.
        # Even better would be to run mitsuba rendering, and check it it worked (like we used to). The problem is that mitsuba takes really long in cloud.
        user_prompt = get_user_prompt(scene_description, latest_program, previous_error_msg=scene_validator.error_message)
        with open(depth_save_subdir / 'system_prompt.md', 'w') as f:
            f.write(system_prompt)
        with open(depth_save_subdir / 'user_prompt.md', 'w') as f:
            f.write(user_prompt)

        if status_callback:
            status_callback(idx, depth, "Running model inference")

        new_programs = run(save_dir=depth_save_subdir.as_posix(),
            user_prompt=user_prompt,
            system_prompt=system_prompt,
            extra_info={'task': scene_description},
            execute=False,
            lm_config={'num_completions': 1, 'temperature': 0.2},
            code_only=True,
            prepend_program=latest_program,
            scene_validator=scene_validator,
            model=model,
            )
        
        if new_programs:
            # Because num_completions is 1, we only want to extract the first one
            programs.append(new_programs[0])
            latest_program = '\n\n'.join(programs)

        print("Done with depth", idx)

    return depth_save_subdir

def main():
    parser = get_parser()
    args = parser.parse_args()
    save_dir = setup_save_dir(args.log_dir, log_unique=True)

    # Process task from either string or JSON file
    if args.task_json:
        import json
        with open(args.task_json, 'r') as f:
            json_data = json.load(f)
            if not isinstance(json_data, dict):
                raise ValueError("JSON file must contain a dictionary")
            # Convert dictionary to string with each key-value pair on a new line
            task = '\n'.join(f"{key}: {value}" for key, value in json_data.items())
    else:
        task = args.task

    run_unconstrained(task, save_dir, args.depth)


if __name__ == "__main__":
    main()
