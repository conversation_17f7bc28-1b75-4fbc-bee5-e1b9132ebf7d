from datetime import datetime, timezone
import json
from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON>

from .client import ensure_dataset_and_table, insert_rows_to_table
from ..models import ToolCallResult
from ..logger import get_coplay_env, logger


# Schema for usage data table
# NOTE: if you edit this table, you have to make the same edit in the Bigquery schema in the google console.
# NOTE: when updating this table, update the integration test as needed.
TOOL_CALL_RESULTS_SCHEMA = [
    Schem<PERSON><PERSON><PERSON>("timestamp", "TIMESTAMP", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("tool_call_id", "STRING", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON>ield("thread_id", "STRING", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("message_id", "STRING", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("outcome", "STRING", mode="REQUIRED"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("error", "STRING", mode="NULLABLE"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("coplay_version", "STRING", mode="NULLABLE"),
]

# Constants for usage data table
_DATASET_ID = "coplay_analytics"
_TABLE_ID = "tool_call_results"

async def insert_tool_call_result_data(tool_call_result: ToolCallResult) -> bool:
    """Insert usage data into BigQuery"""
    try:
        # Ensure dataset and table exist
        await ensure_dataset_and_table(_DATASET_ID, _TABLE_ID, TOOL_CALL_RESULTS_SCHEMA)
        
        # Convert usage data to BigQuery row format
        row = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": tool_call_result.user_id,
            "thread_id": tool_call_result.thread_id,
            "message_id": tool_call_result.message_id,
            "tool_call_id": tool_call_result.tool_call_id,
            "tool_name": tool_call_result.tool_name,
            "outcome": tool_call_result.outcome,
            "error": tool_call_result.error,
            "model": tool_call_result.model,
            "mode": tool_call_result.mode,
            "system_details": json.dumps(tool_call_result.system_details),
            "coplay_env": get_coplay_env(),
            "coplay_version": tool_call_result.coplay_version,
        }
        
        # Insert the row using generic function
        return await insert_rows_to_table(_DATASET_ID, _TABLE_ID, [row])
        
    except Exception as e:
        logger.error(f"Error inserting usage data to BigQuery: {e}")
        return False
