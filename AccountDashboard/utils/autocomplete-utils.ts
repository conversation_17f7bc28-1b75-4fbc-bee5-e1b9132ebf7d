export function getAutocompleteValue(
  type: "email" | "password" | "text",
  name: string,
  isSignup: boolean
): string {
  if (type === "email") {
    return "email";
  }

  if (type === "password") {
    // For password confirmation fields
    if (name.toLowerCase().includes("confirm")) {
      return "new-password";
    }
    // For signup, use new-password; for login, use current-password
    return isSignup ? "new-password" : "current-password";
  }

  // For text fields, check for username first (more specific than name)
  if (name.toLowerCase().includes("username")) {
    return "username";
  }
  // Then check for name fields
  if (name.toLowerCase().includes("name")) {
    return "name";
  }

  // Default to off for other text fields
  return "off";
}
