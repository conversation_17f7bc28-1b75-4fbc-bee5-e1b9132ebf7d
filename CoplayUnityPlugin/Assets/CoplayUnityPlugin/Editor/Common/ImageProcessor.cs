using System;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Coplay.Common
{
    /// <summary>
    /// Utility class for processing images, including size checking and downsampling.
    /// </summary>
    public static class ImageProcessor
    {
        /// <summary>
        /// Maximum dimension for downsampled images (applied to the larger dimension)
        /// </summary>
        private const int MaxDimension = 1536;

        /// <summary>
        /// JPEG encoding quality (0-100)
        /// </summary>
        private const int JpegQuality = 85;

        /// <summary>
        /// Common image file extensions
        /// </summary>
        private static readonly string[] ImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp" };

        /// <summary>
        /// Checks if a file is an image based on its extension
        /// </summary>
        /// <param name="filePath">Path to the file</param>
        /// <returns>True if the file is an image, false otherwise</returns>
        public static bool IsImage(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            string extension = Path.GetExtension(filePath).ToLowerInvariant();
            return Array.IndexOf(ImageExtensions, extension) >= 0;
        }

        /// <summary>
        /// Processes an image file (downsamples regardless of size)
        /// </summary>
        /// <param name="filePath">Path to the image file</param>
        /// <param name="logMessage">Custom log message to display</param>
        /// <returns>Path to the processed image, or the original path if processing failed</returns>
        public static async Task<string> ProcessImage(string filePath, string logMessage = "Processing image")
        {
            // Check if the file is an image and exists
            if (!IsImage(filePath) || !File.Exists(filePath))
                return filePath;

            Texture2D texture = null;
            Texture2D resizedTexture = null;
            try
            {
                CoplayLogger.Log($"{logMessage}: {filePath}");

                // Load the image
                byte[] fileData = File.ReadAllBytes(filePath);
                texture = new Texture2D(2, 2);
                if (!texture.LoadImage(fileData))
                {
                    CoplayLogger.LogError($"Failed to load image: {filePath}");
                    Object.DestroyImmediate(texture);
                    return filePath;
                }

                // Calculate new dimensions while maintaining aspect ratio
                // Scale down so the larger dimension is MaxDimension (1536)
                int originalWidth = texture.width;
                int originalHeight = texture.height;

                // Find the larger dimension
                int largerDimension = Mathf.Max(originalWidth, originalHeight);

                // If image is already smaller than max dimension, return original file
                if (largerDimension <= MaxDimension)
                {
                    return filePath;
                }

                // Calculate new dimensions for downsampling
                float scale = (float)MaxDimension / largerDimension;
                int newWidth = Mathf.RoundToInt(originalWidth * scale);
                int newHeight = Mathf.RoundToInt(originalHeight * scale);

                // Resize the texture
                Color[] pixels = texture.GetPixels();
                Color[] resizedPixels = new Color[newWidth * newHeight];

                float xRatio = (float)texture.width / newWidth;
                float yRatio = (float)texture.height / newHeight;

                await Task.Run(() =>
                {
                    for (int y = 0; y < newHeight; y++)
                    {
                        for (int x = 0; x < newWidth; x++)
                        {
                            int originalX = Mathf.FloorToInt(x * xRatio);
                            int originalY = Mathf.FloorToInt(y * yRatio);
                            int originalIndex = originalY * originalWidth + originalX;
                            int newIndex = y * newWidth + x;

                            if (originalIndex < pixels.Length && newIndex < resizedPixels.Length)
                            {
                                resizedPixels[newIndex] = pixels[originalIndex];
                            }
                        }
                    }
                });

                // Create a new texture with the calculated dimensions
                resizedTexture = new Texture2D(newWidth, newHeight, TextureFormat.RGB24, false);

                resizedTexture.SetPixels(resizedPixels);
                resizedTexture.Apply();

                // Encode to JPEG
                byte[] jpegData = resizedTexture.EncodeToJPG(JpegQuality);

                // Create output path
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string outputDir = Path.Combine(Path.GetTempPath(), "CoplayDownsampled");
                Directory.CreateDirectory(outputDir);
                string outputPath = Path.Combine(outputDir, $"{fileName}_{newWidth}x{newHeight}.jpg");

                // Save the processed image
                File.WriteAllBytes(outputPath, jpegData);

                CoplayLogger.Log($"Image processed to {newWidth}x{newHeight}, saved to: {outputPath}");

                return outputPath;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error processing image: {ex.Message}", ex);
                return filePath;
            }
            finally
            {
                // Clean up textures
                if (texture != null)
                {
                    Object.DestroyImmediate(texture);
                }
                if (resizedTexture != null)
                {
                    Object.DestroyImmediate(resizedTexture);
                }
            }
        }
    }
}
