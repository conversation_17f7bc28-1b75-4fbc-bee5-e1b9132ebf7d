# Coplay Playbooks System

Playbooks are structured instruction sets that guide users through
specific Unity development tasks.

## Folder Structure

    Backend/coplay/playbooks/
    ├── README.md                   # This documentation
    ├── playbook_service.py         # Core service functions
    └── playbook_strings/           # Playbook content files
        ├── create_a_game.txt       # Example: Game creation workflow
        ├── tower.txt               # Example: Tower building
        └── [additional_playbooks]  # Additional playbook files

## Playbook File Format

Each playbook file must follow this exact format:

    DESCRIPTION:
    [Brief description of when to use this playbook]

    BODY:
    [Step-by-step instructions]

## Adding New Playbooks

### Step 1: Create the File

1.  Navigate to `Backend/coplay/playbooks/playbook_strings/`
2.  Create a new `.txt` file with a descriptive name (e.g.,
    `setup_lighting.txt`)
3.  Use lowercase with underscores for consistency

### Step 2: Write the Content

    DESCRIPTION:
    [Provide a clear, concise description of when this playbook should be used]

    BODY:
    [Write numbered, step-by-step instructions]

## Dynamic Documentation

The system automatically generates documentation with the
`@dynamic_playbook_docstring` decorator, using the file name and
description from the playbook file.
