"""
Utility functions for loading prompts from various sources for recipe embeddings.
"""
import os
import json
from pathlib import <PERSON>
from typing import Tuple, List
from coplay.logger import logger

def load_prompts_from_json(keyword: str, current_dir: Path) -> Tuple[List[str], List[str]]:
    """
    Load prompts from keyword.json file in all_prompts_by_keyword folder.
    
    Args:
        keyword: The keyword to load prompts for
        current_dir: The directory containing the recipe files
        
    Returns:
        Tuple of (positive_prompts, negative_prompts)
        - positive_prompts: list of prompts where negative=false
        - negative_prompts: list of prompts where negative=true
        - Skips prompts where negative=""
    """
    json_path = current_dir / "all_prompts_by_keyword" / f"{keyword}.json"
    
    if not json_path.exists():
        logger.info(f"No JSON file found at {json_path}")
        return [], []
    
    try:
        with open(json_path, "r", encoding='utf-8') as f:
            data = json.load(f)
        
        positive_prompts = []
        negative_prompts = []
        
        for item in data:
            if not isinstance(item, dict) or "prompt" not in item:
                continue
                
            negative_value = item.get("negative", "")
            
            # Skip if negative is empty string
            if negative_value == "":
                continue
            elif negative_value is False:
                positive_prompts.append(item["prompt"])
            elif negative_value is True:
                negative_prompts.append(item["prompt"])
        
        logger.info(f"Loaded from {json_path}: {len(positive_prompts)} positive prompts, {len(negative_prompts)} negative prompts")
        return positive_prompts, negative_prompts
        
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.error(f"Error reading {json_path}: {e}")
        return [], []

def load_prompts_from_folders(keyword: str, current_dir: Path) -> Tuple[List[str], List[str]]:
    """
    Load prompts from positive_prompts and negative_prompts folders.
    
    Args:
        keyword: The keyword to load prompts for
        current_dir: The directory containing the recipe files
        
    Returns:
        Tuple of (positive_prompts, negative_prompts)
    """
    positive_prompts = []
    negative_prompts = []
    
    # Load positive prompts
    positive_folder = current_dir / "positive_prompts"
    if positive_folder.exists():
        files_to_process = [f for f in os.listdir(positive_folder) if f.endswith(".txt")]
        for file in files_to_process:
            try:
                with open(positive_folder / file, "r", encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and keyword.lower() in line.lower():
                            positive_prompts.append(line)
            except Exception as e:
                logger.error(f"Error reading {positive_folder / file}: {e}")
    
    # Load negative prompts
    negative_folder = current_dir / "negative_prompts"
    if negative_folder.exists():
        files_to_process = [f for f in os.listdir(negative_folder) if f.endswith(".txt")]
        for file in files_to_process:
            try:
                with open(negative_folder / file, "r", encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and keyword.lower() in line.lower():
                            negative_prompts.append(line)
            except Exception as e:
                logger.error(f"Error reading {negative_folder / file}: {e}")
    
    logger.info(f"Loaded from folders: {len(positive_prompts)} positive prompts, {len(negative_prompts)} negative prompts")
    return positive_prompts, negative_prompts

def load_prompts_for_keyword(keyword: str, current_dir: Path) -> Tuple[List[str], List[str]]:
    """
    Load prompts for a given keyword, trying JSON first, then falling back to folders.
    
    Args:
        keyword: The keyword to load prompts for
        current_dir: The directory containing the recipe files
        
    Returns:
        Tuple of (positive_prompts, negative_prompts)
    """
    # First try loading from JSON
    positive_prompts, negative_prompts = load_prompts_from_json(keyword, current_dir)
    
    # If no prompts found in JSON, fall back to folders
    if not positive_prompts and not negative_prompts:
        logger.info(f"No prompts found in JSON for '{keyword}', trying folder-based loading...")
        positive_prompts, negative_prompts = load_prompts_from_folders(keyword, current_dir)
    
    return positive_prompts, negative_prompts

def load_negative_prompts_only(keyword: str, current_dir: Path) -> List[str]:
    """
    Load only negative prompts for a given keyword (for backwards compatibility).
    
    Args:
        keyword: The keyword to load prompts for
        current_dir: The directory containing the recipe files
        
    Returns:
        List of negative prompts
    """
    _, negative_prompts = load_prompts_for_keyword(keyword, current_dir)
    return negative_prompts 