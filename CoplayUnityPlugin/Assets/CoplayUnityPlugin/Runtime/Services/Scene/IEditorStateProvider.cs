using System.Threading.Tasks;
using Coplay.Services.Scene.Models;
using JetBrains.Annotations;
using UnityEngine;

namespace Coplay.Services.Scene
{
    /// <summary>
    /// Interface for Unity editor state provider
    /// </summary>
    public interface IEditorStateProvider
    {
        /// <summary>
        /// Gets the current editor state
        /// </summary>
        /// <returns>The editor state or null if an error occurs</returns>
        [ItemCanBeNull]
        Task<EditorState> GetEditorStateAsync();

        /// <summary>
        /// Gets the current scene hierarchy with optional filtering
        /// </summary>
        /// <param name="request">Request with filtering parameters</param>
        /// <returns>A result containing game objects in the scene hierarchy and optional message</returns>
        Task<HierarchyListResult> QuerySceneAsync(QuerySceneRequest request = null);

        /// <summary>
        /// Finds a GameObject by its path in the hierarchy
        /// </summary>
        /// <param name="path">The path of the GameObject (e.g. "/Root/Parent/Child")</param>
        /// <returns>The GameObject if found, null otherwise</returns>
        GameObject FindGameObjectByPath(string path);
    }
}
