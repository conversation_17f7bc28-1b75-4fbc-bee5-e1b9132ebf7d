-- Convert DECIMAL/NUMERIC columns used for credits to DOUBLE PRECISION (float)
-- Note: This is a lossy change if values exceed double precision; acceptable for USD cents-level precision.

begin;

-- credits table: subscription_credits and topup_credits
do $$
begin
  if exists (
    select 1
    from information_schema.columns
    where table_schema = 'public' and table_name = 'credits' and column_name = 'subscription_credits'
      and data_type in ('numeric')
  ) then
    alter table public.credits
      alter column subscription_credits type double precision using subscription_credits::double precision,
      alter column subscription_credits set default 0.0;
  end if;

  if exists (
    select 1
    from information_schema.columns
    where table_schema = 'public' and table_name = 'credits' and column_name = 'topup_credits'
      and data_type in ('numeric')
  ) then
    alter table public.credits
      alter column topup_credits type double precision using topup_credits::double precision,
      alter column topup_credits set default 0.0;
  end if;
end $$;

-- credit_transactions table: amount and balance_after
do $$
begin
  if exists (
    select 1
    from information_schema.columns
    where table_schema = 'public' and table_name = 'credit_transactions' and column_name = 'amount'
      and data_type in ('numeric')
  ) then
    alter table public.credit_transactions
      alter column amount type double precision using amount::double precision;
  end if;

  if exists (
    select 1
    from information_schema.columns
    where table_schema = 'public' and table_name = 'credit_transactions' and column_name = 'balance_after'
      and data_type in ('numeric')
  ) then
    alter table public.credit_transactions
      alter column balance_after type double precision using balance_after::double precision;
  end if;
end $$;

-- plans table: price
do $$
begin
  if exists (
    select 1
    from information_schema.columns
    where table_schema = 'public' and table_name = 'plans' and column_name = 'price'
      and data_type in ('numeric')
  ) then
    alter table public.plans
      alter column price type double precision using price::double precision;
  end if;
end $$;

commit;


