// Error type detection and messaging for auth errors
export function getErrorDetails(message: string, error: string | null) {
  const errorTypes = {
    email_not_confirmed: {
      title: "Email Verification Required",
      description:
        "Please check your email and click the verification link before signing in.",
      type: "warning" as const,
      showTryAgain: false,
      showResendEmail: true,
    },
    invalid_credentials: {
      title: "Invalid Credentials",
      description:
        "The email or password you entered is incorrect. Please try again.",
      type: "error" as const,
      showTryAgain: true,
      showResendEmail: false,
    },
    too_many_requests: {
      title: "Too Many Attempts",
      description:
        "Too many login attempts. Please wait a few minutes before trying again.",
      type: "warning" as const,
      showTryAgain: false,
      showResendEmail: false,
    },
    signup_disabled: {
      title: "Signup Disabled",
      description:
        "New account registration is currently disabled. Please contact support.",
      type: "error" as const,
      showTryAgain: false,
      showResendEmail: false,
    },
    access_denied: {
      title: "Access Denied",
      description: "You do not have permission to access this resource.",
      type: "error" as const,
      showTryAgain: false,
      showResendEmail: false,
    },
    server_error: {
      title: "Server Error",
      description:
        "An unexpected error occurred on our servers. Please try again later.",
      type: "error" as const,
      showTryAgain: true,
      showResendEmail: false,
    },
  };

  // Check for specific error types using exact matching with word boundaries
  for (const [key, details] of Object.entries(errorTypes)) {
    // Create regex patterns for exact matching
    // Convert underscores to match spaces or underscores in the message
    const messagePattern = new RegExp(`\\b${key.replace(/_/g, "[\\s_]+")}\\b`, "i");
    const errorPattern = new RegExp(`\\b${key}\\b`, "i");

    if (
      messagePattern.test(message) ||
      (error && errorPattern.test(error))
    ) {
      return details;
    }
  }

  // Default error
  return {
    title: "Authentication Failed",
    description:
      message || "An error occurred during authentication. Please try again.",
    type: "error" as const,
    showTryAgain: true,
    showResendEmail: false,
  };
}
