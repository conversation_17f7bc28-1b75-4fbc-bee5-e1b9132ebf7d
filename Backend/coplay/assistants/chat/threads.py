from datetime import datetime, timezone
import uuid

from langchain_core.load import dumps as lc_dumps, load as lc_load

from ...logger import logger
from ...models import AssistantMode, CoplayThread
from ...supabase_db import connection_pool
from ...cache import CacheNames, cache
from ...utils.timing import async_timing


async def create_thread(assistant_mode: AssistantMode, device_id: str, name: str | None = None) -> CoplayThread:
    """Initialize a new assistant and return the thread"""
    thread_id = (f"cpl-thread-" + str(uuid.uuid4())).replace("-", "_")

    # Create a new assistant in the database
    async with connection_pool.connection() as conn:
        result = await conn.execute("INSERT INTO threads (id, assistant_mode, name, device_id, created_at, messages) VALUES (%(id)s, %(assistant_mode)s, %(name)s, %(device_id)s, %(created_at)s, %(messages)s) RETURNING id, assistant_mode, created_at, name", {
            "id": thread_id,
            "assistant_mode": assistant_mode,
            "name": name,
            "device_id": device_id,
            "created_at": datetime.now(tz=timezone.utc),
            "messages": lc_dumps([])
        })
        thread = await result.fetchone()

    logger.info(f"Created assistant with thread ID: {thread_id}")
    thread = CoplayThread(id=thread["id"], assistant_mode=thread["assistant_mode"], created_at=thread["created_at"], name=thread["name"])

    return thread

async def update_thread(thread_id: str, name: str | None = None, assistant_mode: AssistantMode | None = None) -> CoplayThread:
    async with connection_pool.connection() as conn:
        result = await conn.execute("UPDATE threads SET name = %(name)s, assistant_mode = %(assistant_mode)s WHERE id = %(thread_id)s RETURNING id, assistant_mode, created_at, name", {"thread_id": thread_id, "name": name, "assistant_mode": assistant_mode})
        thread = await result.fetchone()
    
    thread = CoplayThread(id=thread["id"], assistant_mode=thread["assistant_mode"], created_at=thread["created_at"], name=thread["name"])
    await cache.delete(thread_id, namespace=CacheNames.THREADS)

    return thread

async def update_thread_messages(thread_id: str, messages: list[dict], turn: int) -> None:        
    async with connection_pool.connection() as conn:
        result = await conn.execute("UPDATE threads SET messages = %(messages)s, turn = %(turn)s WHERE id = %(thread_id)s RETURNING id, assistant_mode, created_at, name, messages, turn", {"thread_id": thread_id, "messages": lc_dumps(messages), "turn": turn})
        thread = await result.fetchone()
    
    thread = CoplayThread(id=thread["id"], assistant_mode=thread["assistant_mode"], created_at=thread["created_at"], name=thread["name"], messages=lc_load(thread["messages"] or [], valid_namespaces=["coplay"]), turn=thread["turn"])
    await cache.set(thread_id, thread, ttl=60 * 5, namespace=CacheNames.THREADS) # 5 minutes

@async_timing
async def get_thread(thread_id: str) -> CoplayThread | None:
    cached_thread = await cache.get(thread_id, namespace=CacheNames.THREADS)
    if cached_thread is not None:
        return cached_thread
    
    async with connection_pool.connection() as conn:
        result = await conn.execute("SELECT * FROM threads WHERE id = %(thread_id)s", {"thread_id": thread_id})
        thread = await result.fetchone()
    
    if thread is None:
        return None
    
    thread = CoplayThread(id=thread["id"], assistant_mode=thread["assistant_mode"], created_at=thread["created_at"], name=thread["name"], messages=lc_load(thread["messages"] or [], valid_namespaces=["coplay"]), turn=thread["turn"])
    await cache.set(thread_id, thread, ttl=60 * 5, namespace=CacheNames.THREADS) # 5 minutes
    return thread

async def get_threads_by_device(device_id: str) -> list[CoplayThread]:
    async with connection_pool.connection() as conn:
        result = await conn.execute("SELECT id, assistant_mode, created_at, name FROM threads WHERE device_id = %(device_id)s ORDER BY created_at DESC", {"device_id": device_id})
        return [CoplayThread(id=thread["id"], assistant_mode=thread["assistant_mode"], created_at=thread["created_at"], name=thread["name"]) for thread in await result.fetchall()]

async def delete_thread(thread_id: str) -> None:
    async with connection_pool.connection() as conn:
        await conn.execute("DELETE FROM threads WHERE id = %(thread_id)s", {"thread_id": thread_id})
    
    await cache.delete(thread_id, namespace=CacheNames.THREADS)