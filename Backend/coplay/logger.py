import asyncio
from datetime import datetime, timezone
import time
import logging
import os
import sys
from typing import Any

from opentelemetry import trace as otel_trace
from opentelemetry import baggage

from .constants import GCP_PROJECT_ID


def get_coplay_env():
    """Get the coplay environment."""
    if os.getenv('LANGSMITH_PROJECT') == 'pr-coplay-prod':
        return 'prod'
    return 'dev'


def is_test_environment():
    """Detect if we're running in a test environment."""
    return 'pytest' in sys.modules or os.environ.get('TESTING') == 'true'

def is_local_environment():
    """Detect if we're running in a local environment."""
    return os.environ.get('DEVELOPMENT') == 'true'

def is_production_environment():
    """Detect if we're running in a production environment."""
    return get_coplay_env() == 'prod'

def _trace_context_kwargs() -> dict[str, Any]:
    """Return kwargs for Cloud Logging to correlate logs with current OTEL span."""
    try:
        span = otel_trace.get_current_span()
        ctx = span.get_span_context()
        if not ctx.is_valid:
            return {}
        trace_id_hex = format(ctx.trace_id, '032x')
        span_id_hex = format(ctx.span_id, '016x')
        return {
            "trace": f"projects/{GCP_PROJECT_ID}/traces/{trace_id_hex}",
            "span_id": span_id_hex,
            "trace_sampled": bool(getattr(ctx.trace_flags, "sampled", False)),
        }
    except Exception:
        return {}


def _coplay_attributes_to_labels() -> dict[str, Any]:
    """Convert Coplay attributes to labels from OTEL baggage."""
    try:
        return {b: v for b, v in baggage.get_all().items()}
    except Exception:
        return {}

SEVERITY_MAP = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

class CoplayLogger(object):
    SUBMISSION_THRESHOLD = 30
    SUBMISSION_INTERVAL = 10
    MAX_SUBMISSION_ATTEMPTS = 3
    def __init__(self):
        self._submitting = False
        self._last_submitted = 0.0
        # Standard Python logger for console output
        self.std_logger = self._initialize_console_logger()

        # Only initialize Google Cloud logging if we're not in a test environment
        if not is_test_environment() and not is_local_environment():
            self.cloud_logger = self._initialize_cloud_logger()
        else:
            self.cloud_logger = None
            self.std_logger.info(
                "Running in test environment, Google Cloud logging disabled")
    
    def _initialize_console_logger(self):
        """
        Initialize the console logger.
        """
        # Create a standard output stream handler for console logging
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter(
            # '{"timestamp": "%(asctime)s", "severity": "%(levelname)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}'
            '%(levelname)s    %(asctime)s    %(message)s'
        )
        console_handler.setFormatter(formatter)
        logger = logging.getLogger("coplay")
        logger.setLevel(logging.INFO)
        logger.addHandler(console_handler)
        return logger

    def _initialize_cloud_logger(self):
        """
        Initialize the Google Cloud logger.
        """
        try:
            from google.cloud import logging as cloud_logging
            from google.cloud.logging_v2.logger import Batch
            # Initialize the Google Cloud client
            client = cloud_logging.Client()
            # Get a logger for our custom log name
            cloud_logger = client.logger("coplay-logs")
            # Use batching to reduce network overhead
            batch_logger = Batch(cloud_logger, client)
            self.std_logger.info("Google Cloud logging initialized")
            return batch_logger
        except ImportError:
            self.std_logger.warning(
                "Google Cloud logging client not available, using console logging only")
        except Exception as e:
            self.std_logger.warning(f"Failed to initialize Google Cloud logging: {e}")
        
        return None
    
    def log(self, message, severity=logging.INFO, timestamp=None, labels=None, skip_console=False, exc_info=False, **fields):
        """
        Log a message to both console and Google Cloud (if enabled).

        Args:
            message (str): The main log message
            severity (str): Log severity (INFO, ERROR, WARNING, etc.)
            timestamp: Optional timestamp for the log entry
            labels (dict): Labels to add to the log entry (user, ua, etc.)
            skip_console (bool): If True, skips logging to console
            **fields: Additional fields to include in the structured log
        """
        # Log to console unless explicitly skipped
        if not skip_console:
            self.std_logger.log(severity, message, exc_info=exc_info)

        # Log to Google Cloud if enabled
        if self.cloud_logger is not None:
            # Create the json payload
            payload = {
                "message": message,
            }

            if timestamp is None:
                timestamp = datetime.now(timezone.utc)

            # Add any additional fields
            if fields:
                payload.update(fields)

            # Add opentelemetry trace context to the log entry
            trace_kwargs = _trace_context_kwargs()
            labels = (labels or {}) | _coplay_attributes_to_labels()
            # Write the log entry to Google Cloud
            self.cloud_logger.log_struct(
                payload,
                severity=logging.getLevelName(severity),
                timestamp=timestamp,
                labels={k: v for k, v in labels.items() if v is not None}, # Filter out None values
                **trace_kwargs,
            )
            if (len(self.cloud_logger.entries) > self.SUBMISSION_THRESHOLD or time.time() - self._last_submitted > self.SUBMISSION_INTERVAL) and not self._submitting:
                self._submitting = True
                self._last_submitted = time.time()
                # Use an executor to avoid blocking the main thread
                try:
                    loop = asyncio.get_running_loop()
                    f = loop.run_in_executor(None, self.cloud_logger.commit)
                    f.add_done_callback(self.reset_submitting)
                except Exception as e:
                    self.std_logger.debug(f"Failed to commit logs asynchronously, committing synchronously: {e}")
                    self.cloud_logger.commit()
                    self._submitting = False
    
    def reset_submitting(self, future: asyncio.Future):
        try:
            future.result()
        except Exception:
            self.std_logger.warning("Failed to submit log to Google Cloud")
        finally:
            self._submitting = False
    
    def info(self, message, **kwargs):
        self.log(message, severity=logging.INFO, **kwargs)

    def warning(self, message, **kwargs):
        self.log(message, severity=logging.WARNING, **kwargs)

    def error(self, message, **kwargs):
        self.log(message, severity=logging.ERROR, **kwargs)

    def critical(self, message, **kwargs):
        self.log(message, severity=logging.CRITICAL, **kwargs)

    def debug(self, message, **kwargs):
        self.log(message, severity=logging.DEBUG, **kwargs)
    
    def exception(self, message, **kwargs):
        self.log(message, severity=logging.ERROR, exc_info=True, **kwargs)


logger = CoplayLogger()
