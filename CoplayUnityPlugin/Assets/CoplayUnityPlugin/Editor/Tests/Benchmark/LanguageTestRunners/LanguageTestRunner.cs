using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Coplay.Common;

namespace Coplay.Tests.Benchmark.LanguageTestRunners
{
    public abstract class LanguageTestRunner
    {
        protected const int TestTimeoutSeconds = 180; // 3 minutes timeout for tests
        
        public abstract string Language { get; }
        public abstract Task<bool> IsLanguageAvailable();
        public abstract Task<TestExecutionResult> RunTests(BenchmarkExercise exercise, string workingDirectory);
        
        protected async Task<ProcessResult> ExecuteCommand(string fileName, string arguments, string workingDirectory, int timeoutSeconds = TestTimeoutSeconds, bool logErrors = true)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    WorkingDirectory = workingDirectory,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (var process = Process.Start(processInfo))
                {
                    if (process == null)
                    {
                        return new ProcessResult
                        {
                            Success = false,
                            ExitCode = -1,
                            Output = "",
                            Error = "Failed to start process"
                        };
                    }

                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();
                    
                    try
                    {

                        if (!process.WaitForExit(timeoutSeconds * 1000))
                        {
                            process.Kill();
                            // Give it a moment to clean up
                            await Task.Delay(1000);

                            return new ProcessResult
                            {
                                Success = false,
                                ExitCode = -1,
                                Output = "",
                                Error = $"Process timed out after {timeoutSeconds} seconds",
                                TimedOut = true
                            };
                        }
                        
                        // Process completed normally - read output
                        string output = "";
                        string error = "";
                        
                        try
                        {
                            output = await outputTask;
                            error = await errorTask;
                        }
                        catch (Exception ex)
                        {
                            CoplayLogger.LogWarning($"Failed to read process output: {ex.Message}");
                            error = $"Failed to read process output: {ex.Message}";
                        }
                        
                        return new ProcessResult
                        {
                            Success = process.ExitCode == 0,
                            ExitCode = process.ExitCode,
                            Output = output,
                            Error = error,
                            TimedOut = false
                        };
                    }
                    finally
                    {
                        // Ensure tasks are completed to avoid deadlocks
                        try
                        {
                            await outputTask;
                            await errorTask;
                        }
                        catch (Exception ex)
                        {
                            CoplayLogger.LogWarning($"Failed to complete stream reading tasks: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (logErrors)
                {
                    CoplayLogger.LogError($"Exception executing command '{fileName} {arguments}': {ex.Message}", ex);
                }
                return new ProcessResult
                {
                    Success = false,
                    ExitCode = -1,
                    Output = "",
                    Error = ex.Message
                };
            }
        }
        
        protected virtual void PrepareTestEnvironment(BenchmarkExercise exercise, string exerciseDirectory)
        {
            // Override in derived classes to prepare language-specific test environment
            // This method can be used to install dependencies, set up virtual environments, etc.
            // Since we're running tests in the original exercise folder, no file copying is needed
        }
        
        protected string CleanTestOutput(string output, string workingDirectory)
        {
            if (string.IsNullOrEmpty(output))
                return output;
            
            // Remove timing information to avoid randomizing responses
            output = System.Text.RegularExpressions.Regex.Replace(output, @"\bin \d+\.\d+s\b", "");
            output = System.Text.RegularExpressions.Regex.Replace(output, @"\d+\.\d+ms", "");
            output = System.Text.RegularExpressions.Regex.Replace(output, @"\d+ms", "");
            
            // Replace absolute paths with relative paths
            output = output.Replace(workingDirectory, Path.GetFileName(workingDirectory));
            
            return output;
        }
    }
    
    public class ProcessResult
    {
        public bool Success { get; set; }
        public int ExitCode { get; set; }
        public string Output { get; set; }
        public string Error { get; set; }
        public bool TimedOut { get; set; }
    }
    
    public class TestExecutionResult
    {
        public bool Success { get; set; }
        public string Output { get; set; }
        public string Error { get; set; }
        public bool TimedOut { get; set; }
        public int SyntaxErrors { get; set; }
        public int CompilationErrors { get; set; }
        public TimeSpan Duration { get; set; }
        
        public TestExecutionResult()
        {
            Success = false;
            Output = "";
            Error = "";
            TimedOut = false;
            SyntaxErrors = 0;
            CompilationErrors = 0;
            Duration = TimeSpan.Zero;
        }
    }
}
