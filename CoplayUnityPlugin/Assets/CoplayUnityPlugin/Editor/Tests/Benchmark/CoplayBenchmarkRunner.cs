using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Coplay.Common;
using Coplay.Controllers.Assistants;
using Coplay.Controllers.Functions.Implementations;
using Coplay.Tests.Benchmark.LanguageTestRunners;
using Coplay.Models.Assistants;
using Coplay.Models.Configuration;
using Coplay.Models;
using Coplay.Services.Chat;
using Coplay.Controllers.Systems;
using UnityEngine;
using UnityEditor;
using System.Reactive.Linq;
using System.Threading;
using System.Diagnostics;

namespace Coplay.Tests.Benchmark
{
    public class CoplayBenchmarkRunner
    {
        private readonly BenchmarkExerciseManager _exerciseManager;
        private readonly AssistantController _assistantController;
        private readonly FileFunctions _fileFunctions;
        private readonly IChatThreads _chatThreads;
        private readonly CoplayStateModel _stateModel;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly Dictionary<string, LanguageTestRunner> _testRunners;
        private readonly string _polyglotRepoPath;
        
        public CoplayBenchmarkRunner(
            BenchmarkExerciseManager exerciseManager,
            AssistantController assistantController,
            FileFunctions fileFunctions,
            IChatThreads chatThreads,
            CoplayStateModel stateModel,
            AssistantConfiguration assistantConfiguration)
        {
            _exerciseManager = exerciseManager;
            _assistantController = assistantController;
            _fileFunctions = fileFunctions;
            _chatThreads = chatThreads;
            _stateModel = stateModel;
            _assistantConfiguration = assistantConfiguration;
            
            // Get the Unity project root directory (parent of Assets folder)
            var projectRoot = Directory.GetParent(Application.dataPath).FullName;
            _polyglotRepoPath = Path.Combine(projectRoot, "Assets", BenchmarkExerciseManager.BenchmarkDataDir, "polyglot-benchmark");
            
            // Initialize test runners
            _testRunners = new Dictionary<string, LanguageTestRunner>
            {
                { "python", new PythonTestRunner() },
                { "javascript", new JavaScriptTestRunner() },
                { "rust", new RustTestRunner() },
                { "csharp", new CSharpTestRunner() }
            };
        }
        
        public async Task<BenchmarkSession> RunFullBenchmark(string model, int maxExercises = -1, List<string> specificLanguages = null, CancellationToken cancellationToken = default)
        {
            var session = new BenchmarkSession
            {
                Model = model
            };
            
            try
            {
                CoplayLogger.Log($"Starting benchmark session for model: {model}");
                
                // Ensure polyglot benchmark exists
                var repoReady = await _exerciseManager.EnsurePolyglotBenchmarkExists();
                if (!repoReady)
                {
                    throw new Exception("Failed to setup polyglot benchmark repository");
                }

                // Reset git repository to clean state before running exercises
                await ResetGitRepository();
                
                // Load exercises
                var allExercises = _exerciseManager.LoadExercises();
                
                // Filter by specific languages if requested
                if (specificLanguages != null && specificLanguages.Count > 0)
                {
                    allExercises = allExercises.Where(e => specificLanguages.Contains(e.Language, StringComparer.OrdinalIgnoreCase)).ToList();
                }
                
                // Limit number of exercises if specified
                if (maxExercises > 0 && allExercises.Count > maxExercises)
                {
                    allExercises = allExercises.Take(maxExercises).ToList();
                }
                
                session.TotalExercises = allExercises.Count;
                CoplayLogger.Log($"Running benchmark on {session.TotalExercises} exercises");
                
                // Check language availability
                var availableRunners = new Dictionary<string, LanguageTestRunner>();
                foreach (var kvp in _testRunners)
                {
                    var isAvailable = await kvp.Value.IsLanguageAvailable();
                    if (isAvailable)
                    {
                        availableRunners[kvp.Key] = kvp.Value;
                        CoplayLogger.Log($"Language {kvp.Key} is available");
                    }
                    else
                    {
                        CoplayLogger.LogWarning($"Language {kvp.Key} is not available - exercises will be skipped");
                    }
                }
                
                // Run exercises
                foreach (var exercise in allExercises)
                {
                    // Check for cancellation before each exercise
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    if (!availableRunners.ContainsKey(exercise.Language))
                    {
                        CoplayLogger.LogWarning($"Skipping {exercise} - language not available");
                        continue;
                    }
                    
                    try
                    {
                        var result = await RunSingleExercise(exercise, availableRunners[exercise.Language], model, cancellationToken);
                        session.Results.Add(result);
                        session.CompletedExercises++;
                        session.TotalCost += result.Cost;
                        session.TotalDuration += result.Duration;
                        
                        CoplayLogger.Log($"Completed exercise {session.CompletedExercises}/{session.TotalExercises}: {exercise} - Success: {result.Success}");
                    }
                    catch (OperationCanceledException)
                    {
                        CoplayLogger.Log($"Benchmark cancelled during exercise {exercise}");
                        throw; // Re-throw to propagate cancellation
                    }
                    catch (Exception ex)
                    {
                        CoplayLogger.LogError($"Failed to run exercise {exercise}: {ex.Message}", ex);
                        
                        var errorResult = new BenchmarkResult
                        {
                            ExerciseName = exercise.Name,
                            Language = exercise.Language,
                            Model = model,
                            Success = false,
                            ErrorMessage = ex.Message
                        };
                        
                        session.Results.Add(errorResult);
                        session.CompletedExercises++;
                    }
                }
                
                session.Complete();
                CoplayLogger.Log($"Benchmark session completed. Success rate: {session.SuccessRate:P2}, Total cost: ${session.TotalCost:F4}");
                
                return session;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Benchmark session failed: {ex.Message}", ex);
                session.Complete();
                throw;
            }
        }
        
        private async Task<BenchmarkResult> RunSingleExercise(BenchmarkExercise exercise, LanguageTestRunner testRunner, string model, CancellationToken cancellationToken = default)
        {
            var result = new BenchmarkResult
            {
                ExerciseName = exercise.Name,
                Language = exercise.Language,
                Model = model
            };
            
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Check for cancellation at the start of exercise execution
                cancellationToken.ThrowIfCancellationRequested();
                
                // Build prompt for the AI to work directly in the original exercise folder
                var prompt = BuildExercisePrompt(exercise, exercise.ExercisePath);
                
                // Create a new chat thread for this exercise to ensure clean state
                await _chatThreads.CreateThread();
                CoplayLogger.Log($"Created new chat thread for exercise {exercise.Name}");
                
                // Check for cancellation after thread creation
                cancellationToken.ThrowIfCancellationRequested();
                
                // Set the model for this exercise
                var availableModels = AIModelExtensions.GetAllModelStrings();
                if (!availableModels.Contains(model))
                {
                    throw new Exception($"Model '{model}' is not available. Available models: {string.Join(", ", availableModels)}");
                }
                
                CoplayLogger.Log($"Setting model to '{model}' for exercise {exercise.Name}");
                _assistantConfiguration.Model = model;
                _assistantConfiguration.Save();
                
                // Send to LLM and track function usage
                var functionCallsUsed = new List<string>();

                var coplayWindow = EditorWindow.GetWindow<Coplay>();
                if (coplayWindow == null)
                {
                    throw new Exception("Could not get Coplay window. Make sure Coplay is open in the Unity Editor.");
                }
                
                // Check for cancellation before submitting prompt
                cancellationToken.ThrowIfCancellationRequested();
                
                // Submit the prompt to Coplay
                await coplayWindow.ManualNormalModeMessageSubmission(prompt);
                CoplayLogger.Log($"Submitted prompt to Coplay for exercise {exercise.Name}");
                
                // Wait for Coplay to process the task using state monitoring
                await WaitForCoplayTaskCompletion(cancellationToken);
                CoplayLogger.Log($"Coplay task completed for exercise {exercise.Name}");
                
                // Check for cancellation before running tests
                cancellationToken.ThrowIfCancellationRequested();
                
                // Run tests to validate the solution in the original exercise folder
                var testResult = await testRunner.RunTests(exercise, exercise.ExercisePath);
                
                result.Success = testResult.Success;
                result.TestOutcomes.Add(testResult.Success);
                result.Duration = (DateTime.UtcNow - startTime).TotalSeconds;
                result.SyntaxErrors = testResult.SyntaxErrors;
                result.CompilationErrors = testResult.CompilationErrors;
                result.FunctionsUsed = functionCallsUsed;
                
                if (testResult.TimedOut)
                {
                    result.TestTimeouts = 1;
                }
                
                if (!string.IsNullOrEmpty(testResult.Error))
                {
                    result.ErrorMessage = testResult.Error;
                    result.ErrorOutputs = 1;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = (DateTime.UtcNow - startTime).TotalSeconds;
                CoplayLogger.LogError($"Exception in RunSingleExercise for {exercise}: {ex.Message}", ex);
                return result;
            }
            finally
            {
                // No cleanup needed since git reset handles restoration automatically
                // The repository will be reset before the next exercise runs
            }
        }
        
        private async Task ResetGitRepository()
        {
            try
            {
                if (!Directory.Exists(_polyglotRepoPath))
                {
                    CoplayLogger.LogError($"Polyglot repository path does not exist: {_polyglotRepoPath}");
                    throw new Exception($"Polyglot repository not found at: {_polyglotRepoPath}");
                }
                
                var processInfo = new ProcessStartInfo
                {
                    FileName = "git",
                    Arguments = "reset --hard HEAD",
                    WorkingDirectory = _polyglotRepoPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (var process = Process.Start(processInfo))
                {
                    if (process == null)
                    {
                        CoplayLogger.LogError("Failed to start git reset process");
                        throw new Exception("Failed to start git reset process");
                    }
                    
                    // Create tasks to read output and error streams
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();
                    
                    try
                    {
                        // Wait for process to exit using Task.Run wrapper
                        await Task.Run(() => process.WaitForExit());
                        
                        var output = await outputTask;
                        var error = await errorTask;
                        
                        if (process.ExitCode == 0)
                        {
                            CoplayLogger.Log("Successfully reset git repository to clean state");
                            if (!string.IsNullOrEmpty(output))
                            {
                                CoplayLogger.Log($"Git reset output: {output}");
                            }
                        }
                        else
                        {
                            CoplayLogger.LogError($"Git reset failed with exit code {process.ExitCode}: {error}");
                            throw new Exception($"Git reset failed: {error}");
                        }
                    }
                    finally
                    {
                        // Ensure tasks are completed to avoid deadlocks
                        await outputTask;
                        await errorTask;
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Exception during git reset: {ex.Message}", ex);
                throw;
            }
        }
        
        private async Task WaitForCoplayTaskCompletion(CancellationToken cancellationToken = default)
        {
            const int timeoutSeconds = 180; // 3 minutes timeout
            var startTime = DateTime.UtcNow;
            
            // Wait for state to change from Initialized (indicating task has started)
            var taskStarted = false;
            while (!taskStarted && (DateTime.UtcNow - startTime).TotalSeconds < timeoutSeconds)
            {
                // Check for cancellation
                cancellationToken.ThrowIfCancellationRequested();
                
                if (_stateModel.CurrentState != CoplayState.Initialized)
                {
                    taskStarted = true;
                    CoplayLogger.Log($"Coplay task started, current state: {_stateModel.CurrentState}");
                    break;
                }
                await Task.Delay(100, cancellationToken); // Check every 100ms with cancellation support
            }
            
            if (!taskStarted)
            {
                throw new TimeoutException("Coplay task did not start within the timeout period");
            }
            
            // Now wait for state to return to Initialized (indicating task completion)
            var taskCompleted = false;
            while (!taskCompleted && (DateTime.UtcNow - startTime).TotalSeconds < timeoutSeconds)
            {
                // Check for cancellation
                cancellationToken.ThrowIfCancellationRequested();
                
                if (_stateModel.CurrentState == CoplayState.Initialized)
                {
                    taskCompleted = true;
                    CoplayLogger.Log("Coplay task completed, state returned to Initialized");
                    break;
                }
                await Task.Delay(100, cancellationToken); // Check every 100ms with cancellation support
            }
            
            if (!taskCompleted)
            {
                throw new TimeoutException("Coplay task did not complete within the timeout period");
            }
        }
        
        private string BuildExercisePrompt(BenchmarkExercise exercise, string workingDirectory)
        {
            var prompt = "";
            
            // Add introduction if available
            if (!string.IsNullOrEmpty(exercise.Introduction))
            {
                prompt += exercise.Introduction + "\n\n";
            }
            
            // Add instructions
            if (!string.IsNullOrEmpty(exercise.Instructions))
            {
                prompt += exercise.Instructions + "\n\n";
            }
            
            // List the files that need to be modified
            if (exercise.SolutionFiles.Count > 0)
            {
                prompt += "Files to modify:\n";
                foreach (var file in exercise.SolutionFiles)
                {
                    prompt += $"- {file}\n";
                }
                prompt += "\n";
            }
            
            // Add current file contents
            prompt += "Current file contents:\n\n";
            foreach (var solutionFile in exercise.SolutionFiles)
            {
                var filePath = Path.Combine(workingDirectory, solutionFile);
                if (File.Exists(filePath))
                {
                    var content = File.ReadAllText(filePath);
                    prompt += $"File: {solutionFile}\n```{exercise.Language}\n{content}\n```\n\n";
                }
            }
            
            prompt += "Please implement the solution by modifying the appropriate files using the available file functions. Use replace_in_file tool call to modify source files. No other actions are needed on verifying implementation.\n";
            prompt += "Do not run any tests after implementation, tests are executed separately by test framework.\n";
            prompt += $"Search file to modify under `Assets/{BenchmarkExerciseManager.BenchmarkDataDir}/polyglot-benchmark/{exercise.Language}/exercises/practice/{exercise.Name}`";
            
            return prompt;
        }
    }
}
