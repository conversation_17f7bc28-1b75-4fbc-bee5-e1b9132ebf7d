using System;
using Newtonsoft.Json;

namespace Coplay.Models.Generation
{
    /// <summary>
    /// Asset generation status values
    /// </summary>
    public static class AssetGenerationStatusValues
    {
        public const string Pending = "pending";
        public const string Processing = "processing";
        public const string Completed = "completed";
        public const string Failed = "failed";
        public const string Cancelled = "cancelled";
    }

    /// <summary>
    /// Asset generation providers
    /// </summary>
    public enum AssetGenerationProviders
    {
        Meshy4,
        Meshy5,
        Hunyuan3D21
    }

    /// <summary>
    /// Asset generation request model - handles both 3D model generation and texture generation
    /// </summary>
    [Serializable]
    public class AssetGenerationRequest
    {
        // Common fields
        [JsonProperty("provider")]
        public string Provider { get; set; } = "Meshy4";

        [JsonProperty("model_url")]
        public string ModelUrl { get; set; }

        // FAL (image-to-3D) specific fields
        [JsonProperty("input_image_url")]
        public string InputImageUrl { get; set; }

        [JsonProperty("seed")]
        public int? Seed { get; set; }

        [JsonProperty("num_inference_steps")]
        public int NumInferenceSteps { get; set; } = 50;

        [JsonProperty("guidance_scale")]
        public float GuidanceScale { get; set; } = 7.5f;

        [JsonProperty("octree_resolution")]
        public int OctreeResolution { get; set; } = 256;

        [JsonProperty("textured_mesh")]
        public bool TexturedMesh { get; set; } = false;

        // Meshy (text-to-3D) specific fields
        [JsonProperty("prompt")]
        public string Prompt { get; set; }

        [JsonProperty("art_style")]
        public string ArtStyle { get; set; } = "realistic";

        [JsonProperty("negative_prompt")]
        public string NegativePrompt { get; set; } = "low quality, low resolution, low poly, ugly";

        [JsonProperty("enable_refinement")]
        public bool EnableRefinement { get; set; } = true;

        // Meshy image-to-3D specific fields
        [JsonProperty("ai_model")]
        public string AiModel { get; set; } = "meshy-4";

        [JsonProperty("topology")]
        public string Topology { get; set; } = "triangle";

        [JsonProperty("target_polycount")]
        public int TargetPolycount { get; set; } = 30000;

        [JsonProperty("symmetry_mode")]
        public string SymmetryMode { get; set; } = "auto";

        [JsonProperty("should_remesh")]
        public bool ShouldRemesh { get; set; } = true;

        [JsonProperty("should_texture")]
        public bool ShouldTexture { get; set; } = true;

        [JsonProperty("enable_pbr")]
        public bool EnablePbr { get; set; } = false;

        [JsonProperty("texture_prompt")]
        public string TexturePrompt { get; set; }

        [JsonProperty("texture_image_url")]
        public string TextureImageUrl { get; set; }

        [JsonProperty("moderation")]
        public bool Moderation { get; set; } = false;
    }

    [Serializable]
    public class AssetGenerationStatus
    {
        [JsonProperty("job_id")]
        public string JobId { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("result")]
        public AssetGenerationResult Result { get; set; }

        [JsonProperty("error")]
        public string Error { get; set; }

        [JsonProperty("progress")]
        public int? Progress { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

    /// <summary>
    /// Asset generation result - handles both 3D model URLs and texture URLs
    /// </summary>
    [Serializable]
    public class AssetGenerationResult
    {
        // Common URLs for 3D models
        [JsonProperty("model_glb_url")]
        public string ModelGlbUrl { get; set; }

        [JsonProperty("thumbnail_url")]
        public string ThumbnailUrl { get; set; }

        [JsonProperty("provider")]
        public string Provider { get; set; }

        [JsonProperty("preview_task_id")]
        public string PreviewTaskId { get; set; }

        // Texture URLs for texture generation
        [JsonProperty("texture_url")]
        public string TextureUrl { get; set; }
    }

    [Serializable]
    public class AssetGenerationJobResponse
    {
        [JsonProperty("job_id")]
        public string JobId { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("progress")]
        public int Progress { get; set; }
    }

    /// <summary>
    /// Meshy art style values
    /// </summary>
    public static class MeshyArtStyles
    {
        public const string Realistic = "realistic";
        public const string Sculpture = "sculpture";
    }

    /// <summary>
    /// Meshy AI model values
    /// </summary>
    public static class MeshyAiModels
    {
        public const string Meshy4 = "meshy-4";
        public const string Meshy5 = "meshy-5";
    }

    /// <summary>
    /// Meshy topology values
    /// </summary>
    public static class MeshyTopologies
    {
        public const string Triangle = "triangle";
        public const string Quad = "quad";
    }

    /// <summary>
    /// Meshy symmetry mode values
    /// </summary>
    public static class MeshySymmetryModes
    {
        public const string Off = "off";
        public const string Auto = "auto";
        public const string On = "on";
    }
} 