using System;
using System.Linq;
using System.Threading.Tasks;
using UnityEditor;
using Coplay.Common;
using Coplay.Controllers.Systems;
using Coplay.Models.Orchestrator;
using Coplay.Services.Chat;
using Coplay.Models.Configuration;
using Coplay.Models.Assistants;
using Coplay.Services;
using System.IO;
using Newtonsoft.Json;
using Coplay.Models;
using System.Reactive.Disposables;

namespace Coplay.Controllers.Orchestrator
{
    public class OrchestratorProcessor : IDisposable
    {
        private readonly CoplayStateModel _stateModel;
        private readonly IChatThreads _chatThreads;
        private readonly AssistantConfiguration _assistantConfiguration;
        private readonly IApplicationPaths _applicationPaths;
        private readonly ChatHistoryRepository _chatHistory;
        private readonly StateController _stateManager;
        private readonly CompositeDisposable _disposables = new();
        private IDisposable _stateChangeSubscription;

        private OrchestratorModel _orchestratorSession;
        public bool IsProcessing => _orchestratorSession?.IsProcessing ?? false;

        public event Action<int, int> OnProgressUpdate; // current, total
        public event Action OnComplete;
        public event Action<string> OnPromptStarted; // prompt text
        public event Action<bool> OnProcessingStateChanged;

        public OrchestratorProcessor(
            CoplayStateModel stateModel,
            IChatThreads chatThreads,
            AssistantConfiguration assistantConfiguration,
            IApplicationPaths applicationPaths,
            ChatHistoryRepository chatHistory,
            StateController stateManager)
        {
            _stateModel = stateModel;
            _chatThreads = chatThreads;
            _assistantConfiguration = assistantConfiguration;
            _applicationPaths = applicationPaths;
            _chatHistory = chatHistory;
            _stateManager = stateManager;
            LoadSession();

            _chatThreads.ThreadLoadedObservable.Subscribe(RestoreStateChangeListenerAfterDomainReload).AddTo(_disposables);
        }

        public void Dispose()
        {
            // We cannot call StopSession here because this gets triggered on a unity domain reload.
            _disposables.Dispose();
            if (_orchestratorSession?.IsProcessing == true)
            {
                _stateChangeSubscription.Dispose();
            }
        }

        private void RestoreStateChangeListenerAfterDomainReload(CoplayThread thread)
        {
            // If some domain reload happens while executing a normal thread, we want to restore the state change listener.
            try
            {
                if (_assistantConfiguration.Mode != AssistantMode.Orchestrator) return;
                CoplayLogger.Log("before restarting the listener. isprocessing: " + _orchestratorSession?.IsProcessing);
                if (_orchestratorSession?.IsProcessing == true)
                {
                    _stateChangeSubscription?.Dispose();
                    _stateChangeSubscription = _stateModel.CurrentStateObservable.Subscribe(OnStateChanged);

                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Error restoring Orchestrator state change listener after domain reload", ex);
            }
        }

        public void StartSession()
        {
            if (_orchestratorSession?.IsProcessing == true)
            {
                CoplayLogger.LogWarning("Orchestrator session already in progress");
            }

            _orchestratorSession = new OrchestratorModel
            {
                IsProcessing = true
            };
            OnProcessingStateChanged?.Invoke(true);

            SaveSession();

            _stateChangeSubscription?.Dispose();
            _stateChangeSubscription = _stateModel.CurrentStateObservable.Subscribe(OnStateChanged);

            // We only pass the plan file on the very first message, so if the user changes it inbetween, that will not be picked up.
            EditorWindow.GetWindow<Coplay>().OnMessageSubmittedFromChatView("Starting Orchestrator session.");
            _orchestratorSession.CurrentThreadId = _chatThreads.CurrentThread.id; // We need to ensure that we get the thread_id of the orchestrator session and not the first normal thread.
            _chatThreads.CurrentThread.is_orchestrator_thread = true;
            SaveSession();
        }

        public void StopSession()
        {
            if (_orchestratorSession?.IsProcessing == true)
            {
                CoplayLogger.Log("Stopping Orchestrator session");
                _orchestratorSession.IsProcessing = false;

                _stateChangeSubscription.Dispose();
                _stateChangeSubscription = null;

                SaveSession();
                OnComplete?.Invoke();
                OnProcessingStateChanged?.Invoke(false);
            }
        }

        private async void OnStateChanged(CoplayState newState)
        {
            if (_orchestratorSession is not { IsProcessing: true })
            {
                return;
            }

            try
            {
                switch (newState)
                {
                    // Check if we've returned to the Initialized state after a child thread finished.
                    case CoplayState.Initialized when _orchestratorSession.HasChildThreadStarted && !IsCurrentThreadOrchestrator():
                        _orchestratorSession.HasChildThreadStarted = false;
                        await SubmitTaskResultToOrchestratorThread();
                        break;
                    // Check if we've returned to the Initialized state in the orchestrator thread.
                    case CoplayState.Initialized when !_orchestratorSession.HasChildThreadStarted && IsCurrentThreadOrchestrator():
                        if (!HasRemainingPlannedTasks())
                        {
                            StopSession();
                        }
                        break;
                    case CoplayState.WaitingForAI when !IsCurrentThreadOrchestrator():
                        _orchestratorSession.HasChildThreadStarted = true;
                        break;
                }
            }
            catch (Exception e)
            {
                CoplayLogger.LogError("Error handling state change in orchestrator processor.", e);
            }
        }

        private bool HasRemainingPlannedTasks()
        {
            var lastMessage = _chatHistory.Messages.LastOrDefault();

            var isNoMessagesYet = lastMessage == null;
            if (isNoMessagesYet)
            {
                return true;
            }

            var isLastFromUserOrOther = lastMessage.ChatMessageRole != ChatMessageRole.Coplay;
            if (isLastFromUserOrOther)
            {
                return true;
            }

            // If Coplay's last message has function calls, there is remaining work.
            return lastMessage.FunctionCalls.Count > 0;
        }
        
        private bool IsCurrentThreadOrchestrator() => 
            _chatThreads?.CurrentThread?.id == (_orchestratorSession?.CurrentThreadId??"N/A");
        
        private async Task SubmitTaskResultToOrchestratorThread()
        {
            CoplayLogger.Log("Submitting task result to orchestrator thread");

            if (_orchestratorSession?.IsProcessing != true)
            {
                return;
            }
            
            SaveSession();
                
            var taskResult = GetTaskResult(); // First get the result for the current normal mode thread.
            await _chatThreads.LoadThreadMessages(_orchestratorSession.CurrentThreadId); // Then load in the orchestrator thread.
            UpdateFunctionCallResult(taskResult); // Update the function call result in the orchestrator thread.
            // Avoid issues with immediate state changes
            await Task.Yield();
            // Note, setting the state here might mess up the state history, so maybe we should clear the state history when switching away from this mode or something.
            _stateManager.SetState(CoplayState.WaitingForAI);
        }

        private void UpdateFunctionCallResult(string taskResult)
        {
            var lastFunctionCall = _chatHistory.Messages.LastOrDefault()?.FunctionCalls.LastOrDefault();
            // TODO: not sure how to handle errors or nulls here yet. Fingers crossed.
            CoplayLogger.Log($"Function result: {taskResult}");
            CoplayLogger.Log($"Last function call description: {lastFunctionCall.Description}");
            
            lastFunctionCall.Result = taskResult;
            lastFunctionCall.ResultType = taskResult is not string ? taskResult?.GetType().Name : null;
            // OnFunctionExecutionChange?.Invoke(); // TODO: for safety we should actually call this to ensure nothing here triggers auto-complete. But that's unlikely, so leaving this as a todo.

            lastFunctionCall.State = FunctionState.Executed;
            lastFunctionCall.HasExecuted = true; // For some reason, HasExecute is not set for functions that require recompilation. So for now we override it here.
            
            // Pass the updated function call to the UpdateMessage method so it can properly update it
            _chatHistory.UpdateMessage(lastFunctionCall.MessageId); // This is to trigger a UI refresh as the function result is updated. Not sure if this is needed.
        }

        private string GetTaskResult()
        {
            var lastMessage = _chatHistory.Messages.LastOrDefault();
            if (lastMessage?.ChatMessageRole != ChatMessageRole.Coplay)
            {
                return "Error: The last message of the thread is not from the Coplay assistant. Perhaps some error occurred in the thread.";
            }
            if (lastMessage.FunctionCalls.Count != 0)
            {
                return "Error: The last message of the thread contains function calls. Perhaps some error occurred in the thread.";
            }
            if (string.IsNullOrEmpty(lastMessage.Content))
            {
                return "Error: The last message of the thread is empty. Perhaps some error occurred in the thread.";
            }
            return lastMessage.Content;
        }

        private void SaveSession()
        {
            if (_orchestratorSession == null) return;
            try
            {
                var json = JsonConvert.SerializeObject(_orchestratorSession, Formatting.Indented);
                File.WriteAllText(_applicationPaths.OrchestratorSessionPath, json);
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to save Orchestrator session", ex);
            }
        }

        private void LoadSession()
        {
            try
            {
                if (File.Exists(_applicationPaths.OrchestratorSessionPath))
                {
                    var json = File.ReadAllText(_applicationPaths.OrchestratorSessionPath);
                    _orchestratorSession = JsonConvert.DeserializeObject<OrchestratorModel>(json);
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError("Failed to load Orchestrator session", ex);
            }
        }
    }
}
