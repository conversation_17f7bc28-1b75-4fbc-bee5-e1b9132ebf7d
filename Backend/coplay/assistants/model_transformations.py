"""
Model-specific transformations for tool calls.

This module contains transformation functions that need to be applied to tool calls
from specific AI models to ensure they conform to the expected schema format.
"""

from typing import Any


def normalize_zai_tool_call_args(args: dict[str, Any], tool_name: str) -> dict[str, Any]:
    """
    Normalize tool call arguments for Z.AI models.

    Z.AI models sometimes return arrays for parameters that should be comma-separated strings.
    This function converts arrays to comma-separated strings for specific parameters.

    Args:
        args: The tool call arguments dictionary
        tool_name: The name of the tool being called

    Returns:
        Normalized arguments dictionary
    """
    normalized_args = args.copy()

    # Parameters that should be comma-separated strings but Z.AI might return as arrays
    string_params = {
        "create_gameobject": ["position", "size"],
        "set_transform": ["position", "rotation", "scale"],
        "add_asset_to_scene": ["position"],
        "create_terrain": ["position"],
        "duplicate_gameobject": ["position"],
        "snap_to_bounds": ["offset"],
        "snap_to_vertex": ["referencePoint"],
        "snap_to_surface": ["raycastOriginDirection", "raycastOriginOffset"],
        "snap_to_grid": ["gridOrigin"],
    }

    if tool_name in string_params:
        for param_name in string_params[tool_name]:
            if param_name in normalized_args:
                value = normalized_args[param_name]
                # Convert array to comma-separated string
                if isinstance(value, list):
                    # Convert numbers to strings and join with commas
                    normalized_args[param_name] = ','.join(
                        str(x) for x in value)

    return normalized_args
