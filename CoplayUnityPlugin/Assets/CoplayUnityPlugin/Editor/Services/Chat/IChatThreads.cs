using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Reactive;
using Coplay.Models.Assistants;
using Coplay.Views.Components;

namespace Coplay.Services.Chat
{
    public interface IChatThreads : IDisposable
    {
        // Properties
        IReadOnlyList<CoplayThread> Threads { get; }
        bool IsLoadingThreads { get; }
        bool IsDeletingThread { get; }
        bool IsLoadingThreadMessages { get; }
        bool IsCreatingThread { get; }
        bool IsDoingSomething { get; }
        CoplayThread CurrentThread { get; }

        // Observables
        IObservable<IReadOnlyList<CoplayThread>> ThreadsLoadedObservable { get; }
        IObservable<CoplayThread> ThreadLoadedObservable { get; }
        IObservable<Unit> LoadingThreadObservable { get; }
        IObservable<Unit> CreatingThreadObservable { get; }
        IObservable<Unit> ThreadCreatedObservable { get; }

        // Methods
        Task LoadState();
        Task ClearState();
        Task CreateThread(CancellationToken cancellationToken = default, bool overrideToNormalMode = false);
        Task UpdateThread(string threadId, string name, AssistantMode mode, CancellationToken cancellationToken = default);
        Task DeleteThread(CoplayThread thread, CancellationToken cancellationToken = default);
        Task LoadThreads(string threadId = null, CancellationToken cancellationToken = default);
        Task LoadThreadMessages(string threadId, CancellationToken cancellationToken = default, bool avoidCache = false);
        Task CheckForThreadReview(ChatView chatView, CancellationToken cancellationToken = default);
    }
}
