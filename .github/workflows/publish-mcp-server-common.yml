name: Publish MCP Server (Common)

on:
  workflow_call:
    inputs:
      repository_url:
        description: 'PyPI repository URL'
        required: true
        type: string
      environment_name:
        description: 'GitHub environment name for deployment'
        required: true
        type: string
      working_directory:
        description: 'Working directory for the MCP server'
        required: false
        type: string
        default: 'CoplayMCPServer'
    secrets:
      PYPI_API_TOKEN:
        description: 'PyPI API token for authentication'
        required: true

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment_name }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
        
    - name: Verify project structure
      working-directory: ${{ inputs.working_directory }}
      run: |
        echo "Verifying project structure..."
        ls -la
        echo "Checking pyproject.toml..."
        cat pyproject.toml
        echo "Checking version in __init__.py..."
        cat coplay_mcp_server/__init__.py
        
    - name: Install build dependencies
      working-directory: ${{ inputs.working_directory }}
      run: |
        uv pip install --system build twine
        
    - name: Build package
      working-directory: ${{ inputs.working_directory }}
      run: |
        echo "Building package..."
        python -m build
        echo "Build completed. Contents of dist/:"
        ls -la dist/
        
    - name: Verify package
      working-directory: ${{ inputs.working_directory }}
      run: |
        echo "Verifying package contents..."
        python -m twine check dist/*
        
    - name: Publish to PyPI
      working-directory: ${{ inputs.working_directory }}
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
        TWINE_REPOSITORY_URL: ${{ inputs.repository_url }}
      run: |
        echo "Publishing to ${{ inputs.repository_url }}..."
        python -m twine upload dist/* --verbose
        
    - name: Verify installation
      working-directory: ${{ inputs.working_directory }}
      run: |
        echo "Waiting for package to be available..."
        sleep 30
        echo "Testing installation..."
        if [[ "${{ inputs.repository_url }}" == *"test.pypi.org"* ]]; then
          pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ coplay-mcp-server
        else
          pip install coplay-mcp-server
        fi
        echo "Testing CLI..."
        coplay-mcp-server --help
        echo "Testing Python import..."
        python -c "import coplay_mcp_server; print(f'Successfully imported version {coplay_mcp_server.__version__}')"
