"use client";

declare global {
	interface Window {
		dataLayer?: unknown[];
		gtag?: (...args: unknown[]) => void;
	}
}

export type GoogleAdsConversion = {
	sendTo: string; // e.g. AW-XXXX/YYYY
	value?: number;
	currency?: string;
	transactionId?: string;
};

export function trackGoogleAdsConversion(params: GoogleAdsConversion) {
	if (typeof window === "undefined") return;
	if (typeof window.gtag !== "function") return;
	window.gtag("event", "conversion", {
		send_to: params.sendTo,
		value: params.value,
		currency: params.currency,
		transaction_id: params.transactionId,
	});
}


