using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Coplay.Common.CoplayApi.Models;
using Coplay.Models.Assistants;
using Coplay.Models.Generation;
using Coplay.Models.ThreadReview;
using Coplay.Services.LogCollection;

namespace Coplay.Common.CoplayApi
{
    public interface ICoplayApiClient : IDisposable
    {
        /// <summary>
        /// Generates a scene from a description
        /// </summary>
        /// <param name="request">The scene generation request</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The scene generation response</returns>
        Task<SceneGenerationResponse> GenerateSceneAsync(SceneGenerationRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of a scene generation job
        /// </summary>
        /// <param name="jobId">The job ID to get status for</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The scene generation status response</returns>
        Task<SceneGenerationResponse> GetSceneGenerationStatusAsync(string jobId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks for updates by comparing the local package version with the remote version
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Version response containing update information, or null if check fails</returns>
        Task<VersionResponse> CheckForUpdates(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a device ID from the API and stores it in settings
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The device ID string</returns>
        Task<string> GetDeviceId(CancellationToken cancellationToken = default);

        /// <summary>
        /// Polls the API to check if authentication tokens are available for the device
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if tokens were successfully retrieved, false otherwise</returns>
        Task<bool> CheckForTokens(CancellationToken cancellationToken = default);

        /// <summary>
        /// Refreshes the access token using the refresh token
        /// </summary>
        /// <param name="requestTime">The time when the original request was made</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if token refresh was successful, false otherwise</returns>
        Task<bool> RefreshAccessToken(DateTime requestTime, CancellationToken cancellationToken = default);

        /// <summary>
        /// Submits a log to the Coplay API
        /// We retry on network errors 3 times
        /// At the moment this is most likely to occur when sending large prompts with a slower internet speed
        /// </summary>
        /// <param name="severity">The severity level of the log (e.g., "info", "warning", "error")</param>
        /// <param name="message">The log message content</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SubmitLog(string severity, string message, string threadId = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a hash for a specific context ID from the API
        /// </summary>
        /// <param name="contextId">The context ID to get the hash for</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The context hash, or null if not found</returns>
        Task<string> GetContextHash(string contextId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Syncs context data with the API
        /// </summary>
        /// <param name="contextId">The context ID to sync</param>
        /// <param name="hash">The hash of the context</param>
        /// <param name="content">The content to sync</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SyncContext(string contextId, string hash, string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends a message to the language model with streaming enabled
        /// </summary>
        /// <param name="promptRequest">The prompt request containing the message and other parameters</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A PromptResponse object containing the task ID and other information</returns>
        Task<PromptResponse> StreamSendMessageToLLM(PromptRequest promptRequest, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a streamed message by task ID
        /// </summary>
        /// <param name="taskId">The task ID returned by StreamSendMessageToLLM</param>
        /// <param name="lastUpdateId">The last update ID returned by GetStreamedMessage</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A PromptResponse object containing the message content and other information</returns>
        Task<PromptResponse> GetStreamedMessage(string taskId, int lastUpdateId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel task by task ID
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns></returns>
        Task CancelStream(string taskId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all thread IDs
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A list of thread IDs</returns>
        Task<List<CoplayThread>> GetThreads(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets messages for a specific thread
        /// </summary>
        /// <param name="threadId">The thread ID to get messages for</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A list of prompt responses, or null if retrieval fails</returns>
        Task<List<PromptResponse>> GetThreadMessages(string threadId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new assistant
        /// </summary>
        /// <param name="assistantMode">The mode for the assistant</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The thread ID for the new assistant</returns>
        Task<CoplayThread> CreateAssistant(AssistantMode assistantMode, CancellationToken cancellationToken = default);


        /// <summary>
        /// Updates a thread with a new name
        /// </summary>
        /// <param name="threadId">The thread ID to update</param>
        /// <param name="name">The new name for the thread</param>
        /// <param name="assistantMode">The mode for the assistant</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The updated thread</returns>   
        Task<CoplayThread> UpdateThread(string threadId, string name, AssistantMode assistantMode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a thread
        /// </summary>
        /// <param name="threadId">The thread ID to delete</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if deletion was successful, false otherwise</returns>
        Task<bool> DeleteThread(string threadId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Uploads an FBX file to the API
        /// </summary>
        /// <param name="localFilePath">The local path of the FBX file to upload</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if upload was successful, false otherwise</returns>
        Task<bool> UploadFbxFile(string localFilePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates an image using DALL-E through the Coplay API
        /// </summary>
        /// <param name="objectPrompt">The main prompt describing what to generate</param>
        /// <param name="stylePrompt">The style prompt describing the visual style</param>
        /// <param name="quality">The quality of the generated image (standard or hd)</param>
        /// <param name="size">The size of the generated image (e.g., "1024x1024")</param>
        /// <param name="style">The style of the generated image (natural or vivid)</param>
        /// <param name="provider">The image generation provider to use (e.g., "dall-e" or "imagen")</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The image generation response containing URLs to the generated images</returns>
        Task<ImageGenerationResponse> GenerateImage(string objectPrompt, string stylePrompt, string quality = "standard", string size = "1024x1024", string style = "natural", string provider = "dall-e", CancellationToken cancellationToken = default);

        /// <summary>
        /// Downloads dependencies from the Coplay backend
        /// </summary>
        /// <param name="name">The dependency name to download</param>
        /// <param name="version">The version of the dependency</param>
        /// <param name="os">The operating system (e.g., "windows", "macos", "linux")</param>
        /// <param name="architecture">The CPU architecture (e.g., "x64", "aarch64")</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A response containing signed URLs to download the dependencies</returns>
        Task<DependencyResponse> DownloadDependencyAsync(string name, string version, string os, string architecture, CancellationToken cancellationToken = default);

        /// <summary>
        /// Provides code autocompletion suggestions
        /// </summary>
        /// <param name="request">The autocomplete request</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The autocomplete response</returns>
        Task<AutocompleteResponse> GetAutocompleteAsync(AutocompleteRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Submits feedback for an agent run
        /// </summary>
        /// <param name="runId">The ID of the run to provide feedback for</param>
        /// <param name="key">The key for the feedback</param>
        /// <param name="score">The score for the feedback</param>
        /// <param name="comment">An optional comment for the feedback</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task<bool> SubmitFeedbackAsync(string runId, string key, float score, string comment = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates and saves the user's Anthropic API key
        /// </summary>
        /// <param name="apiKey">The Anthropic API key to validate and save</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if the key was validated and saved successfully, false otherwise</returns>
        Task<bool> ValidateAndSaveAnthropicKeyAsync(string apiKey, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the remaining USD credit balance for the current user
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The detailed credit balance breakdown for the user</returns>
        Task<CreditBalanceResponse> GetCreditBalanceAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the subscription information for the current user
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The subscription information for the user</returns>
        Task<SubscriptionResponse> GetSubscriptionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes the user's Anthropic API key
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if the key was deleted successfully, false otherwise</returns>
        Task<bool> DeleteAnthropicKeyAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts image generation as a background task using the new task-based pattern
        /// </summary>
        /// <param name="request">The image generation request</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The initial task response with task_id</returns>
        Task<ImageFunctionsResponse> StartImageGenerationAsync(object request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current status of an image generation task
        /// </summary>
        /// <param name="taskId">The task ID returned by StartImageGenerationAsync</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The current task status with partial or final results</returns>
        Task<ImageFunctionsResponse> GetImageGenerationStatusAsync(string taskId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancels an ongoing image generation task
        /// </summary>
        /// <param name="taskId">The task ID to cancel</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if the cancellation was successful</returns>
        Task<bool> CancelImageGenerationAsync(string taskId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Uploads a file to Supabase storage organized by thread ID
        /// </summary>
        /// <param name="filePath">The local file path to upload</param>
        /// <param name="threadId">The thread ID to organize the uploaded file</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The file upload response containing the signed download URL</returns>
        Task<FileUploadResponse> UploadFileAsync(string filePath, string threadId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts a new 3D asset generation job from an image using Meshy or Hunyuan 3D models
        /// </summary>
        /// <param name="request">The asset generation request</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The asset generation job response with job_id</returns>
        Task<AssetGenerationJobResponse> GenerateAssetAsync(AssetGenerationRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of an asset generation job
        /// </summary>
        /// <param name="jobId">The job ID to get status for</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The current status of the asset generation job</returns>
        Task<AssetGenerationStatus> GetAssetGenerationStatusAsync(string jobId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancels a running asset generation job
        /// </summary>
        /// <param name="jobId">The job ID to cancel</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>True if the cancellation was successful</returns>
        Task<bool> CancelAssetGenerationAsync(string jobId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a thread should be reviewed for this device
        /// </summary>
        /// <param name="threadId">Thread ID to review</param>
        /// <param name="customRules">Optional custom rules content to use for the review</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A response indicating whether the thread should be reviewed</returns>
        Task<ThreadShouldReviewResponse> ShouldReviewThreadAsync(string threadId, string customRules = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a specific thread review by review ID
        /// </summary>
        /// <param name="reviewId">The ID of the review to get</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The thread review response, or null if not found</returns>
        Task<ThreadReviewResponse> GetThreadReviewByIdAsync(string reviewId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Responds to a thread review (accept or reject)
        /// </summary>
        /// <param name="reviewId">The review ID to respond to</param>
        /// <param name="accepted">Whether the review suggestion was accepted</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The response status</returns>
        Task<ThreadReviewStatusResponse> RespondToThreadReviewAsync(string reviewId, bool accepted, CancellationToken cancellationToken = default);

        /// <summary>
        /// Tops up user credits using Stripe payment
        /// </summary>
        /// <param name="request">The top-up request containing amount and payment method</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The top-up response with success status and new balance</returns>
        Task<TopUpResponse> TopUpCreditsAsync(TopUpRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Uploads a log archive to the backend for debugging purposes
        /// </summary>
        /// <param name="request">The log archive upload request containing metadata</param>
        /// <param name="archiveBytes">The compressed log archive as a byte array</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task UploadLogArchiveAsync(LogArchiveUploadRequest request, byte[] archiveBytes, CancellationToken cancellationToken = default);

    }
}
