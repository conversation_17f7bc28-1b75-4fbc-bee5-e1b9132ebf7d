using UnityEngine;
using UnityEditor;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Coplay.Services;
using Microsoft.Extensions.Logging;
using Coplay.Services.Threading;
using System.Threading;
using System;
using Coplay.Services.UnityLogs;
using System.Collections.Concurrent;
using System.Reactive.Linq;

namespace Coplay.Controllers.Systems
{
    public class CompilationService : ICompilationService
    {
        private const string PossibleWrongPathMessage = "This file requires a Unity recompile, but the file was not created in the Assets folder. Proceed if this is intended.";

        private readonly IEditorStateService _editorStateService;
        private readonly IEditorThreadHelper _editorThreadHelper;
        private readonly IUnityLogsRepository _unityLogsRepository;

        public bool HasCompilationErrors => EditorUtility.scriptCompilationFailed;

        public CompilationService(
            IEditorStateService editorStateService,
            IEditorThreadHelper editorThreadHelper,
            IUnityLogsRepository unityLogsRepository)
        {
            _editorStateService = editorStateService;
            _editorThreadHelper = editorThreadHelper;
            _unityLogsRepository = unityLogsRepository;
        }

        public bool IsCompilationSkipped(string message) => message == PossibleWrongPathMessage;

        public async Task<string> CreateScriptWithCompilationCheck(string relativePath, string content)
        {
            await File.WriteAllTextAsync(relativePath, content);

            if (!relativePath.StartsWith("Assets" + Path.DirectorySeparatorChar) && !relativePath.StartsWith("Assets" + Path.AltDirectorySeparatorChar))
            {
                return PossibleWrongPathMessage;
            }

            bool isShader = IsShaderFile(relativePath);

            if (isShader)
            {
                AssetDatabase.Refresh(); // I think we need a refresh for the file to show in Unity.
                // Shaders don't cause a domain reload, so we check errors manually and directly return the result, unlike C# scripts.
                return await CheckShaderCompilationStatus(relativePath);
            }

            var compilationErrors = await CheckCompilationErrors(default);
            if (compilationErrors != null)
            {
                return compilationErrors;
            }

            // In case there are no compilation errors - this point is not reachable, since the domain reload starts and unloads our dll from memory.
            throw new InvalidOperationException("This code should be not reachable");
        }

        public async Task<string> CheckCompilationErrors(CancellationToken cancellationToken)
        {
            var errorMessages = new ConcurrentBag<UnityLogEntry>();
            using var errorMessagesSubscription = _unityLogsRepository
                .LogStream
                .Where(entry => entry.level == UnityLogLevel.Error)
                .Subscribe(entry => errorMessages.Add(entry));

            AssetDatabase.Refresh();
            await WaitForScriptCompilation(cancellationToken);

            if (HasCompilationErrors)
            {
                return $"Error: there are compilation errors:\n" + string.Join("\n", errorMessages.Select(x => x.message));
            }

            return null;
        }

        public async Task WaitForScriptCompilation(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var isCompilingOrUpdating = await _editorThreadHelper.ExecuteOnEditorThreadAsync(() => _editorStateService.IsCompiling || _editorStateService.IsUpdating);
                if (!isCompilingOrUpdating)
                    break;

                await Task.Delay(TimeSpan.FromMilliseconds(200));
            }
        }

        private bool IsShaderFile(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".shader" || extension == ".compute" || extension == ".cginc" || extension == ".hlsl";
        }

        private async Task<string> CheckShaderCompilationStatus(string scriptPath)
        {
            const int maxAttempts = 4; // 4 * 500ms = 2 seconds
            int attempts = 0;
            bool isCheckingShaderCompilation = true;
            Shader currentShader = null;

            while (attempts < maxAttempts)
            {
                // Wait for any ongoing compilation to finish and for the shader to be loaded
                if (!ShaderUtil.anythingCompiling && isCheckingShaderCompilation)
                {
                    // Try to load the shader asset
                    if (currentShader == null)
                    {
                        AssetDatabase.Refresh();
                        currentShader = AssetDatabase.LoadAssetAtPath<Shader>(scriptPath);
                    }

                    if (currentShader != null)
                    {
                        isCheckingShaderCompilation = false;

                        // Check for shader compilation errors
                        if (ShaderUtil.ShaderHasError(currentShader))
                        {
                            var shaderMessages = ShaderUtil.GetShaderMessages(currentShader);

                            if (shaderMessages.Length > 0)
                            {
                                string errorMessage = "Failed shader was preserved. Please fix the errors and/or delete the shader.";
                                string fullErrorMessage = $"Error: shader creation failed for '{scriptPath}'. {errorMessage}";

                                // Combine all shader messages (since ShaderHasError is true, these should be errors)
                                string combinedStackTrace = string.Join("\n\n", shaderMessages.Select(msg =>
                                    $"Line {msg.line}: {msg.message}\nFile: {msg.file}\nPlatform: {msg.platform}"));

                                return fullErrorMessage + "\n\n" + combinedStackTrace;
                            }
                        }

                        var successMsg = "Successfully created shader: " + scriptPath;
                        return successMsg;
                    }
                }

                await Task.Delay(500);
                attempts++;
            }

            // Timeout reached
            string timeoutMessage = $"Error: Shader creation check timed out for '{scriptPath}'.";
            return timeoutMessage;
        }
    }
}
