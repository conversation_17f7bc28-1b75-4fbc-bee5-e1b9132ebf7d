import { useEffect, useState } from "react";
import { verifySubscriptionSession } from "@/services/subscriptionService";
import {
  type Subscription,
  type VerificationStatus,
} from "@/types/billing";

interface UseSubscriptionVerificationResult {
  loading: boolean;
  verificationStatus: VerificationStatus;
  subscription: Subscription | null;
  error: string | null;
}

export function useSubscriptionVerification(
  sessionId: string | null
): UseSubscriptionVerificationResult {
  const [loading, setLoading] = useState(true);
  const [verificationStatus, setVerificationStatus] =
    useState<VerificationStatus>("pending");
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const verifySession = async () => {
      if (!sessionId) {
        setVerificationStatus("error");
        setError("No session ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setVerificationStatus("pending");

        const result = await verifySubscriptionSession(sessionId);

        setVerificationStatus(result.verificationStatus);
        setSubscription(result.subscription);
        setError(result.error);
      } catch (err) {
        console.error("Error verifying session:", err);
        setVerificationStatus("error");
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred"
        );
        setSubscription(null);
      } finally {
        setLoading(false);
      }
    };

    verifySession();
  }, [sessionId]);

  return {
    loading,
    verificationStatus,
    subscription,
    error,
  };
}

// Re-export types for backward compatibility
export type { Subscription, VerificationStatus } from "@/types/billing";
