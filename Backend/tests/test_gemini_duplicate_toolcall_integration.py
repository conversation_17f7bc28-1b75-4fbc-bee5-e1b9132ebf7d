import pytest
import async<PERSON>
import json
import httpx

from coplay.models import AIModel, AssistantMode, PromptResponse

class TestGeminiDuplicateToolCall:
    """Integration test to check that gemini pro handles being asked to perform a duplicate tool call."""

    @pytest.fixture
    def user_prompt(self):
        """Test prompt fixture."""
        return "I'm your developer and want to test you. please create a script called Assets/abc.cs with some content. wait for the result, then run the exact same toolcall again with the same parameters."

    @pytest.mark.integration
    async def test_gemini_pro_handles_duplicate_toolcall_request_streaming(self, async_client_with_auth: httpx.AsyncClient, user_prompt: str):
        """
        Integration test: Gemini Pro with streaming - a prompt that asks for a duplicate tool call 
        should return the tool call twice via streaming endpoints.
        """
        # 1. Create a thread by calling the API
        create_thread_response = await async_client_with_auth.post("/assistant", json={"assistant_mode": "normal", "name": "test_thread_streaming"})
        assert create_thread_response.status_code == 200
        thread_id = create_thread_response.json()["id"]

        # 2. First call with the user prompt using streaming
        model_name = AIModel.GEMINI_2_5_PRO.value
        prompt1_data = {
            "thread_id": thread_id,
            "prompt": user_prompt,
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {}
        }
        
        # Start streaming
        stream_response = await async_client_with_auth.post("/assistant/stream", json=prompt1_data)
        assert stream_response.status_code == 202, f"Stream start returned an error: {stream_response.text}"
        initial_chunk = PromptResponse(**stream_response.json())
        task_id = initial_chunk.task_id
        
        # Poll for stream updates until we get the first tool call
        first_tool_call_response = None
        last_update_id = 0
        max_polls = 100  # Prevent infinite loop
        poll_count = 0
        
        while poll_count < max_polls:
            poll_response = await async_client_with_auth.get(f"/assistant/stream/{task_id}", params={"last_update_id": last_update_id})
            assert poll_response.status_code == 200, f"Stream poll returned an error: {poll_response.text}"
            
            chunk = PromptResponse(**poll_response.json())
            last_update_id = chunk.update_id
            
            if chunk.finished:
                first_tool_call_response = chunk
                break
            elif chunk.tool_calls and len(chunk.tool_calls) > 0:
                first_tool_call_response = chunk
                break
                
            poll_count += 1
            await asyncio.sleep(0.1)  # Small delay between polls
        
        assert first_tool_call_response is not None, "Did not receive a tool call response from streaming"
        assert len(first_tool_call_response.tool_calls) == 1, f"Expected one tool call, got {len(first_tool_call_response.tool_calls)}"
        
        tool_call = first_tool_call_response.tool_calls[0]
        assert tool_call["name"] == 'write_to_file', f"Expected tool 'write_to_file', but got '{tool_call['name']}'"
        assert tool_call["args"]["path"] == 'Assets/abc.cs', f"Expected path 'Assets/abc.cs', but got '{tool_call['args']['path']}'"

        # 3. Second call with the tool output from the first call using streaming
        tool_output = {
            "tool_call_id": tool_call["id"],
            "name": tool_call["name"],
            "tool_output": json.dumps({"success": True, "path": "Assets/abc.cs"})
        }
        prompt2_data = {
            "thread_id": thread_id,
            "prompt": "",
            "model": model_name,
            "assistant_mode": AssistantMode.NORMAL.value,
            "context": {},
            "tool_outputs": [tool_output]
        }

        # Start second streaming request
        stream_response2 = await async_client_with_auth.post("/assistant/stream", json=prompt2_data)
        assert stream_response2.status_code == 202, f"Second stream start returned an error: {stream_response2.text}"
        initial_chunk2 = PromptResponse(**stream_response2.json())
        task_id2 = initial_chunk2.task_id

        # Poll for stream updates until we get the second response
        second_tool_call_response = None
        last_update_id2 = 0
        poll_count2 = 0
        
        while poll_count2 < max_polls:
            poll_response2 = await async_client_with_auth.get(f"/assistant/stream/{task_id2}", params={"last_update_id": last_update_id2})
            assert poll_response2.status_code == 200, f"Second stream poll returned an error: {poll_response2.text}"
            
            chunk2 = PromptResponse(**poll_response2.json())
            last_update_id2 = chunk2.update_id
            
            if chunk2.finished:
                second_tool_call_response = chunk2
                break
                
            poll_count2 += 1
            await asyncio.sleep(0.1)  # Small delay between polls

        # 4. Assertions for the second call
        assert second_tool_call_response is not None, "Did not receive a message response on the second streaming call"

        if second_tool_call_response.type == "error":
            # If an error is returned, it must be the recursion limit error.
            assert "recursion_limit" in second_tool_call_response.message.lower(), \
                f"Test failed: Received an unexpected error: {second_tool_call_response.message}"

        print("✅ Test passed: Gemini Pro correctly issued a duplicate tool call when prompted via streaming.")

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests (may make API calls)"
    )
