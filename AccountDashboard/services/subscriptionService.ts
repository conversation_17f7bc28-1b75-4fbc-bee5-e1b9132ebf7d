import { createClient } from "@/lib/supabase/client";
import { handleAuthError, fetchWithAuthErrorHandling } from "@/utils/auth-error-handler";
import toast from "react-hot-toast";
import { SupabaseClient } from "@supabase/supabase-js";
import {
  SubscriptionDetails,
  SubscriptionVerificationResult,
} from "@/types/billing";

/**
 * Fetch subscription details for the authenticated user
 */
export async function fetchSubscriptionDetails(): Promise<SubscriptionDetails> {
  const supabase = createClient();

  // Use getUser() for secure authentication verification
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    // Use the centralized auth error handler
    await handleAuthError(authError);
    throw new Error("Not authenticated");
  }

  // Make the API call using the fetch wrapper with auth error handling
  const response = await fetchWithAuthErrorHandling("/api/subscription");

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || "Failed to fetch subscription details");
  }

  const data: SubscriptionDetails = await response.json();
  return data;
}

/**
 * Server-side handler to fetch subscription data
 */
export async function getSubscriptionData(supabase: SupabaseClient) {
  try {
    // Use getUser() for secure authentication verification
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error("Authentication error:", authError);
      return { error: "User not authenticated" };
    }

    // Get the session for the access token (only after verifying user)
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError);
      return { error: "No session found" };
    }

    // Call the backend API directly
    const response = await fetch(
      `${process.env.BACKEND_API_URL}/auth/user/subscription`,
      {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Backend API error:", response.status, errorText);
      return { error: `Failed to fetch subscription data: ${response.status}` };
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return { error: "Failed to load subscription data" };
  }
}

/**
 * Helper function to handle session creation requests with common error handling
 */
const createSession = async (endpoint: string, sessionType: string) => {
  const response = await fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
  });

  const { url, error } = await response.json();
  if (error) {
    console.error(`Error creating ${sessionType} session:`, error);
    toast.error(`Failed to create ${sessionType} session`);
    return;
  }

  window.location.href = url;
};

/**
 * Handles subscription actions - either creating a checkout session for new subscriptions
 * or creating a billing session for existing subscriptions
 */
export const handleSubscriptionAction = async (
  hasActiveSubscription: boolean
) => {
  try {
    if (hasActiveSubscription) {
      await createSession("/api/create-billing-session", "billing");
    } else {
      await createSession("/api/create-checkout-session", "checkout");
    }
  } catch (error) {
    console.error("Network error during subscription action:", error);
    toast.error("Network error occurred. Please try again.");
    throw error; // Re-throw to maintain existing test expectations
  }
};

/**
 * Verify subscription session with Stripe
 */
export async function verifySubscriptionSession(sessionId: string): Promise<SubscriptionVerificationResult> {
  if (!sessionId) {
    throw new Error("No session ID provided");
  }

  const response = await fetch("/api/subscription/verify", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ session_id: sessionId }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.error || "Failed to verify subscription");
  }

  return {
    verificationStatus: data.verificationStatus,
    subscription: data.subscription,
    error: data.error || null,
  };
}
