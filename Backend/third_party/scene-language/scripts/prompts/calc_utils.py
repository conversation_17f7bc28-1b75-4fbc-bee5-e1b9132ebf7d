from type_utils import Shape, P
import numpy as np
from typing import Literal, <PERSON><PERSON>
from _shape_utils import compute_bbox, compute_bboxes
from math_utils import translation_matrix
from shape_utils import transform_shape, concat_shapes
import os
import subprocess
from pathlib import Path
import sys
import json

# __all__ = ["attach", "align_with_min", "align_with_center"]
__all__ = ["compute_shape_center", "compute_shape_min", "compute_shape_max", "compute_shape_sizes"]


def attach(direction: P, shapes: list[Shape]) -> list[Shape]:
    """
    Iteratively attach each input shape along the given direction, with the first shape attached to the second, the second to the third, and so on.
    The LAST shape is the base and will not be transformed.
    Returns a shape list with the input order after attachment.
    """
    return _attach(direction, shapes)[0]


def _attach(direction: P, shapes: list[Shape], atol: float = 1e-2) -> Tuple[list[Shape], bool]:
    dir = np.asarray(direction) / np.linalg.norm(direction)
    out_shapes = []
    s2 = None
    proj_lens = []
    for s1 in reversed(shapes):
        if len(s1) == 0:
            out_shapes.append(s1)
            continue
        if s2 is not None:
            d1 = np.max([max(b.max @ dir, b.min @ dir) for b in compute_bboxes(s1)])
            d2 = np.min([min(b.max @ dir, b.min @ dir) for b in compute_bboxes(s2)])
            proj_lens.append(d1 - d2)
            s1 = transform_shape(s1, translation_matrix((d2 - d1) * dir))  # move s1 to attach to s2
        s2 = s1
        out_shapes.append(s2)
    # print('attach assertion', proj_lens)
    return list(reversed(out_shapes)), 0 if len(proj_lens) == 0 else np.abs(proj_lens).max() > atol


def align_with_min(normal: P, shapes: list[Shape]) -> list[Shape]:
    """
    Align shapes such that their bottom lie on some shared plane perp to the given normal, if the normal points up.
    The LAST shape is the base and will not be transformed.
    Returns a shape list with the input order after alignment.
    """
    return _align('min', normal, shapes)[0]


def align_with_center(normal: P, shapes: list[Shape]) -> list[Shape]:
    """
    Align shapes such that their center lie on some shared plane perp to the given normal.
    The LAST shape is the base and will not be transformed.
    Returns a shape list with the input order after alignment.
    """
    return _align('center', normal, shapes)[0]


def _align(key: Literal['min', 'max', 'center'], normal: P, shapes: list[Shape], atol: float = 1e-2) -> Tuple[list[Shape], bool]:
    out_shapes = []
    normal = np.array(normal) / np.linalg.norm(normal)
    proj_lens = []
    for shape in shapes:
        box = compute_bbox(shape)
        proj_len = getattr(box, key) @ normal
        proj_lens.append(proj_len)
        trans = translation_matrix(-proj_len * normal)
        out_shapes.append(transform_shape(shape, trans))
    # set the last shape as the base  # TODO note this in docstring
    # out_shapes = [transform_shape(shape, -trans) for shape in out_shapes]
    # print('align assertion', np.asarray(proj_lens) - proj_lens[-1])
    return out_shapes, np.abs(np.asarray(proj_lens) - proj_lens[-1]).max() > atol


def compute_shape_center(shape: Shape) -> P:
    """
    Returns the shape center.
    """
    if isinstance(shape, dict):  # hack
        shape = [shape]
    return compute_bbox(shape).center


def compute_shape_min(shape: Shape) -> P:
    """
    Returns the min corner of the shape.
    """
    if isinstance(shape, dict):  # hack
        shape = [shape]
    box = compute_bbox(shape)
    # Get the last part of the filename if shape contains a filename
    # NOTE: this below is used. We run python in a subprocess and then look at the printed log outputs to read in values. Ask Jos or Joned.
    display_shape = shape
    if isinstance(shape, list) and shape and isinstance(shape[0], dict) and 'filename' in shape[0]:
        filename = shape[0]['filename']
        display_shape = os.path.basename(filename)
    print(f"Min of {display_shape}: {box.min}")
    return box.min


def compute_shape_max(shape: Shape) -> P:
    """
    Returns the max corner of the shape.
    """
    if isinstance(shape, dict):  # hack
        shape = [shape]
    box = compute_bbox(shape)
    
    # Get the last part of the filename if shape contains a filename
    # NOTE: this below is used. We run python in a subprocess and then look at the printed log outputs to read in values. Ask Jos or Joned.
    display_shape = shape
    if isinstance(shape, list) and shape and isinstance(shape[0], dict) and 'filename' in shape[0]:
        filename = shape[0]['filename']
        display_shape = os.path.basename(filename)
    
    print(f"Max of {display_shape}: {box.max}")
    return box.max


def compute_shape_sizes(shape: Shape) -> P:
    """
    Returns the shape sizes along x, y, and z axes.
    """
    if isinstance(shape, dict):  # hack
        shape = [shape]
    sizes = compute_bbox(shape).sizes
    # NOTE: this below is used. We run python in a subprocess and then look at the printed log outputs to read in values. Ask Jos or Joned.
    print("shape sizes: ")
    print(sizes) # This line is read by the subprocess and used as a return value
    return sizes

def compute_shape_size_wrapper(shape: Shape) -> P:
    """
    To run this function on our production server, we need to use a subprocess because we're running Mitsuba.
    """
    current_file = str(Path(__file__).resolve())
    
    # Convert numpy arrays to lists before JSON serialization
    def numpy_to_list(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: numpy_to_list(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [numpy_to_list(item) for item in obj]
        return obj
    
    # Serialize the shape to JSON string with numpy array handling
    shape_json = json.dumps(numpy_to_list(shape))

    prompts_dir = Path(__file__).parent
    scene_language_dir = prompts_dir.parent.parent
    print(f"Prompts directory: {prompts_dir}")
    print(f"Scene language directory: {scene_language_dir}")

    # Create environment dictionary for subprocess
    env = os.environ.copy()
    env.update({
        'ENGINE_MODE': 'exposed',
        'DEBUG': '0',
        'PYTHONPATH': f"{prompts_dir}:{scene_language_dir}:{env.get('PYTHONPATH', '')}"
    })

    result = subprocess.run(
        ['python', current_file, '--', shape_json],
        check=True,
        capture_output=True,
        text=True,
        shell=False,
        env=env
    )
    
    output_lines = result.stdout.split('\n')
    size_str = next((line.strip() for line in reversed(output_lines) 
                     if line.strip() and '[' in line), None)  # Look for line containing array
    
    if not size_str:
        raise RuntimeError("No size string received from subprocess")
    
    # Extract the numbers from the array string using string manipulation
    size_str = size_str.strip('[]')  # Remove brackets
    size = [float(x) for x in size_str.split()]  # Split on whitespace and convert to float
    
    return np.array(size, dtype=float)


if __name__ == "__main__":
    # Skip the first few Blender-specific arguments
    argv = sys.argv
    if "--" in argv:
        argv = argv[argv.index("--") + 1:]
    
    if len(argv) < 1:
        print("Please provide the shape JSON as an argument")
        sys.exit(1)
    
    # Deserialize the shape from JSON
    shape_json = argv[0]
    try:
        shape = json.loads(shape_json)
    except json.JSONDecodeError:
        print("Invalid shape JSON provided")
        sys.exit(1)
        
    print(f"Processing shape")
    compute_shape_sizes(shape)