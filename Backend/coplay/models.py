import asyncio
from datetime import datetime
from typing import Any, Literal, Optional
from typing_extensions import NotRequired
from enum import Enum
import json
 

from langgraph.graph import MessagesState
from langchain_core.messages import ToolCall, BaseMessage
from langchain_core.load import Serializable
from pydantic import BaseModel, Field, field_validator
from sqlalchemy import Column, Integer, String
from supabase_auth.types import User

class CoplaySerializable(Serializable):
    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

class VersionModel(BaseModel):
    version: str

class LogEntry(BaseModel):
    timestamp: datetime
    message: str
    severity: str = "INFO"
    unity_version: Optional[str] = None
    thread_id: Optional[str] = None

class SceneChange(BaseModel):
    added: list[dict]
    modified: list[dict]
    removed: list[dict]

class MerkleTree(BaseModel):
    path: str
    hash: str
    children: dict[str, 'MerkleTree']

class ChangeSet(BaseModel):
    changes: SceneChange
    tree: MerkleTree

class ToolOutput(CoplaySerializable):
    tool_call_id: str
    tool_output: str
    image_url: str | None = None

class AssistantMode(str, Enum):
    ONE_SHOT_SCENE_GEN = "one_shot_scene_gen"
    NORMAL = "normal"
    STEP_BY_STEP = "step_by_step"
    EXPERIMENTAL = "experimental"
    PIPELINE_RECORDING = "pipeline_recording"
    ORCHESTRATOR = "orchestrator"
    THREAD_REVIEW = "thread_review"

class AIModel(str, Enum):
    GEMINI_2_5_FLASH_LITE = "gemini-2-5-flash-lite"
    GEMINI_2_5_PRO = "gemini-2-5-pro"
    GEMINI_2_5_FLASH = "gemini-2-5-flash"
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet"
    CLAUDE_3_7_SONNET = "claude-3-7-sonnet"
    CLAUDE_4_SONNET = "claude-4-sonnet"
    CLAUDE_3_7_THINKING = "claude-3-7-thinking"
    CLAUDE_4_THINKING = "claude-4-thinking"
    GPT4O_MINI = "gpt-4o-mini"
    GPT4O = "gpt-4o"
    GPT41 = "gpt-4.1"
    GPT5 = "gpt-5"
    O3 = "o3"
    O4_MINI = "o4-mini"
    GROK4 = "grok-4"
    GROK_CODE_FAST_1 = "grok-code-fast-1"
    MERCURY_CODER_SMALL = "mercury-coder-small" 
    MERCURY_CODER_SMALL_STREAM = "mercury-coder-small-stream"
    KIMI_K2 = "kimi-k2"
    QWEN3_CODER = "qwen3-coder"
    META_LLAMA_3_3_70B = "llama-3-3-70b"
    Z_AI_GLM_4_5 = "z-ai-glm-4-5"
    GPT_OSS_120B = "gpt-oss-120b"

    @classmethod
    def get_model(cls, model_name: str):
        try:
            return cls(model_name)
        except ValueError:
            return None

class MessageType(str, Enum):
    HUMAN = "coplay_human_message"
    ERROR = "coplay_error_message"
    SCREENSHOT = "coplay_screenshot"
    CONTEXT_UPDATE = "context_update"

class AssistantThreadRequest(BaseModel):
    # TODO: we need to refactor automation mode to work with the new mode system.
    assistant_mode: AssistantMode
    name: str | None = None

class AssistantThreadUpdate(BaseModel):
    name: str | None = None
    assistant_mode: AssistantMode

class CoplayThread(BaseModel):
    id: str
    name: str | None = None
    assistant_mode: AssistantMode
    created_at: datetime
    messages: list[BaseMessage] = []
    turn: int = 1

class CoplayUsage(BaseModel):
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cache_read_tokens: int
    cache_write_tokens: int
    non_cache_tokens: int
    payable_tokens: int
    cost_usd: float
    remaining_tokens: int = 0  # Deprecated, TODO: remove when we feel good after the refactor
    credit_balance: float = 0.0  # New USD credit balance

class Prompt(CoplaySerializable):
    prompt: str
    context: dict | None = None
    tool_outputs: list[ToolOutput] | None = []
    thread_id: str | None = None
    assistant_mode: AssistantMode = AssistantMode.NORMAL
    model: AIModel | None = None
    images: list[str] | None = None  # List of image data URLs

    def get_system_details(self) -> dict:
        return json.loads(self.context.get("system_details", "{}"))

class PruneResult(BaseModel):
    messages_to_send: list[BaseMessage]
    updated_messages: list[BaseMessage]
    token_count: int

class CoplayToolCall(ToolCall):
    description: NotRequired[Optional[str]]
    output: NotRequired[Optional[str]]
    image_urls: NotRequired[Optional[list[str]]]

class PromptResponse(BaseModel):
    type: str
    cost: float | None = None
    update_id: int = 0
    thread_id: str
    tool_calls: list[CoplayToolCall] | None = []
    message: str | None = None
    image_urls: list[str] | None = None
    message_id: str | None = None
    task_id: str | None = None
    finished: bool = True

class SceneGenerationRequest(BaseModel):
    description: str
    depth: int = 5
    attached_fbx_files: list[str] = []
    model: str = "claude-3-7-sonnet"

class SceneGenerationStatus(BaseModel):
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    result: list[Any] | None = None  # List of json objects
    error: str | None = None
    current_iteration: int | None = None
    total_iterations: int | None = None
    # More detailed status about current iteration
    iteration_status: str | None = None

class UserAttachedModels(BaseModel):
    id: int
    path: str
    description: str
    size: str
    ply_path: str
    content_hash: str
    device_id: str
    local_ply_path: str | None = None

class ChatMessage(BaseModel):
    role: str
    content: str

class ImageAttachment(BaseModel):
    image_url: str
    aspect_ratio: float | None = None

class XamlGeneratorRequest(BaseModel):
    description: str
    model: AIModel | None = None
    image_attachment: ImageAttachment | None = None
    thread_id: str | None = None

class XamlGeneratorResponse(BaseModel):
    name: str = Field(description="The name of the UI element")
    xaml: str = Field(description="The XAML code for the UI element")
    description: str = Field(description="A description of the UI that was created. Do not mention the XAML language or any XAML elements.")
    update_existing_element: bool = Field(description="Whether to update an existing element or create a new one")
    image_urls: list[str] | None = Field(description="The URLs of the images to be used in the created UI element")

class CreateDeviceResponse(BaseModel):
    device_id: str

class GetDeviceResponse(BaseModel):
    access_token: str | None = None
    refresh_token: str | None = None

class AutocompleteRequest(BaseModel):
    """Request model for autocomplete endpoint"""
    prefix: str | None = Field(default=None, description="The text before the cursor")
    suffix: str | None = Field(default=None, description="The text after the cursor")
    language: str | None = Field(default=None, description="The programming language")
    file_path: str | None = Field(default=None, description="The file path")
    line: int | None = Field(default=None, description="The line number")
    character: int | None = Field(default=None, description="The character position")
    max_tokens: int | None = Field(default=None, description="Maximum number of tokens to generate")
    model: AIModel | None = Field(default=None, description="The model to use for completion")

class AutocompleteResponse(BaseModel):
    """Response model for autocomplete endpoint"""
    completion_text: str = Field(description="The generated completion text")
    model: AIModel = Field(description="The model used for completion")
    latency_ms: int = Field(description="The latency in milliseconds")

class ImageGenerationRequest(BaseModel):
    object_prompt: str
    style_prompt: str
    quality: Literal["standard", "hd"] = "standard"
    size: Literal["1024x1024", "1792x1024", "1024x1792"] = "1024x1024"
    style: Literal["natural", "vivid"] = "natural"

class ImageFunctionsRequest(BaseModel):
    is_edit: bool
    prompt: str
    size: Literal["64x64", "128x128", "256x256", "512x512", "512x256", "256x512", "128x64", "64x128", "256x128", "128x256", "1024x1024", "1536x1024", "1024x1536", "auto"] = "auto"
    quality: Literal["low", "medium", "high", "auto"] = "auto"
    format: Literal["png", "jpeg", "webp"] = "png"
    compression: int | None = None
    transparent_background: bool | None = None
    provider: Literal["gpt_image_1", "imagen"] = "gpt_image_1"
    attached_images: list[str] | None = None  # List of base64 encoded images
    use_attached_image: bool | None = False

class ImageFunctionsResponse(BaseModel):
    type: str  # "partial_image", "final_image", "error"
    task_id: str | None = None
    finished: bool = True
    count: int | None = None
    total: int | None = None
    image_data: str | None = None  # Base64 data URL
    result: str | None = None
    error: str | None = None
    chunk_id: int = 0  # Integer counter that increments with each update
    image_url: str | None = None
    width: int | None = None  # Final image width after cropping
    height: int | None = None  # Final image height after cropping

class ImageGenerationTask(BaseModel):
    cancel_scope: asyncio.Event
    _chunk: ImageFunctionsResponse = None
    chunk_id_counter: int = 0  # Counter for tracking chunk updates

    class Config:
        arbitrary_types_allowed = True
        # Allow private attributes to be set during initialization
        underscore_attrs_are_private = True

    def __init__(self, **data):
        super().__init__(**data)
        if 'chunk' in data:
            # Set the initial chunk without incrementing counter
            self._chunk = data['chunk']

    @property
    def chunk(self) -> ImageFunctionsResponse:
        return self._chunk

    @chunk.setter
    def chunk(self, value: ImageFunctionsResponse):
        # Increment counter and update chunk_id on the response
        self.chunk_id_counter += 1
        if value is not None:
            value.chunk_id = self.chunk_id_counter
        self._chunk = value


class CheckVersionResponse(BaseModel):
    update_needed: bool = Field(description="Whether an update is required for the Coplay package")
    update_available: bool = Field(description="Whether an update is available for the Coplay package")

class CoplayVersionInfo(BaseModel):
    coplay_version: str

class CoplayException(Exception):
    def __init__(self, message: str):
        super().__init__(message)
        self.message = message

class ContextTooLargeError(CoplayException):
    """Raised when context cannot be pruned to fit within the token limit."""
    pass

class AgentState(MessagesState):
    tool_calls: list[dict] | None
    tool_outputs: list[dict] | None
    device_id: str
    user: User
    scene_id: str
    prefab_id: str
    thread_id: str
    prompt: Prompt
    assistant_mode: AssistantMode
    current_model: AIModel
    messages_to_send: list[BaseMessage]
    current_input_tokens: int
    coplay_version: str | None = None

class FeedbackRequest(BaseModel):
    run_id: str = Field(description="The ID of the run to provide feedback for")
    key: str = Field(description="The key for the feedback")
    score: float = Field(description="The score for the feedback")
    comment: Optional[str] = Field(default=None, description="An optional comment for the feedback")

class AnthropicKeyRequest(BaseModel):
    api_key: str = Field(description="The Anthropic API key to validate and save")

class CoplayTask(BaseModel):
    cancel_scope: asyncio.Event = Field(exclude=True)
    update_id: int = 0
    chunk: PromptResponse

    class Config:
        arbitrary_types_allowed = True

class CoplayRun(BaseModel):
    usage: CoplayUsage | None = None
    user_id: str
    thread_id: str
    message_id: str
    prompt: str | None = None
    tool_calls: int = 0
    tool_outputs: int = 0
    model: AIModel
    chargeable: bool
    coplay_version: str | None = None

class CoplayToolOutput(BaseModel):
    tool_call_id: str
    output: str

class AssetGenerationProvider(str, Enum):
    MESHY4 = "Meshy4"
    MESHY5 = "Meshy5"
    HUNYUAN3D21 = "Hunyuan3D21"

class AssetGenerationRequest(BaseModel):
    # Common fields
    provider: AssetGenerationProvider = Field(default=AssetGenerationProvider.MESHY4, description="Generation provider: 'Meshy4' or 'Meshy5' for text-to-3D, image-to-3D. or texture generation, 'Hunyuan3D21' for image-to-3D")
    output_format: str = Field(default="glb", description="Output format: 'glb', 'fbx', 'obj'")
    
    # Model URL for texture generation
    model_url: str | None = Field(default=None, description="URL or data URI of the 3D model file for texture generation")
    
    # FAL (image-to-3D) specific fields
    input_image_url: str | None = Field(default=None, description="URL of image to use while generating the 3D model (required for FAL)")
    seed: int | None = Field(default=None, description="Seed for reproducible results")
    num_inference_steps: int = Field(default=50, description="Number of inference steps to perform")
    guidance_scale: float = Field(default=7.5, description="Guidance scale for the model")
    octree_resolution: int = Field(default=256, description="Octree resolution for the model")
    textured_mesh: bool = Field(default=False, description="If true, textured mesh will be generated at 3x cost")
    
    # Meshy (text-to-3D) specific fields  
    prompt: str | None = Field(default=None, description="Text prompt for 3D model generation (required for Meshy text-to-3D)")
    art_style: str = Field(default="realistic", description="Art style: 'realistic', 'cartoon', 'low-poly', 'sculpture', 'pbr'")
    negative_prompt: str = Field(default="low quality, low resolution, low poly, ugly", description="Negative prompt for generation")
    enable_refinement: bool = Field(default=True, description="Whether to automatically refine the preview model")
    
    # Meshy image-to-3D specific fields
    topology: str = Field(default="triangle", description="Mesh topology: 'quad', 'triangle'")
    target_polycount: int = Field(default=30000, description="Target polygon count (100-300000)")
    symmetry_mode: str = Field(default="auto", description="Symmetry mode: 'off', 'auto', 'on'")
    should_remesh: bool = Field(default=True, description="Whether to enable remesh phase")
    should_texture: bool = Field(default=True, description="Whether to generate textures")
    enable_pbr: bool = Field(default=False, description="Generate PBR maps (metallic, roughness, normal)")
    texture_prompt: str | None = Field(default=None, description="Text prompt to guide texturing process")
    texture_image_url: str | None = Field(default=None, description="Image to guide texturing process")
    moderation: bool = Field(default=False, description="Screen content for harmful material")

class AssetGenerationStatus(BaseModel):
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    result: dict | None = None
    error: str | None = None
    progress: int | None = None  # 0-100
    message: str | None = None  # Detailed status message

class AssetGenerationResult(BaseModel):
    # Common URLs for 3D models
    model_glb_url: str | None = None
    thumbnail_url: str | None = None
    provider: AssetGenerationProvider | None = None
    preview_task_id: str | None = None  # For Meshy refinement workflow
    
    # Texture URLs for texture generation
    texture_url: str | None = None

class AssetGenerationTaskStatus(BaseModel):
    """Specialized status class for asset generation tasks"""
    task_id: str
    status: str  # "pending", "processing", "completed", "failed", "cancelled"
    progress: int = 0  # 0-100
    message: str
    result: Optional[AssetGenerationResult] = None
    finished: bool = False

class AssetGenerationTask(BaseModel):
    """Specialized task class for asset generation"""
    cancel_scope: asyncio.Event
    status: AssetGenerationTaskStatus

    class Config:
        arbitrary_types_allowed = True

class FileUploadRequest(BaseModel):
    """Request model for file upload to Supabase storage"""
    thread_id: str = Field(description="Thread ID to organize the uploaded file")
    file_name: str = Field(description="Name of the file being uploaded")
    content_type: str = Field(default="application/octet-stream", description="MIME type of the file")

class FileUploadResponse(BaseModel):
    """Response model for file upload containing the signed download URL"""
    success: bool = Field(description="Whether the upload was successful")
    file_url: str | None = Field(default=None, description="Signed download URL for the uploaded file")
    file_path: str | None = Field(default=None, description="Storage path of the uploaded file")
    error: str | None = Field(default=None, description="Error message if upload failed")

class ToolDescription(BaseModel):
    tool_call_id: str
    tool_description: str

class ToolDescriptionOutput(BaseModel):
    tool_descriptions: list[ToolDescription]

class ToolCallOutcome(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    CANCELLED = "cancelled"
    MISSING = "missing"

class ToolCallResult(BaseModel):
    user_id: str
    thread_id: str
    message_id: str
    tool_call_id: str
    tool_name: str
    outcome: ToolCallOutcome
    error: str | None = None
    model: AIModel
    mode: AssistantMode
    system_details: dict | None = None
    coplay_version: str | None = None

class ThreadReviewRequest(BaseModel):
    previous_thread_id: str = Field(description="ID of the previous thread to review")
    new_thread_id: str = Field(description="ID of the new thread being created")

class ThreadReviewResponse(BaseModel):
    review_id: str = Field(description="Unique ID of the review")
    suggestion: str = Field(description="The suggested improvement for user rules")
    diff_content: str = Field(description="The diff content of the review")
    created_at: datetime = Field(description="When the review was created")
    status: str = Field(description="The status of the review: 'pending', 'accepted', 'rejected', 'notShownToUser'")

class ThreadReviewStatusRequest(BaseModel):
    accepted: bool = Field(description="Whether the user accepted the review suggestion")

class ThreadReviewCheckResponse(BaseModel):
    should_review: bool = Field(description="Whether a thread review should be started")
    review_id: Optional[str] = Field(description="ID of the review that was created (if should_review is True)")

class TopUpRequest(BaseModel):
    amount_usd: float = Field(description="Amount in USD to add to user's credit balance", gt=0, le=10000000)

class TopUpResponse(BaseModel):
    success: bool = Field(description="Whether the top-up was successful")
    new_balance: float | None = Field(default=None, description="New credit balance after top-up")
    transaction_id: str | None = Field(default=None, description="Stripe payment intent ID")
    error: str | None = Field(default=None, description="Error message if top-up failed")

class LogArchiveUploadRequest(BaseModel):
    """Request model for log archive upload"""
    # Note: device_id and user_email are sent via headers, not form data
    archive_file_name: str = Field(description="Name of the archive file")
    archive_size: int = Field(description="Size of the archive in bytes")
    timestamp: datetime = Field(description="Timestamp when logs were collected")
    unity_version: str = Field(description="Version of Unity used")
    platform: str = Field(description="Platform (Windows, macOS, Linux)")

class LogArchiveUploadResponse(BaseModel):
    """Response model for log archive upload"""
    success: bool = Field(description="Whether the upload was successful")
    archive_url: Optional[str] = Field(default=None, description="Signed URL to access the uploaded archive")
    archive_path: Optional[str] = Field(default=None, description="Path of the uploaded archive in storage")
    error: Optional[str] = Field(default=None, description="Error message if upload failed")
class CreditBalance(BaseModel):
    subscription_credits: float = Field(description="Amount of subscription credits")
    topup_credits: float = Field(description="Amount of top-up credits")
    total: float = Field(description="Total amount of credits")
