import re
import sys
from typing import Optional

class PromptViolationChecker:
    """Prompt violation checker to detect policy violations in user messages."""
    
    def __init__(self):
        # TODO: this does not capture any requests related to stealing our tool schema.
        # System-related patterns in multiple languages
        self.system_prompt_patterns = [
            # English variations - system prompt
            r'system\s*prompt',
            r'system[_-]prompt',
            r'systemprompt',
            r'system\s+prompt',
            r'sys\s*prompt',
            r'sys[_-]prompt',
            
            # English variations - system message
            r'system\s*message',
            r'system[_-]message',
            r'systemmessage',
            r'system\s+message',
            r'sys\s*message',
            r'sys[_-]message',
            
            # English variations - system instructions (both singular and plural)
            r'system\s*instructions?',
            r'system[_-]instructions?',
            r'systeminstructions?',
            r'system\s+instructions?',
            r'sys\s*instructions?',
            r'sys[_-]instructions?',
            
            # Mixed language patterns - English terms in other languages
            # These patterns catch English system-related terms regardless of surrounding language
            r'system\s+instruction',
            r'system\s*instruction',
            r'system[_-]instruction',
            r'systeminstruction',
            
            # "sys" equivalents for mixed language patterns
            r'sys\s+instruction',
            r'sys\s*instruction', 
            r'sys[_-]instruction',
            r'sysinstruction',
            
            # Chinese (Simplified and Traditional)
            r'系统提示',      # system prompt
            r'系統提示',      # system prompt (traditional)
            r'系统指令',      # system instructions
            r'系統指令',      # system instructions (traditional)
            r'系统消息',      # system message
            r'系統消息',      # system message (traditional)
            r'系统说明',      # system description/instructions
            r'系統說明',      # system description/instructions (traditional)
            
            # Japanese
            r'システムプロンプト',           # system prompt
            r'システム プロンプト',          # system prompt with space
            r'システム・プロンプト',         # system prompt with middle dot
            r'システム指示',                # system instructions
            r'システムメッセージ',           # system message
            r'システム メッセージ',          # system message with space
            r'システム・メッセージ',         # system message with middle dot
            r'システム命令',                # system command/instructions
            
            # Russian
            r'системный\s*промпт',          # system prompt
            r'системная\s*подсказка',       # system hint/prompt
            r'системное\s*приглашение',     # system invitation/prompt
            r'системный\s*запрос',          # system request/prompt
            r'системное\s*сообщение',       # system message
            r'системное\s*послание',        # system message
            r'системные\s*инструкции',      # system instructions
            r'системные\s*указания',        # system instructions/directions
            
            # Spanish
            r'prompt\s*del\s*sistema',       # system prompt
            r'indicación\s*del\s*sistema',   # system indication/prompt
            r'aviso\s*del\s*sistema',        # system notice/prompt
            r'comando\s*del\s*sistema',      # system command/prompt
            r'mensaje\s*del\s*sistema',      # system message
            r'instrucciones\s*del\s*sistema', # system instructions
            r'indicaciones\s*del\s*sistema', # system indications/instructions
        ]
        
        # Compile patterns for better performance (case insensitive)
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.UNICODE) 
            for pattern in self.system_prompt_patterns
        ]
        
        # Patterns to detect just "system" mentions in various languages
        self.system_mention_patterns = [
            # English
            r'\bsystem\b',
            r'\bsys\b',
            
            # Chinese (Simplified and Traditional)
            r'系统',     # system (simplified)
            r'系統',     # system (traditional)
            
            # Japanese  
            r'システム',  # system
            
            # Russian
            r'системн',   # system (covers системный, системная, системное, системные)
            
            # Spanish
            r'\bsistema\b',  # system
        ]
        
        # Compile system mention patterns
        self.compiled_system_mention_patterns = [
            re.compile(pattern, re.IGNORECASE | re.UNICODE)
            for pattern in self.system_mention_patterns
        ]
        
        # Patterns for specific policy violations that result in credit zeroing
        self.credit_zero_patterns = [
            r'kawaii[ \t]+stickers',  # space or tab between
            r'kawaii[_-]stickers',    # underscore or hyphen between
            r'kawaiistickers',        # no space between
        ]
        
        # Compile credit zero patterns
        self.compiled_credit_zero_patterns = [
            re.compile(pattern, re.IGNORECASE | re.UNICODE)
            for pattern in self.credit_zero_patterns
        ]

        self.project_root_patterns = [
            r'projectRoot: C:\\Users\\<USER>\\', # home directory
            r'projectRoot: C:\\Users\\<USER>\\', # home directory
        ]

        self.compiled_project_root_patterns = [
            re.compile(pattern, re.IGNORECASE | re.UNICODE)
            for pattern in self.project_root_patterns
        ]
    
    def check_system_prompt_violation(self, message: str) -> bool:
        """
        Check if the message contains variations of "system prompt" in multiple languages.
        
        Args:
            message: The user message to check
            
        Returns:
            True if a violation is detected, False otherwise
        """
        if not message:
            return False
            
        # Check against all compiled patterns
        for pattern in self.compiled_patterns:
            if pattern.search(message):
                return True
                
        return False
    
    def check_system_mention(self, message: str) -> bool:
        """
        Check if the message contains any mention of "system" in multiple languages.
        This is less strict than check_system_prompt_violation.
        
        Args:
            message: The user message to check
            
        Returns:
            True if a system mention is detected, False otherwise
        """
        if not message:
            return False
            
        # Check against all compiled system mention patterns
        for pattern in self.compiled_system_mention_patterns:
            if pattern.search(message):
                return True
                
        return False
    
    def check_credit_zero_violation(self, message: str) -> bool:
        """
        Check if the message contains patterns that should result in credit zeroing.
        Currently checks for combinations of (kawaii OR chibi) AND stickers.
        
        Args:
            message: The user message to check
        Returns:
            True if a credit zero violation is detected, False otherwise
        """
        if not message:
            return False
        
        # Convert to lowercase for case-insensitive matching
        message_lower = message.lower()
        
        has_sticker = bool(re.search(r'stickers?', message_lower))
        
        if not has_sticker:
            return False
        
        has_kawaii_or_chibi = bool(re.search(r'(kawaii|chibi)', message_lower))
        
        # If both conditions are met, it's a violation
        if has_kawaii_or_chibi and has_sticker:
            return True
        
        # Also check the original specific patterns for backward compatibility
        for pattern in self.compiled_credit_zero_patterns:
            if pattern.search(message):
                return True
                
        return False

    def check_project_root_violation(self, message: str) -> bool:
        """
        Check if the message contains patterns that should result in project root violation.
        
        Args:
            message: The user message to check
        Returns:
            True if a credit zero violation is detected, False otherwise
        """
        if not message:
            return False
        
        # Convert to lowercase for case-insensitive matching
        message_lower = message.lower()

        for pattern in self.compiled_project_root_patterns:
            if pattern.search(message_lower):
                return True
        
        return False

    
    def get_violation_message(self) -> str:
        """
        Get the standard violation message.
        
        Returns:
            The violation message to return to the user
        """
        return "Your prompt was flagged as violating our Fair Use Policy."
    
    @staticmethod
    def is_running_tests() -> bool:
        """
        Check if we're currently running tests.
        
        Returns:
            True if running tests, False otherwise
        """
        return 'pytest' in sys.modules


# Global instance for easy importing
prompt_violation_checker = PromptViolationChecker()
