using UnityEditor;
using UnityEngine;
using System.IO;
using System.Xml;
using System.Reflection;
using System;
using Newtonsoft.Json.Linq;

public static class CoplayDependenciesInstaller
{
    [MenuItem("Coplay/Dev Dependencies/1. Install NuGet for Unity")]
    private static void InstallNugetForUnity()
    {
        Debug.Log("Checking whether NuGet for Unity is installed");
        string manifestPath = Path.Combine(Application.dataPath, "..", "Packages", "manifest.json");

        if (!File.Exists(manifestPath))
        {
            Debug.LogError("Packages/manifest.json not found!");
            return;
        }

        try
        {
            string manifestContent = File.ReadAllText(manifestPath);
            var manifest = JObject.Parse(manifestContent);

            var dependencies = manifest["dependencies"] as JObject;
            if (dependencies == null)
            {
                dependencies = new JObject();
                manifest["dependencies"] = dependencies;
            }

            string nugetPackageId = "com.github-glitchenzo.nugetforunity";
            string nugetPackageUrl = "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity";

            if (!dependencies.ContainsKey(nugetPackageId))
            {
                dependencies[nugetPackageId] = nugetPackageUrl;

                string updatedManifest = manifest.ToString(Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(manifestPath, updatedManifest);

                Debug.Log("NuGet for Unity dependency added to manifest.json, now deactivate then activate Unity window");
            }
            else
            {
                Debug.Log("NuGet for Unity dependency already installed");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to update manifest.json: {ex.Message}");
        }
    }


    [MenuItem("Coplay/Dev Dependencies/2. Install NuGet Dependencies")]
    public static void InstallNuGetDependencies()
    {
        Debug.Log("Starting NuGet dependencies installation...");

        // Get the path to packages.config relative to CoplayDependenciesInstaller.cs
        string currentFilePath = GetCurrentFilePath();
        string packagesConfigPath = Path.Combine(Path.GetDirectoryName(currentFilePath), "..", "..", "packages.config");
        packagesConfigPath = Path.GetFullPath(packagesConfigPath);

        if (!File.Exists(packagesConfigPath))
        {
            Debug.LogError($"packages.config not found at: {packagesConfigPath}");
            return;
        }

        try
        {
            // Parse the XML file
            XmlDocument doc = new XmlDocument();
            doc.Load(packagesConfigPath);

            // Find packages with manuallyInstalled="true"
            XmlNodeList packageNodes = doc.SelectNodes("//package[@manuallyInstalled='true']");

            if (packageNodes.Count == 0)
            {
                Debug.Log("No manually installed packages found in packages.config");
                return;
            }

            Debug.Log($"Found {packageNodes.Count} manually installed packages to install");

            // Use reflection to access NuGet for Unity
            Assembly nugetAssembly = GetNuGetForUnityAssembly();
            if (nugetAssembly == null)
            {
                Debug.LogError("NuGet for Unity assembly not found. Please install NuGet for Unity first.");
                return;
            }

            // Get the NugetPackageInstaller type
            Type installerType = nugetAssembly.GetType("NugetForUnity.NugetPackageInstaller");
            if (installerType == null)
            {
                Debug.LogError("NugetPackageInstaller type not found in NuGet for Unity assembly");
                return;
            }

            // Get the NugetPackageIdentifier type
            Type identifierType = nugetAssembly.GetType("NugetForUnity.Models.NugetPackageIdentifier");
            if (identifierType == null)
            {
                Debug.LogError("NugetPackageIdentifier type not found in NuGet for Unity assembly");
                return;
            }

            // Get the InstallIdentifier method
            MethodInfo installMethod = installerType.GetMethod("InstallIdentifier",
                BindingFlags.Static | BindingFlags.Public,
                null,
                new Type[] {
                    nugetAssembly.GetType("NugetForUnity.Models.INugetPackageIdentifier"),
                    typeof(bool),
                    typeof(bool),
                    typeof(bool)
                },
                null);

            if (installMethod == null)
            {
                Debug.LogError("InstallIdentifier method not found in NugetPackageInstaller");
                return;
            }

            // Install each manually installed package
            foreach (XmlNode packageNode in packageNodes)
            {
                string packageId = packageNode.Attributes["id"]?.Value;
                string version = packageNode.Attributes["version"]?.Value;

                if (string.IsNullOrEmpty(packageId))
                {
                    Debug.LogWarning("Package node missing id attribute, skipping");
                    continue;
                }

                Debug.Log($"Installing package: {packageId} version: {version ?? "latest"}");

                try
                {
                    // Create NugetPackageIdentifier instance
                    object packageIdentifier = Activator.CreateInstance(identifierType, packageId, version);

                    // Set IsManuallyInstalled to true using reflection
                    PropertyInfo isManuallyInstalledProperty = identifierType.GetProperty("IsManuallyInstalled");
                    if (isManuallyInstalledProperty != null && isManuallyInstalledProperty.CanWrite)
                    {
                        isManuallyInstalledProperty.SetValue(packageIdentifier, true);
                    }

                    // Call InstallIdentifier method
                    installMethod.Invoke(null, new object[] {
                        packageIdentifier,
                        true,  // refreshAssets
                        false, // isSlimRestoreInstall
                        true   // allowUpdateForExplicitlyInstalled
                    });

                    Debug.Log($"Successfully queued installation for package: {packageId}");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Failed to install package {packageId}: {ex.Message}");
                }
            }

            Debug.Log("NuGet dependencies installation completed");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to install NuGet dependencies: {ex.Message}");
        }
    }

    [MenuItem("Coplay/Dev Dependencies/3. Install Coplay from Sources")]
    public static void InstallCoplayFromSources()
    {
        Debug.Log("Installing Coplay from sources...");
        
        string manifestPath = Path.Combine(Application.dataPath, "..", "Packages", "manifest.json");

        if (!File.Exists(manifestPath))
        {
            Debug.LogError("Packages/manifest.json not found!");
            return;
        }

        try
        {
            // Get the path to CoplayUnityPlugin folder relative to current cs file
            string currentFilePath = GetCurrentFilePath();
            string coplayPluginPath = Path.Combine(Path.GetDirectoryName(currentFilePath), "..", "..", "CoplayUnityPlugin");
            coplayPluginPath = Path.GetFullPath(coplayPluginPath);
            
            // Convert to file:// URL format
            string coplayPackageUrl = "file:" + coplayPluginPath.Replace('\\', '/');
            
            Debug.Log($"Coplay plugin path: {coplayPluginPath}");
            Debug.Log($"Coplay package URL: {coplayPackageUrl}");

            // Verify the CoplayUnityPlugin directory exists
            if (!Directory.Exists(coplayPluginPath))
            {
                Debug.LogError($"CoplayUnityPlugin directory not found at: {coplayPluginPath}");
                return;
            }

            // Verify package.json exists in CoplayUnityPlugin
            string packageJsonPath = Path.Combine(coplayPluginPath, "package.json");
            if (!File.Exists(packageJsonPath))
            {
                Debug.LogError($"package.json not found in CoplayUnityPlugin at: {packageJsonPath}");
                return;
            }

            string manifestContent = File.ReadAllText(manifestPath);
            var manifest = JObject.Parse(manifestContent);

            var dependencies = manifest["dependencies"] as JObject;
            if (dependencies == null)
            {
                dependencies = new JObject();
                manifest["dependencies"] = dependencies;
            }

            string coplayPackageId = "com.coplaydev.coplay";

            var logMessage = dependencies.ContainsKey(coplayPackageId) 
                ? "Coplay package already exists in manifest.json, updating path..." 
                : "Adding Coplay package to manifest.json...";
            Debug.Log(logMessage);
            dependencies[coplayPackageId] = coplayPackageUrl;

            string updatedManifest = manifest.ToString(Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(manifestPath, updatedManifest);

            Debug.Log("Coplay package successfully added to manifest.json. Unity will refresh packages automatically.");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to install Coplay from sources: {ex.Message}");
        }
    }
    
    private static string GetCurrentFilePath([System.Runtime.CompilerServices.CallerFilePath] string filePath = "")
    {
        return filePath;
    }
    
    private static Assembly GetNuGetForUnityAssembly()
    {
        // Try to find NuGet for Unity assembly
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();
        
        // Look for assembly containing NuGet for Unity types
        foreach (var assembly in assemblies)
        {
            if (assembly.GetType("NugetForUnity.NugetPackageInstaller") != null)
            {
                return assembly;
            }
        }
        
        // If not found in loaded assemblies, try to load it by name
        try
        {
            return Assembly.Load("NugetForUnity");
        }
        catch
        {
            // Assembly not found
            return null;
        }
    }
}
