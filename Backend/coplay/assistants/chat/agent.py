import asyncio
from collections import defaultdict
import json
from time import perf_counter
import traceback
from typing import As<PERSON><PERSON>enerator
import uuid

from langchain_core.messages import AIMessage, SystemMessage, AIMessageChunk
from supabase_auth.types import User
from fastapi import Request
import yaml
from fastapi_events.dispatcher import dispatch
from openai import OpenAIError, APITimeoutError as OpenAITimeoutError
from anthropic import AnthropicError, APITimeoutError as AnthropicTimeoutError
from langchain_google_genai.chat_models import GoogleGenerativeAIError
from google.api_core.exceptions import InternalServerError as GoogleInternalServerError
from google.api_core.exceptions import DeadlineExceeded
from litellm import InternalServerError

from .threads import get_thread, update_thread_messages
from .messages import _to_coplay_tool_calls, extract_reasoning_output
from .graph import WORKFLOW, MEMORY, MODEL_INSTANCES
from .violations import handle_system_mention_notification, handle_prompt_violation_notification

from ...events import CoplayEvent
from ...usage_calculation import calculate_usage
from ...prompts import AGENT_SYSTEM_PROMPT, BATCH_TOOL_INSTRUCTIONS, CHAT_ASSISTANT_INSTRUCTIONS, ORCHESTRATOR_ASSISTANT_INSTRUCTIONS, PIPELINE_ASSISTANT_INSTRUCTIONS
from ...request_utils import extract_coplay_version, guard_disconnected
from ...logger import logger
from ...models import AIModel, CoplayException, CoplayRun, MessageType, Prompt, PromptResponse, AssistantMode, AgentState, CoplayUsage, ToolOutput
from ...supabase_db import can_user_make_requests, check_if_user_has_subscription, get_user_anthropic_key, zero_user_credits
from ...third_party.coplay_tracer import CoplayTracer
from ...prompt_violation_checker import prompt_violation_checker
from ...assistants.model_config import is_claude_model


THREAD_LOCKS: dict[str, asyncio.Lock] = defaultdict(lambda: asyncio.Lock())
DEFAULT_MODEL = AIModel.GPT4O

async def _acquire_thread_lock(thread_id: str):
    """Acquire lock for a specific thread"""
    await asyncio.wait_for(THREAD_LOCKS[thread_id].acquire(), timeout=60)

def _release_thread_lock(thread_id: str):
    """Release lock for a specific thread"""
    try:
        THREAD_LOCKS[thread_id].release()
    except RuntimeError:
        logger.error("Releasing an unlocked lock")

async def send_message_to_llm(
    prompt: Prompt,
    device_id: str,
    user: User,
    request: Request,
    task_id: str | None = None,
    cancel_scope: asyncio.Event | None = None
) -> AsyncGenerator[PromptResponse, None]:    
    thread_id = prompt.thread_id
    user_email = user.email
    turn = None
    coplay_version = extract_coplay_version(request) or "unknown"
    config = {
        "configurable": {"thread_id": thread_id},
        "metadata": {
            "user_id": user_email,
            "device_id": device_id,
            "thread_id": thread_id,
            "coplay_version": coplay_version
        },
        "callbacks": [CoplayTracer()]
    }
    error_response = None
    
    # Check for content policy violations before processing
    if prompt_violation_checker.check_system_prompt_violation(prompt.prompt): # TODO: this only checks the user prompt, and users could still get there through the coplayrules or orchestratorPlan.
        logger.error(f"Prompt violation detected for user {user_email} in thread {thread_id}. Prompt: {prompt.prompt}")
        
        # Send violation notification in background (non-blocking)
        asyncio.create_task(handle_prompt_violation_notification(prompt.prompt, user_email, thread_id, coplay_version))
        
        yield PromptResponse(
            type="error",
            thread_id=thread_id,
            message=prompt_violation_checker.get_violation_message()
        )
        return
    
    # Check for credit zero violations (kawaii stickers)
    if not await check_if_user_has_subscription(user.id) and (
        prompt_violation_checker.check_credit_zero_violation(prompt.prompt) or prompt_violation_checker.check_project_root_violation(yaml.dump(prompt.get_system_details()))):
        logger.warning(f"Credit zero violation detected for user {user_email} in thread {thread_id}. Prompt: {prompt.prompt}")
        
        # Send violation notification in background (non-blocking)
        asyncio.create_task(handle_prompt_violation_notification(prompt.prompt, user_email, thread_id, coplay_version))
        
        # Zero the user's credits immediately
        try:
            result = await zero_user_credits(user.id, "kawaii_stickers_violation")
            if result:
                logger.warning(f"Successfully zeroed credits for user {user.id} due to kawaii stickers violation")
            else:
                logger.error(f"Failed to zero credits for user {user.id} due to kawaii stickers violation")
        except Exception as e:
            logger.error(f"Error zeroing credits for user {user.id}: {e}")
        
        yield PromptResponse(
            type="error",
            thread_id=thread_id,
            message="Your prompt was flagged as violating our Fair Use Policy. Your account credits have been reset to zero."
        )
        return
    
    # Check for system mentions (non-blocking notification). We do this to check if anyone is close to stealing our tool schema.
    if prompt_violation_checker.check_system_mention(prompt.prompt):
        asyncio.create_task(handle_system_mention_notification(prompt.prompt, user_email, thread_id, coplay_version))
    
    try:
        await _acquire_thread_lock(thread_id)
        
        logger.info(f"Sending message to LLM with thread_id: {thread_id}")
        logger.info(f"prompt: {str(prompt)[:100]}")
        if prompt.model:
            logger.info(f"model: {prompt.model}")
        logger.info(f"tool_outputs: {str(prompt.tool_outputs)[:100]}")
        logger.info(f"assistant_mode: {prompt.assistant_mode}")

        # Get the model to use for this request
        request_model = AIModel(prompt.model or DEFAULT_MODEL)
        model_config = MODEL_INSTANCES[request_model]
        if not model_config:
            yield PromptResponse(
                type="error",
                thread_id=thread_id,
                message=f"Model {request_model} is not supported"
            )
            return
        
        # Check if user is using their own API key for supported models
        user_api_key = await get_user_anthropic_key(user)
        using_own_key = bool(user_api_key and is_claude_model(request_model))
        
        # Skip eligibility check if using own API key
        if not using_own_key:
            can_make_request = await can_user_make_requests(user.id, str(request_model), using_own_key)
            if not can_make_request:
                # Check if the user has an active subscription to provide a more specific message
                has_subscription = await check_if_user_has_subscription(user.id)
                
                if has_subscription:
                    raise CoplayException(f"You've used all your subscription credits for this billing cycle. Your $4 use-it-or-lose-it credits will reset tomorrow. Full credits will reset on your next billing date.")
                else:
                    raise CoplayException(
                        "You've used all your free credits. Please subscribe or top up to continue using Coplay."
                    )

        system_details = prompt.get_system_details()

        thread = await get_thread(thread_id)
        turn = thread.turn + 1
        messages = thread.messages

        if len(messages) > 0:
            state = AgentState(
                messages=messages,
                device_id=device_id,
                user=user,
                scene_id=f"scene-{prompt.context.get('scene_id')}",
                prefab_id=f"prefab-{prompt.context.get('prefab_id')}" if prompt.context.get('prefab_id') else None,
                thread_id=thread_id,
                prompt=prompt,
                assistant_mode=thread.assistant_mode,
                current_model=request_model,  # Store the model being used for this request
                coplay_version=coplay_version,
            )
        else:
            batch_mode_instructions = BATCH_TOOL_INSTRUCTIONS if prompt.assistant_mode == AssistantMode.EXPERIMENTAL or prompt.assistant_mode == AssistantMode.PIPELINE_RECORDING else ''

            # Choose the system prompt based on the assistant mode
            screenshots_enabled = prompt.context.get("screenshots_enabled") == "true"
            system_prompt = CHAT_ASSISTANT_INSTRUCTIONS.format(
                system_details=yaml.dump(system_details),
                batch_tool_instructions=batch_mode_instructions,
                screenshots_enabled=screenshots_enabled,
            )
            if prompt.assistant_mode == AssistantMode.PIPELINE_RECORDING:
                system_prompt = PIPELINE_ASSISTANT_INSTRUCTIONS.format(
                    system_details=yaml.dump(system_details),
                    batch_tool_instructions=batch_mode_instructions,
                    screenshots_enabled=screenshots_enabled,
                )
            elif prompt.assistant_mode == AssistantMode.STEP_BY_STEP:
                system_prompt = AGENT_SYSTEM_PROMPT.format(
                    system_details=yaml.dump(system_details),
                    project_root=system_details["projectRoot"],
                    screenshots_enabled=screenshots_enabled,
                )
            elif prompt.assistant_mode == AssistantMode.ORCHESTRATOR:
                system_prompt = ORCHESTRATOR_ASSISTANT_INSTRUCTIONS.format()
            
            # Append custom rules if they exist
            custom_rules = prompt.context.get("custom_rules")
            if custom_rules:
                system_prompt += f"\n\n====\n\nCUSTOM RULES\n\n{custom_rules}"
            
            state = AgentState(
                messages=[SystemMessage(content=[{"type": "text", "text": system_prompt}])],
                device_id=device_id,
                user=user,
                scene_id=f"scene-{prompt.context.get('scene_id')}",
                prefab_id=f"prefab-{prompt.context.get('prefab_id')}" if prompt.context.get('prefab_id') else None,
                thread_id=thread_id,
                prompt=prompt,
                assistant_mode=prompt.assistant_mode,
                current_model=request_model,  # Store the model being used for this request
                coplay_version=coplay_version,
            )
                    
        # Update config metadata with turn information
        config["metadata"]["turn"] = turn
        
        # Run workflow with recursive validation (handles all attempts including the first one)
        async for response in _run_workflow_with_validation_recursive(
            state, config, prompt, thread_id, task_id, cancel_scope,
            request_model, user, not using_own_key, request
        ):
            yield response

    except CoplayException as e:
        error_response = PromptResponse(
            type="error",
            thread_id=prompt.thread_id,
            message=e.message
        )
        yield error_response

    except asyncio.CancelledError as e:
        logger.warning(f"User cancelled the request for thread {thread_id}.")
            
    except (DeadlineExceeded, OpenAITimeoutError, AnthropicTimeoutError, TimeoutError) as e:
        logger.error(f"Timeout in send_message_to_llm: {str(e)}")
        yield PromptResponse(
            type="error",
            thread_id=prompt.thread_id,
            message="Request to model timed out. Please try again."
        )
    
    except (InternalServerError, OpenAIError, AnthropicError, GoogleGenerativeAIError, GoogleInternalServerError) as e:
        logger.error(f"Error in send_message_to_llm: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Handle specific Anthropic API overload error
        if isinstance(e, AnthropicError):
            is_overloaded = False
            
            try:
                # Check the error message or response for overload indicators
                error_str = str(e)
                if 'overloaded_error' in error_str or 'Overloaded' in error_str:
                    is_overloaded = True
                
                # Also check the response body if available
                if not is_overloaded and hasattr(e, 'response') and e.response:
                    try:
                        error_data = e.response.json() if hasattr(e.response, 'json') else {}
                        if error_data.get('error', {}).get('type') == 'overloaded_error':
                            is_overloaded = True
                    except Exception:
                        # If we can't parse the error response, continue with string check
                        pass
            except Exception:
                # If we can't parse the error, fall through to generic handling
                pass
            
            if is_overloaded:
                error_response = PromptResponse(
                    type="error",
                    thread_id=prompt.thread_id,
                    message="Anthropic Overloaded Error. It seems we are hitting rate limits for this model. Please try again in a few moments or use a different model."
                )
                yield error_response
                return
        
        if isinstance(e, GoogleInternalServerError):
            yield PromptResponse(
                type="error",
                thread_id=prompt.thread_id,
                message=f"Google Gemini API returned an internal server error: {str(e)}. This is likely a transient issue on Google's side. Please try again in a few moments."
            )
            return

        # Generic API error handling
        error_response = PromptResponse(
            type="error",
            thread_id=prompt.thread_id,
            message=f"{prompt.model.value} returned an error: {str(e)}."
        )
        yield error_response
        
    except Exception as ex:
        logger.error(f"Error in send_message_to_llm: {str(ex)}")
        logger.error(traceback.format_exc())
        # Add detailed debugging information about the state
        if 'state' in locals():
            logger.debug("\nDebug State Information:")
            logger.debug(f"Number of messages: {len(state['messages'])}")
            logger.debug("\nTool Calls:")
            for i, call in enumerate(state.get('tool_calls', [])):
                logger.debug(f"\nTool Call {i + 1}:")
                logger.debug(f"  ID: {call.get('id')}")
                logger.debug(f"  Name: {call.get('name')}")
                logger.debug(f"  Arguments: {json.dumps(call.get('args'), indent=2)}")
            
            logger.debug("\nTool Outputs:")
            for i, output in enumerate(state.get('tool_outputs', [])):
                logger.debug(f"\nTool Output {i + 1}:")
                logger.debug(f"  Tool Call ID: {getattr(output, 'tool_call_id', 'N/A')}")
                logger.debug(f"  Output: {getattr(output, 'tool_output', 'N/A')}")
            
            logger.debug("\nMessage History:")
            for i, msg in enumerate(state['messages']):
                logger.debug(f"\nMessage {i + 1}:")
                logger.debug(f"  Type: {type(msg).__name__}")
                logger.debug(f"  Content: ...{msg.content[-100:]}")  # Last 100 chars
                if hasattr(msg, 'tool_calls'):
                    logger.debug(f"  Tool Calls: {msg.tool_calls}")

        error_response = PromptResponse(
            type="error",
            thread_id=prompt.thread_id,
            message="Error -- If the error persists start a new chat. Details: " + repr(ex)
        )
        yield error_response

    finally:
        try:
            state = await WORKFLOW.aget_state(config)
            messages = state.values.get("messages", [])
            # If we don't have any messages a failure happened before the workflow was invoked so we don't update the messages.
            if len(messages) > 0:
                if error_response is not None:
                    messages.append(AIMessage(id=str(uuid.uuid4()), content=error_response.message, tool_calls=[], additional_kwargs={"type": MessageType.ERROR.value}))
            else:
                # Make sure we load existing database messages to avoid losing them.
                thread = await get_thread(thread_id)
                messages = thread.messages
            await update_thread_messages(thread_id, messages, turn or 0) # If the user cancels before the first message is saved, we need to run this update to ensure there are zero messages.
        except Exception as e:
            logger.error(f"Error in update_assistant_messages: {e}")
            logger.error(traceback.format_exc())
        
        # Delete the in-memory checkpoint since all the state we need is stored in the database
        try:
            MEMORY.delete_thread(thread_id)
        except Exception as e:
            logger.error(f"Error in deleting checkpoint: {e}")
            logger.error(traceback.format_exc())
        
        _release_thread_lock(thread_id)

async def _run_workflow_with_validation_recursive(
    state: AgentState, 
    config: dict, 
    prompt: Prompt, 
    thread_id: str, 
    task_id: str | None, 
    cancel_scope: asyncio.Event | None,
    request_model: str,
    user: User,
    not_using_own_key: bool,
    request: Request,
    validation_attempt: int = 1,
    max_validation_attempts: int = 3
):
    """
    Recursively run the workflow with validation until tool calls are valid or max attempts reached.
    This is an async generator that yields PromptResponse objects during streaming.
    """
    final_state: AgentState | None = None
    cumulative_message: AIMessage | None = None
    usage: CoplayUsage | None = None
    run: CoplayRun | None = None
    
    chunks: list[AIMessageChunk] = []
    first_token_time = None
    stream_start_time = perf_counter()
    # Track if this is the first workflow run for accurate timing
    is_first_run = validation_attempt == 1
    
    if cancel_scope is not None and cancel_scope.is_set():
        raise asyncio.CancelledError("Client disconnected")

    async for chunk in WORKFLOW.astream(state, config, stream_mode=["values", "messages", "custom"], checkpoint_during=True):
        if chunk[0] == "custom":
            yield PromptResponse(
                type="progress",
                message=chunk[1]["coplay"],
                thread_id=thread_id,
                task_id=task_id,
                finished=False
            )
        elif chunk[0] == "values":
            final_state = chunk[1]
        elif chunk[0] == "messages":
            if chunk[1][1]["langgraph_node"] in ["respond_to_user", "check_tool_outputs"]:
                msg = chunk[1][0]
                if msg.type == "AIMessageChunk":
                    if first_token_time is None and len(msg.content) > 0:
                        first_token_time = perf_counter()
                        # Only log timing for the first attempt, not validation retries
                        if is_first_run:
                            time_to_first_token = first_token_time - stream_start_time
                            logger.info(f"Model time-to-first-token for {request_model} (streaming): {time_to_first_token:.2f}s")
                    
                    if cumulative_message is None or (cumulative_message.id != msg.id and not cumulative_message.id.startswith('resp_')):
                        cumulative_message = msg
                        cumulative_message.additional_kwargs["_coplay_id"] = cumulative_message.id
                        if len(chunks) > 0:
                            # Dispatch usage for previous message chunks
                            usage = calculate_usage(chunks, request_model)
                            run = CoplayRun(
                                usage=usage,
                                message_id=cumulative_message.id,
                                model=request_model,
                                thread_id=thread_id,
                                user_id=user.id,
                                chargeable=not_using_own_key,
                                prompt=prompt.prompt,
                                tool_calls=len(getattr(cumulative_message, "tool_calls", []) or []),
                                tool_outputs=len(prompt.tool_outputs)
                            )
                            dispatch(CoplayEvent.USAGE_DATA_UPDATE, run)
                        chunks = [msg]
                    else:
                        cumulative_message += msg
                        chunks.append(msg)
                    yield PromptResponse(
                        type="message",
                        finished=False,
                        thread_id=thread_id,
                        task_id=task_id,
                        message_id=cumulative_message.additional_kwargs["_coplay_id"],
                        message=extract_reasoning_output(cumulative_message) + cumulative_message.text(),
                        tool_calls=_to_coplay_tool_calls(cumulative_message, state),
                    )
        if cancel_scope is not None and cancel_scope.is_set():
            raise asyncio.CancelledError("Client disconnected")

    if not final_state:
        raise CoplayException("Failed to get final state from workflow")

    last_message: AIMessage = final_state["messages"][-1]

    if len(chunks) > 0:
        usage = calculate_usage(chunks, request_model)
    else:
        usage = calculate_usage([last_message], request_model)

    run = CoplayRun(
        usage=usage,
        message_id=last_message.id,
        model=request_model,
        thread_id=thread_id,
        user_id=user.id,
        chargeable=not_using_own_key,
        prompt=prompt.prompt,
        tool_calls=len(getattr(last_message, "tool_calls", [])),
        tool_outputs=len(prompt.tool_outputs),
        coplay_version=config["metadata"]["coplay_version"]
    )
    
    # Validate tool calls - only if we haven't exceeded max attempts
    if validation_attempt <= max_validation_attempts:
        validation_errors = _validate_tool_calls_logic(last_message)
        if validation_errors:
            logger.warning(f"Tool call validation failed (attempt {validation_attempt}/{max_validation_attempts}), restarting workflow")
            
            # Send progress message about validation
            yield PromptResponse(
                type="progress",
                message=f"Validating AI actions (attempt {validation_attempt})",
                thread_id=thread_id,
                task_id=task_id,
                finished=False
            )
            
            prompt.tool_outputs = validation_errors
            state["prompt"] = prompt
            
            if validation_attempt < max_validation_attempts:
                # Recursively retry with validation errors
                async for response in _run_workflow_with_validation_recursive(
                    state, config, prompt, thread_id, task_id, cancel_scope,
                    request_model, user, not_using_own_key, request, validation_attempt + 1, max_validation_attempts
                ):
                    yield response
                return
            else:
                logger.warning(f"Maximum validation attempts ({max_validation_attempts}) reached, proceeding with last response")
    
    # Dispatch usage data update here to avoid charging for validation attempts.
    dispatch(CoplayEvent.USAGE_DATA_UPDATE, run)
    
    # Return the final message
    yield PromptResponse(
        type="message",
        cost=run.usage.cost_usd if run.usage else 0.0,
        thread_id=thread_id,
        message_id=last_message.id,
        message=extract_reasoning_output(last_message) + last_message.text(),
        tool_calls=_to_coplay_tool_calls(last_message, state),
    )

def _validate_tool_calls_logic(message: AIMessage) -> list[ToolOutput]:
    """
    Validate the tool calls and return any validation errors as ToolOutputs.
    Currently only validates replace_in_file.
    """
    if not isinstance(message, AIMessage) or not message.tool_calls:
        return []
    
    validation_errors = []
    
    for tool_call in message.tool_calls:
        if tool_call["name"] == "replace_in_file":
            diff_block = tool_call['args'].get('diff')
            
            if not diff_block:
                continue
                
            is_different = True
            try:
                # Normalize line endings and split into lines
                lines = diff_block.strip().replace('\r\n', '\n').replace("```", "").strip().split('\n')
                
                # Check for markers
                if lines[0] == '------- SEARCH' and lines[-1] == '+++++++ REPLACE':
                    # Find separator
                    separator_index = lines.index('=======')
                    
                    # Extract blocks
                    search_from_diff = '\n'.join(lines[1:separator_index])
                    replace_from_diff = '\n'.join(lines[separator_index + 1:-1])
                    
                    # Compare trimmed content
                    if search_from_diff.strip() == replace_from_diff.strip():
                        is_different = False
                
            except (ValueError, IndexError):
                # If markers are not found or something is wrong with the structure,
                # assume it's a valid change to avoid blocking legitimate edits.
                pass
            
            if not is_different:
                msg = f"The tool call {tool_call['name']} failed because the search and replace blocks are identical and need to be different."
                logger.warning(msg)
                validation_errors.append(ToolOutput(tool_call_id=tool_call['id'], tool_output=msg))

    return validation_errors
