import { useState } from "react";
import { useToast } from "@/components/ui/toast";
import { updatePassword, validatePassword, validatePasswordConfirmation } from "@/services/authService";
import { useAsyncOperation } from "@/utils/formHelpers";

export function usePasswordChange() {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const toast = useToast();

  const { execute: handleChangePassword, isLoading } = useAsyncOperation(
    async () => {
      // Validate new password
      const passwordValidation = validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        throw new Error(passwordValidation.error);
      }

      // Validate password confirmation
      const confirmationValidation = validatePasswordConfirmation(newPassword, confirmPassword);
      if (!confirmationValidation.isValid) {
        throw new Error(confirmationValidation.error);
      }

      await updatePassword(currentPassword, newPassword);

      // Clear password fields on success
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    },
    () => {
      toast.success("Password changed successfully!");
    },
    (error) => {
      console.error("Failed to change password:", error);
      toast.error(`Failed to change password: ${error.message || "Please try again."}`);
    }
  );

  const isFormValid = (() => {
    // Check all fields have content (not just truthy)
    if (!currentPassword?.trim() || !newPassword?.trim() || !confirmPassword?.trim()) {
      return false;
    }

    // Validate new password requirements
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return false;
    }

    // Validate password confirmation matches
    const confirmationValidation = validatePasswordConfirmation(newPassword, confirmPassword);
    if (!confirmationValidation.isValid) {
      return false;
    }

    return true;
  })();

  return {
    currentPassword,
    setCurrentPassword,
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    isLoading,
    handleChangePassword,
    isFormValid,
  };
}
