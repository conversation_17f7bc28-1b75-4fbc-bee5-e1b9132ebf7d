import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { User } from "@supabase/supabase-js";
import { AuthProvider } from "@/services/authService";
import {
  fetchProfileData,
  updateUserProfile,
  validateProfileForm,
  buildProfileUpdatePayload,
  getProfileChanges,
  type ProfileData,
  type ProfileUpdatePayload,
} from "@/services/profileService";

// Re-export types for backward compatibility
export type { Profile, ProfileData, ProfileUpdatePayload } from "@/services/profileService";

export interface ProfileChanges {
  hasChanges: boolean;
  nameChanged: boolean;
  emailChanged: boolean;
}

/**
 * Custom hook to fetch and update user profile data
 */
export function useProfile(initialData?: ProfileData) {
  const [loading, setLoading] = useState<boolean>(true);
  const [updating, setUpdating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  // Form state
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [initialValues, setInitialValues] = useState({ name: "", email: "" });
  const [initialValuesSet, setInitialValuesSet] = useState(false);

  // Refs for managing async operations and preventing race conditions
  const isMountedRef = useRef(true);
  const fetchAbortControllerRef = useRef<AbortController | null>(null);
  const updateAbortControllerRef = useRef<AbortController | null>(null);
  const isUpdatingRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      fetchAbortControllerRef.current?.abort();
      updateAbortControllerRef.current?.abort();
    };
  }, []);

  const fetchProfile = useCallback(async () => {
    // Cancel any existing fetch operation
    fetchAbortControllerRef.current?.abort();

    // Create new abort controller for this fetch
    const abortController = new AbortController();
    fetchAbortControllerRef.current = abortController;

    try {
      if (isMountedRef.current) {
        setLoading(true);
        setError(null);
      }

      const data = await fetchProfileData();

      // Check if component is still mounted and this is the latest fetch
      if (isMountedRef.current && !abortController.signal.aborted) {
        setProfileData(data);
        setError(null);
      }
    } catch (err) {
      // Only handle error if not aborted and component still mounted
      if (!abortController.signal.aborted && isMountedRef.current) {
        console.error("Error fetching profile data:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch profile data"
        );
      }
    } finally {
      if (isMountedRef.current && !abortController.signal.aborted) {
        setLoading(false);
      }
    }
  }, []);

  // Update local state when profile data loads - single effect to prevent race conditions
  useEffect(() => {
    if (!loading && profileData && isMountedRef.current) {
      const newName = profileData.profile?.name || "";
      const newEmail = profileData.user?.email || "";

      setName(newName);
      setEmail(newEmail);

      // Only set initial values once per data load to prevent race conditions
      if (!initialValuesSet) {
        setInitialValues({ name: newName, email: newEmail });
        setInitialValuesSet(true);
      }
    }
  }, [loading, profileData, initialValuesSet]);

  // Reset initial values flag when starting a new fetch
  useEffect(() => {
    if (loading) {
      setInitialValuesSet(false);
    }
  }, [loading]);

  // Determine if profile data has changed
  const profileChanges = useMemo((): ProfileChanges => {
    const authProvider = profileData?.authProvider || { isOAuth: false };
    return getProfileChanges(name, email, initialValues.name, initialValues.email, authProvider.isOAuth);
  }, [name, email, initialValues, profileData?.authProvider]);

  // Build update payload with only changed fields
  const buildUpdatePayload = useCallback((): ProfileUpdatePayload => {
    const authProvider = profileData?.authProvider || { isOAuth: false };
    return buildProfileUpdatePayload(
      name,
      email,
      initialValues.name,
      initialValues.email,
      authProvider.isOAuth
    );
  }, [name, email, initialValues, profileData?.authProvider]);

  const updateProfile = useCallback(
    async (payload: ProfileUpdatePayload) => {
      if (!profileData?.user) {
        throw new Error("User not authenticated");
      }

      // Prevent concurrent updates
      if (isUpdatingRef.current) {
        throw new Error("Update already in progress");
      }

      // Cancel any existing update operation
      updateAbortControllerRef.current?.abort();

      // Create new abort controller for this update
      const abortController = new AbortController();
      updateAbortControllerRef.current = abortController;

      isUpdatingRef.current = true;

      try {
        if (isMountedRef.current) {
          setUpdating(true);
          setError(null);
        }

        await updateUserProfile(payload);

        // Only refresh if component is still mounted and operation wasn't aborted
        if (isMountedRef.current && !abortController.signal.aborted) {
          await fetchProfile();
          return { success: true };
        }

        return { success: false };
      } catch (err) {
        // Only handle error if not aborted and component still mounted
        if (!abortController.signal.aborted && isMountedRef.current) {
          const errorMessage =
            err instanceof Error ? err.message : "Failed to update profile";
          setError(errorMessage);
        }
        throw err;
      } finally {
        isUpdatingRef.current = false;
        if (isMountedRef.current && !abortController.signal.aborted) {
          setUpdating(false);
        }
      }
    },
    [profileData?.user, fetchProfile]
  );

  // Handle profile update submission using the built payload
  const submitProfileUpdate = useCallback(async () => {
    // Prevent submission during loading or updating
    if (loading || updating || isUpdatingRef.current) {
      throw new Error("Cannot submit while operation is in progress");
    }

    const payload = buildUpdatePayload();

    if (Object.keys(payload).length === 0) {
      throw new Error("No changes to save");
    }

    await updateProfile(payload);

    // Update baseline values after successful save - only if component still mounted
    if (isMountedRef.current) {
      setInitialValues((prev) => ({
        name: payload.name ?? prev.name,
        email: payload.email ?? prev.email,
      }));
      setInitialValuesSet(true);
    }
  }, [buildUpdatePayload, updateProfile, loading, updating]);

  // Check if form data is valid for submission
  const isFormValid = useMemo(() => {
    const authProvider = profileData?.authProvider || { isOAuth: false };
    return validateProfileForm(name, email, authProvider.isOAuth);
  }, [name, email, profileData?.authProvider]);

  // Initialize with provided data if available
  useEffect(() => {
    if (initialData && isMountedRef.current) {
      setProfileData(initialData);
      setLoading(false);
      setError(null);

      // Set form state with initial data
      const newName = initialData.profile?.name || "";
      const newEmail = initialData.user?.email || "";

      setName(newName);
      setEmail(newEmail);
      setInitialValues({ name: newName, email: newEmail });
      setInitialValuesSet(true);
    } else {
      fetchProfile();
    }
  }, [initialData]); // Include initialData in dependencies

  return {
    profileData,
    loading,
    updating,
    error,
    updateProfile,
    refreshProfile: fetchProfile,
    // Form state and functions
    name,
    setName,
    email,
    setEmail,
    profileChanges,
    buildUpdatePayload,
    submitProfileUpdate,
    isFormValid,
    // Convenience getters for easier access
    user: profileData?.user || null,
    profile: profileData?.profile || null,
    authProvider: profileData?.authProvider || {
      provider: "email",
      isOAuth: false,
      displayName: "Email",
    },
  };
}
