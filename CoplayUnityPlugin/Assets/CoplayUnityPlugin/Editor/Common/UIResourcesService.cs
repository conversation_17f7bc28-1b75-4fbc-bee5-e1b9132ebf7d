using System;
using System.IO;
using UnityEditor;
using Coplay.Services;

namespace Coplay.Common
{
    /// <summary>
    /// Service for loading UI resources from the appropriate locations based on installation type.
    /// </summary>
    public class UIResourcesService
    {
        private readonly IApplicationPaths _applicationPaths;

        /// <summary>
        /// Initializes a new instance of the UIResourcesService with the specified application paths.
        /// </summary>
        /// <param name="applicationPaths">The application paths service</param>
        public UIResourcesService(IApplicationPaths applicationPaths)
        {
            _applicationPaths = applicationPaths ?? throw new ArgumentNullException(nameof(applicationPaths));
        }

        /// <summary>
        /// Finds an asset using the specified file name.
        /// </summary>
        /// <typeparam name="T">The type of asset to find</typeparam>
        /// <param name="fileName">The file name of the asset</param>
        /// <returns>The loaded asset, or null if not found</returns>
        public T FindAsset<T>(string fileName) where T : UnityEngine.Object
        {
            bool isFont = Path.GetExtension(fileName).Equals(".ttf", StringComparison.OrdinalIgnoreCase) ||
                          Path.GetExtension(fileName).Equals(".otf", StringComparison.OrdinalIgnoreCase);
            
            // Build the path using the CoplayUIResourcesPath from IApplicationPaths
            string resourcePath = _applicationPaths.CoplayUIResourcesPath;
            
            // Add the Fonts subfolder if it's a font file
            if (isFont)
            {
                resourcePath = Path.Combine(Path.GetDirectoryName(resourcePath), "Fonts");
            }
            
            // Combine with the filename
            string assetPath = Path.Combine(resourcePath, fileName);
            
            // Convert backslashes to forward slashes for Unity's AssetDatabase
            assetPath = assetPath.Replace("\\", "/");
            
            // Try to load the asset
            T asset = AssetDatabase.LoadAssetAtPath<T>(assetPath);
            
            if (asset == null)
            {
                // Log warning if asset is not found
                UnityEngine.Debug.LogWarning($"Could not find UI resource: {fileName} at path: {assetPath}");
            }
            
            return asset;
        }
    }
}
