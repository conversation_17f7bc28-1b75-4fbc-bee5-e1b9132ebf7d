using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using UnityEngine;
using Coplay.Common;

namespace Coplay.Tests.Benchmark
{
    public class BenchmarkReporter
    {
        private readonly string _reportsDirectory;
        
        public BenchmarkReporter()
        {
            _reportsDirectory = Path.Combine(Application.dataPath, "Tests", "BenchmarkReports~");
            if (!Directory.Exists(_reportsDirectory))
            {
                Directory.CreateDirectory(_reportsDirectory);
            }
        }
        
        public async Task SaveSessionReport(BenchmarkSession session)
        {
            try
            {
                var timestamp = session.StartTime.ToString("yyyyMMdd_HHmmss");
                var fileName = $"benchmark_{session.Model}_{timestamp}.json";
                var filePath = Path.Combine(_reportsDirectory, fileName);
                
                var json = JsonConvert.SerializeObject(session, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                
                CoplayLogger.Log($"Saved benchmark session report: {fileName}");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to save session report: {ex.Message}", ex);
            }
        }
        
        public List<BenchmarkSession> LoadAllSessions()
        {
            var sessions = new List<BenchmarkSession>();
            
            try
            {
                var jsonFiles = Directory.GetFiles(_reportsDirectory, "benchmark_*.json");
                
                foreach (var file in jsonFiles)
                {
                    try
                    {
                        var json = File.ReadAllText(file);
                        var session = JsonConvert.DeserializeObject<BenchmarkSession>(json);
                        if (session != null)
                        {
                            sessions.Add(session);
                        }
                    }
                    catch (Exception ex)
                    {
                        CoplayLogger.LogError($"Failed to load session from {file}: {ex.Message}", ex);
                    }
                }
                
                CoplayLogger.Log($"Loaded {sessions.Count} benchmark sessions");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to load sessions: {ex.Message}", ex);
            }
            
            return sessions.OrderBy(s => s.StartTime).ToList();
        }
        
        public async Task GenerateComparisonReport(List<BenchmarkSession> sessions)
        {
            try
            {
                var report = new StringBuilder();
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                
                report.AppendLine("# Coplay Benchmark Comparison Report");
                report.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                report.AppendLine();
                
                // Overall summary
                GenerateOverallSummary(report, sessions);
                
                // Model comparison
                GenerateModelComparison(report, sessions);
                
                // Language breakdown
                GenerateLanguageBreakdown(report, sessions);
                
                // Detailed results
                GenerateDetailedResults(report, sessions);
                
                // Performance metrics
                GeneratePerformanceMetrics(report, sessions);
                
                var fileName = $"comparison_report_{timestamp}.md";
                var filePath = Path.Combine(_reportsDirectory, fileName);
                await File.WriteAllTextAsync(filePath, report.ToString());
                
                CoplayLogger.Log($"Generated comparison report: {fileName}");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to generate comparison report: {ex.Message}", ex);
            }
        }
        
        private void GenerateOverallSummary(StringBuilder report, List<BenchmarkSession> sessions)
        {
            report.AppendLine("## Overall Summary");
            report.AppendLine();
            
            var totalExercises = sessions.Sum(s => s.CompletedExercises);
            var totalSuccessful = sessions.Sum(s => s.Results.Count(r => r.Success));
            var totalCost = sessions.Sum(s => s.TotalCost);
            var totalDuration = sessions.Sum(s => s.TotalDuration);
            
            report.AppendLine($"- **Total Sessions**: {sessions.Count}");
            report.AppendLine($"- **Total Exercises**: {totalExercises}");
            report.AppendLine($"- **Total Successful**: {totalSuccessful} ({(totalExercises > 0 ? (double)totalSuccessful / totalExercises : 0):P2})");
            report.AppendLine($"- **Total Cost**: ${totalCost:F4}");
            report.AppendLine($"- **Total Duration**: {totalDuration:F1} seconds");
            report.AppendLine();
        }
        
        private void GenerateModelComparison(StringBuilder report, List<BenchmarkSession> sessions)
        {
            report.AppendLine("## Model Comparison");
            report.AppendLine();
            
            var modelGroups = sessions.GroupBy(s => s.Model).ToList();
            
            report.AppendLine("| Model | Sessions | Exercises | Success Rate | Avg Cost/Exercise | Avg Duration/Exercise |");
            report.AppendLine("|-------|----------|-----------|--------------|-------------------|----------------------|");
            
            foreach (var group in modelGroups.OrderBy(g => g.Key))
            {
                var modelSessions = group.ToList();
                var totalExercises = modelSessions.Sum(s => s.CompletedExercises);
                var totalSuccessful = modelSessions.Sum(s => s.Results.Count(r => r.Success));
                var totalCost = modelSessions.Sum(s => s.TotalCost);
                var totalDuration = modelSessions.Sum(s => s.TotalDuration);
                
                var successRate = totalExercises > 0 ? (double)totalSuccessful / totalExercises : 0;
                var avgCostPerExercise = totalExercises > 0 ? totalCost / totalExercises : 0;
                var avgDurationPerExercise = totalExercises > 0 ? totalDuration / totalExercises : 0;
                
                report.AppendLine($"| {group.Key} | {modelSessions.Count} | {totalExercises} | {successRate:P2} | ${avgCostPerExercise:F4} | {avgDurationPerExercise:F1}s |");
            }
            
            report.AppendLine();
        }
        
        private void GenerateLanguageBreakdown(StringBuilder report, List<BenchmarkSession> sessions)
        {
            report.AppendLine("## Language Breakdown");
            report.AppendLine();
            
            var allResults = sessions.SelectMany(s => s.Results).ToList();
            var languageGroups = allResults.GroupBy(r => r.Language).ToList();
            
            report.AppendLine("| Language | Exercises | Success Rate | Avg Syntax Errors | Avg Compilation Errors |");
            report.AppendLine("|----------|-----------|--------------|-------------------|------------------------|");
            
            foreach (var group in languageGroups.OrderBy(g => g.Key))
            {
                var results = group.ToList();
                var successRate = results.Count > 0 ? (double)results.Count(r => r.Success) / results.Count : 0;
                var avgSyntaxErrors = results.Count > 0 ? results.Average(r => r.SyntaxErrors) : 0;
                var avgCompilationErrors = results.Count > 0 ? results.Average(r => r.CompilationErrors) : 0;
                
                report.AppendLine($"| {group.Key} | {results.Count} | {successRate:P2} | {avgSyntaxErrors:F1} | {avgCompilationErrors:F1} |");
            }
            
            report.AppendLine();
        }
        
        private void GenerateDetailedResults(StringBuilder report, List<BenchmarkSession> sessions)
        {
            report.AppendLine("## Detailed Results by Model and Language");
            report.AppendLine();
            
            foreach (var session in sessions.OrderBy(s => s.Model).ThenBy(s => s.StartTime))
            {
                report.AppendLine($"### {session.Model} - {session.StartTime:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();
                
                var languageGroups = session.Results.GroupBy(r => r.Language).OrderBy(g => g.Key);
                
                foreach (var langGroup in languageGroups)
                {
                    var results = langGroup.ToList();
                    var successCount = results.Count(r => r.Success);
                    
                    report.AppendLine($"**{langGroup.Key}**: {successCount}/{results.Count} successful ({(double)successCount / results.Count:P2})");
                    
                    // Show failed exercises
                    var failures = results.Where(r => !r.Success).ToList();
                    if (failures.Count > 0)
                    {
                        report.AppendLine("Failed exercises:");
                        foreach (var failure in failures.Take(5)) // Limit to first 5 failures
                        {
                            var errorSummary = !string.IsNullOrEmpty(failure.ErrorMessage) 
                                ? failure.ErrorMessage.Split('\n').FirstOrDefault()?.Trim() 
                                : "Unknown error";
                            report.AppendLine($"- {failure.ExerciseName}: {errorSummary}");
                        }
                        if (failures.Count > 5)
                        {
                            report.AppendLine($"- ... and {failures.Count - 5} more");
                        }
                    }
                    
                    report.AppendLine();
                }
            }
        }
        
        private void GeneratePerformanceMetrics(StringBuilder report, List<BenchmarkSession> sessions)
        {
            report.AppendLine("## Performance Metrics");
            report.AppendLine();
            
            var allResults = sessions.SelectMany(s => s.Results).ToList();
            
            if (allResults.Count == 0)
            {
                report.AppendLine("No results available for performance analysis.");
                return;
            }
            
            // Function usage analysis
            var functionUsage = new Dictionary<string, int>();
            foreach (var result in allResults)
            {
                foreach (var func in result.FunctionsUsed)
                {
                    functionUsage[func] = functionUsage.GetValueOrDefault(func, 0) + 1;
                }
            }
            
            if (functionUsage.Count > 0)
            {
                report.AppendLine("### Function Usage");
                report.AppendLine();
                report.AppendLine("| Function | Usage Count | Percentage |");
                report.AppendLine("|----------|-------------|------------|");
                
                foreach (var kvp in functionUsage.OrderByDescending(kvp => kvp.Value))
                {
                    var percentage = (double)kvp.Value / allResults.Count;
                    report.AppendLine($"| {kvp.Key} | {kvp.Value} | {percentage:P2} |");
                }
                
                report.AppendLine();
            }
            
            // Error analysis
            var totalSyntaxErrors = allResults.Sum(r => r.SyntaxErrors);
            var totalCompilationErrors = allResults.Sum(r => r.CompilationErrors);
            var totalTimeouts = allResults.Sum(r => r.TestTimeouts);
            
            report.AppendLine("### Error Analysis");
            report.AppendLine();
            report.AppendLine($"- **Total Syntax Errors**: {totalSyntaxErrors}");
            report.AppendLine($"- **Total Compilation Errors**: {totalCompilationErrors}");
            report.AppendLine($"- **Total Timeouts**: {totalTimeouts}");
            report.AppendLine($"- **Exercises with Errors**: {allResults.Count(r => r.SyntaxErrors > 0 || r.CompilationErrors > 0)}");
            report.AppendLine();
            
            // Duration analysis
            var avgDuration = allResults.Average(r => r.Duration);
            var minDuration = allResults.Min(r => r.Duration);
            var maxDuration = allResults.Max(r => r.Duration);
            
            report.AppendLine("### Duration Analysis");
            report.AppendLine();
            report.AppendLine($"- **Average Duration**: {avgDuration:F1} seconds");
            report.AppendLine($"- **Minimum Duration**: {minDuration:F1} seconds");
            report.AppendLine($"- **Maximum Duration**: {maxDuration:F1} seconds");
            report.AppendLine();
        }
        
        public async Task GenerateAiderComparisonReport(List<BenchmarkSession> coplaySessions, string aiderResultsPath = null)
        {
            try
            {
                var report = new StringBuilder();
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                
                report.AppendLine("# Coplay vs Aider Benchmark Comparison");
                report.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                report.AppendLine();
                
                // Coplay results summary
                report.AppendLine("## Coplay Results");
                GenerateOverallSummary(report, coplaySessions);
                GenerateModelComparison(report, coplaySessions);
                
                // TODO: If aiderResultsPath is provided, load and compare with Aider results
                if (!string.IsNullOrEmpty(aiderResultsPath) && File.Exists(aiderResultsPath))
                {
                    report.AppendLine("## Aider Comparison");
                    report.AppendLine("*Aider comparison functionality to be implemented*");
                    report.AppendLine();
                }
                
                var fileName = $"coplay_vs_aider_{timestamp}.md";
                var filePath = Path.Combine(_reportsDirectory, fileName);
                await File.WriteAllTextAsync(filePath, report.ToString());
                
                CoplayLogger.Log($"Generated Aider comparison report: {fileName}");
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to generate Aider comparison report: {ex.Message}", ex);
            }
        }
        
        public void CleanupOldReports(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
                var files = Directory.GetFiles(_reportsDirectory);
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTimeUtc < cutoffDate)
                    {
                        File.Delete(file);
                        CoplayLogger.Log($"Deleted old report: {Path.GetFileName(file)}");
                    }
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Failed to cleanup old reports: {ex.Message}", ex);
            }
        }
    }
}
