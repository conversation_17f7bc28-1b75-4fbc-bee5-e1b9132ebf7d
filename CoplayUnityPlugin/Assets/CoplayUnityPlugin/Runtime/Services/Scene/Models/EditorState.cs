﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace Coplay.Services.Scene.Models
{
    /// <summary>
    /// Editor state
    /// </summary>
    public class EditorState
    {
        /// <summary>
        /// Gets or sets a value indicating whether the editor is in play mode
        /// </summary>
        [JsonProperty("playMode")]
        public bool PlayMode { get; set; }

        /// <summary>
        /// Gets or sets the selected game objects
        /// </summary>
        [JsonProperty("selectedGameObjects")]
        public List<GameObjectInfo> SelectedGameObjects { get; set; } = new();
        
        /// <summary>
        /// Gets or sets a value indicating whether there are compilation errors in the Unity project
        /// </summary>
        [JsonProperty("hasCompilationErrors")]
        public bool HasCompilationErrors { get; set; }
        
        /// <summary>
        /// Gets or sets the path of the currently active asset in the editor (scene, prefab, etc.)
        /// </summary>
        [JsonProperty("activeAssetPath")]
        public string ActiveAssetPath { get; set; }
    }
}
