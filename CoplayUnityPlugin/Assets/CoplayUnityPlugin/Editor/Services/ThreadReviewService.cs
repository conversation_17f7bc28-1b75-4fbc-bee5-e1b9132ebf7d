using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Coplay.Common.CoplayApi;
using Coplay.Models.ThreadReview;
using Coplay.Views.Components;
using Coplay.Controllers.Systems;
using Coplay.Controllers.Functions;
using Coplay.Common;
using Coplay.Services.Threading;
using Coplay.Models.Configuration;
using Coplay.Models.Assistants;

namespace Coplay.Services
{
    public class ThreadReviewService
    {
        private readonly ICoplayApiClient _apiClient;
        private readonly SettingsController _settingsController;
        private readonly Func<IFunctionInvokerController> _functionInvokerFactory;
        private readonly IApplicationPaths _applicationPaths;
        private readonly IEditorThreadHelper _editorThreadHelper;
        private readonly AssistantConfiguration _assistantConfiguration;

        public ThreadReviewService(ICoplayApiClient apiClient, SettingsController settingsController, Func<IFunctionInvokerController> functionInvokerFactory, IApplicationPaths applicationPaths, IEditorThreadHelper editorThreadHelper, AssistantConfiguration assistantConfiguration)
        {
            _apiClient = apiClient;
            _settingsController = settingsController;
            _functionInvokerFactory = functionInvokerFactory;
            _applicationPaths = applicationPaths;
            _editorThreadHelper = editorThreadHelper;
            _assistantConfiguration = assistantConfiguration;
        }

        /// <summary>
        /// Checks for a pending thread review and shows a popup if one exists
        /// </summary>
        /// <param name="threadId">The thread ID to check for reviews</param>
        /// <param name="chatView">The ChatView to show the review in</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if a review was found and processed, false otherwise</returns>
        public async Task<bool> CheckAndShowThreadReview(string threadId, ChatView chatView, CancellationToken cancellationToken = default)
        {
            try
            {
                CoplayLogger.Log($"Checking for thread review for thread: {threadId}");
                
                // Check if thread review is enabled in settings
                if (!_settingsController.EnableThreadReview)
                {
                    CoplayLogger.Log("Thread review is disabled in settings");
                    return false;
                }

                // We only do thread reviews in normal mode for now. This means we might miss a few reviews (even normal mode threads), but that's fine we don't need to get them all.
                if (_assistantConfiguration.Mode != AssistantMode.Normal)
                {
                    CoplayLogger.Log("Skipping thread review because assistant mode is not normal");
                    return false;
                }
                
                // Check if a review should be generated and get the review_id if one was created
                // Pass the current custom rules to the API
                var customRules = _settingsController.CustomRules;
                var shouldReviewResponse = await _apiClient.ShouldReviewThreadAsync(threadId, customRules, cancellationToken);
                if (!shouldReviewResponse.ShouldReview || string.IsNullOrEmpty(shouldReviewResponse.ReviewId))
                {
                    CoplayLogger.Log("No thread review should be generated for this thread or review creation failed");
                    return false;
                }
                
                CoplayLogger.Log($"Thread review created with ID: {shouldReviewResponse.ReviewId}. Polling for completion...");

                const int initialDelay = 6000;
                await Task.Delay(initialDelay, cancellationToken);

                // Poll for the specific review by review_id
                // We'll retry for up to 15 seconds to allow the review generation to complete
                const int maxRetries = 15;
                const int retryDelayMs = 1000; // 1 second between retries
                
                for (int attempt = 0; attempt < maxRetries; attempt++)
                {
                    var review = await _apiClient.GetThreadReviewByIdAsync(shouldReviewResponse.ReviewId, cancellationToken);
                    if (review != null)
                    {
                        // Check if the review is completed (has a meaningful suggestion)
                        if (!string.IsNullOrEmpty(review.Suggestion) && review.Status != "pending")
                        {
                            await ShowReviewPopup(review, chatView);
                            return true;
                        }
                        
                        // If it's still pending, continue polling
                        if (review.Status == "pending")
                        {
                            CoplayLogger.Log($"Thread review {shouldReviewResponse.ReviewId} is still pending, retrying in {retryDelayMs}ms (attempt {attempt + 1}/{maxRetries})");
                        }
                        else
                        {
                            CoplayLogger.Log($"Thread review {shouldReviewResponse.ReviewId} has status '{review.Status}' but no suggestion yet, retrying in {retryDelayMs}ms (attempt {attempt + 1}/{maxRetries})");
                        }
                    }
                    else
                    {
                        CoplayLogger.Log($"Thread review {shouldReviewResponse.ReviewId} not found, retrying in {retryDelayMs}ms (attempt {attempt + 1}/{maxRetries})");
                    }
                    
                    // If this is not the last attempt, wait before retrying
                    if (attempt < maxRetries - 1)
                    {
                        await Task.Delay(retryDelayMs, cancellationToken);
                    }
                }

                CoplayLogger.Log($"Thread review {shouldReviewResponse.ReviewId} not found after {maxRetries} attempts");
                return false;
            }
            catch (OperationCanceledException)
            {
                CoplayLogger.Log("Thread review check was cancelled");
                return false;
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error checking for thread review: {ex.Message}", ex);
                return false;
            }
        }

        private async Task ShowReviewPopup(ThreadReviewResponse review, ChatView chatView)
        {
            // Ensure UI creation happens on the main thread
            await _editorThreadHelper.ExecuteOnEditorThreadAsync(() =>
            {
                // Use the ChatView's ShowThreadReview method to display the review with diff content
                chatView.ShowThreadReview(review.DiffContent, (accepted, editedDiffContent) => HandleReviewResponse(review, accepted, editedDiffContent));
            });
        }

        private void HandleReviewResponse(ThreadReviewResponse review, bool accepted, string editedDiffContent)
        {
            _ = HandleReviewResponseAsync(review, accepted, editedDiffContent);
        }

        private async Task HandleReviewResponseAsync(ThreadReviewResponse review, bool accepted, string editedDiffContent)
        {
            try
            {
                var response = await _apiClient.RespondToThreadReviewAsync(review.ReviewId, accepted);
                if (response != null)
                {
                    CoplayLogger.Log($"Successfully responded to thread review: {response.Status}");
                    
                    if (accepted)
                    {
                        // Use edited diff content if available, otherwise use original
                        string diffToExecute = editedDiffContent ?? review.DiffContent;
                        
                        if (!string.IsNullOrEmpty(diffToExecute))
                        {
                            await ExecuteReplaceInFile(diffToExecute);
                        }
                        else
                        {
                            CoplayLogger.Log("User accepted but no diff content to execute - both original and edited diff content are null/empty");
                        }
                    }
                }
                else
                {
                    CoplayLogger.LogError("Failed to respond to thread review");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error handling review response: {ex.Message}", ex);
            }
        }

        private async Task ExecuteReplaceInFile(string diffContent)
        {
            try
            {
                if (!File.Exists(_applicationPaths.CustomRulesFilePath))
                {
                    // This can happen if the user has not created any custom rules yet.
                    string directory = Path.GetDirectoryName(_applicationPaths.CustomRulesFilePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                    File.WriteAllText(_applicationPaths.CustomRulesFilePath, "");
                }

                var parameters = new Dictionary<string, string>
                {
                    ["path"] = _applicationPaths.CustomRulesFilePath,
                    ["diff"] = diffContent
                };

                var functionInvoker = _functionInvokerFactory();
                if (functionInvoker == null)
                {
                    CoplayLogger.LogError("Failed to execute replace_in_file tool: Function invoker is null");
                    return;
                }

                var result = await functionInvoker.InvokeUnityFunction("replace_in_file", parameters);
                
                if (result != null)
                {
                    CoplayLogger.Log($"Successfully updated .coplayrules.md: {result}");
                }
                else
                {
                    CoplayLogger.LogError("Failed to execute replace_in_file tool");
                }
            }
            catch (Exception ex)
            {
                CoplayLogger.LogError($"Error executing replace_in_file tool: {ex.Message}", ex);
            }
        }
    }
}
