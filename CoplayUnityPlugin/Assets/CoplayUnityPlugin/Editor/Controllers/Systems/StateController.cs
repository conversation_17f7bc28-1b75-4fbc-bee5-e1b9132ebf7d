using Microsoft.Extensions.Logging;
using System;
using System.Reactive.Subjects;
using System.Reactive;
using System.Reactive.Linq;
using UnityEngine;

namespace Coplay.Controllers.Systems
{
    public class StateController
    {
        private readonly ILogger<StateController> _logger;
        private readonly CoplayStateModel _stateModel;
        private const float AITimeoutDuration = 180f; // 3 minutes in seconds
        private float _aiWaitStartTime = -1f;

        public string ThinkingProgress { get; set; } = "Coplay is thinking";

        private readonly Subject<Unit> _cancelThinkingSubject = new();
        public IObservable<Unit> CancelThinkingSignal => _cancelThinkingSubject.AsObservable();

        public StateController(ILogger<StateController> logger, CoplayStateModel stateModel)
        {
            _logger = logger;
            _stateModel = stateModel;
        }

        public IObservable<CoplayState> CurrentStateObservable => _stateModel.CurrentStateObservable;

        public CoplayState CurrentState => _stateModel.CurrentState;

        public void SetState(CoplayState state)
        {
            // Start timer when entering WaitingForAI state
            if (state == CoplayState.WaitingForAI)
            {
                _aiWaitStartTime = Time.realtimeSinceStartup;
                ThinkingProgress = "Coplay is thinking";
            }
            else
            {
                _aiWaitStartTime = -1f;
            }

            _stateModel.SetState(state);
        }

        public void ReturnToPreviousState(string message = null)
        {
            _stateModel.ReturnToPreviousState(message);
        }

        public void ClearState()
        {
            if (_stateModel.CurrentState == CoplayState.WaitingForAI) {
                CancelCurrentRequest();
            }
            _stateModel.ClearState();
        }

        public bool InThinkingStateForTooLong()
        {
            if (_stateModel.CurrentState != CoplayState.WaitingForAI || _aiWaitStartTime < 0)
            {
                return false;
            }

            return (Time.realtimeSinceStartup - _aiWaitStartTime) >= AITimeoutDuration;
        }

        public void HandleThinkingStateTimeout()
        {
            if (_stateModel.CurrentState == CoplayState.WaitingForAI)
            {
                _logger.LogError("AI response timed out after 3 minutes. Returning to previous state.");
                ReturnToPreviousState("AI response timed out");
            }
        }

        public void CancelCurrentRequest()
        {
            // TODO: currently this only cancel in-flight web requests,perhaps we should also reset the state to Initialized?
            //That would require making sure the state of the thread is consistent
            _cancelThinkingSubject.OnNext(Unit.Default);
        }
    }
}
