# To build this on apple silicon, use the following command:
# docker build --platform linux/amd64 -t coplay-backend -f Backend/Dockerfile .
FROM nvidia/cuda:13.0.0-base-ubuntu24.04

WORKDIR /app

# Install minimal runtime dependencies for headless Blender and build tools
RUN apt-get update && apt-get install -y \
    software-properties-common \
    build-essential \
    curl \
    xz-utils \
    ca-certificates \
    libgl1 \
    libglvnd0 \
    libglx0 \
    libegl1 \
    libgles2 \
    libdrm2 \
    libgbm1 \
    libvulkan1 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxext6 \
    libxrender1 \
    libxrandr2 \
    libxi6 \
    libsm6 \
    libxfixes3 \
    libxkbcommon0 \
    xvfb \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Install Blender 4.5.2 explicitly
RUN set -eux; \
    url="https://download.blender.org/release/Blender4.1/blender-4.1.1-linux-x64.tar.xz"; \
    curl -fsSL -o /tmp/blender.tar.xz "$url"; \
    mkdir -p /opt/blender; \
    tar -xJf /tmp/blender.tar.xz -C /opt/blender --strip-components=1; \
    ln -sf /opt/blender/blender /usr/local/bin/blender; \
    rm -f /tmp/blender.tar.xz

RUN python3.11 -m pip install --no-cache-dir poetry cffi

COPY Backend/pyproject.toml Backend/poetry.lock ./
COPY Backend/third_party third_party
COPY Backend/utils utils

RUN poetry lock && \
    poetry install --only main --no-interaction --no-ansi

# Copy the backend files
COPY Backend/coplay coplay
# Copy the templates directory
COPY Backend/templates templates

COPY Backend/main.py Backend/docker-entrypoint.sh ./

RUN chmod +x /app/docker-entrypoint.sh

# Set Python to run in unbuffered mode
ENV PYTHONUNBUFFERED=1

# Headless EGL for Blender with NVIDIA (surfaceless EGL context)
ENV BLENDER_EGL_HEADLESS=1 \
    EGL_PLATFORM=surfaceless \
    MESA_EGL_PLATFORM=surfaceless \
    NVIDIA_DRIVER_CAPABILITIES=compute,utility,graphics \
    __GLX_VENDOR_LIBRARY_NAME=nvidia

EXPOSE 8080

# Use the entrypoint script which runs alembic migrations before starting the app
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["poetry", "run", "granian", "--interface", "asgi", "main:app", "--host", "0.0.0.0", "--port", "8080"]
