using System;
using Coplay.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coplay.Common
{
    public static class CoplayLogger
    {
        private static ILogger _logger;
        private static ILogger Logger => _logger ??= RootScopeServices.Services.GetRequiredService<ILogger<Coplay>>();

        public static void Log(string message)
        {
            Logger.LogInformation(message);
        }

        public static void LogWarning(string message)
        {
            Logger.LogWarning(message);
        }

        public static void LogError(Exception ex)
        {
            Logger.LogError(ex, $"CoplayError - {FormatExceptionMessage(ex)}");
        }

        public static void LogError(string message)
        {
            Logger.LogError($"CoplayError - {message}");
        }

        public static void LogError(string message, Exception ex)
        {
            Logger.LogError(ex, $"CoplayError - {message}\n{FormatExceptionMessage(ex)}");
        }

        private static string FormatExceptionMessage(Exception ex)
        {
            string message = $"{ex.GetType()}: {ex.Message}\n{ex.StackTrace}";
            
            // Include inner exception details if present
            var innerException = ex.InnerException;
            while (innerException != null)
            {
                message += $"\n\nCaused by: {innerException.GetType()}: {innerException.Message}\n{innerException.StackTrace}";
                innerException = innerException.InnerException;
            }
            
            return message;
        }
    }
}
